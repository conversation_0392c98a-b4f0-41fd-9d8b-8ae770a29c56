namespace WSA.Integration.API.V1;

using WSA.Integration;
using Microsoft.Inventory.Ledger;
using Microsoft.Inventory.Journal;
using Microsoft.Inventory.Posting;

codeunit 50126 "WSA Adjustment Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Adjustment Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    begin
        if not TryHandlePost(Request) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log")

    var
        itemJournalLine: Record "Item Journal Line";
        itemLedgEntry: Record "Item Ledger Entry";
        json: JsonObject;
        adjustmentQuantity: Decimal;
        firstLineNo: Integer;
        lastLineNo: Integer;

    begin
        json := Common.GetJsonFromBlob(Request);

        adjustmentQuantity := Common.GetJsonValue(json, '$.quantity').AsDecimal();
        if adjustmentQuantity = 0 then begin
            Common.SetNoContentResponse(Request);
            exit;
        end;

        if not InitItemJournalLine(json, itemJournalLine) then begin
            Common.SetErrorResponse(Request, GetLastErrorText());
            exit
        end;

        SetAdjustmentType(json, itemJournalLine);
        itemJournalLine.Validate("Document No.", Common.GetJsonValue(json, '$.number').AsCode());
        SetProduct(json, itemJournalLine);
        itemJournalLine.Validate("WSA Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
        itemJournalLine.Validate(Quantity, Common.GetJsonValue(json, '$.quantity').AsDecimal());
        SetDates(json, itemJournalLine);
        SetSerialNumber(json, itemJournalLine);
        itemJournalLine.Insert();
        firstLineNo := itemJournalLine."Line No.";

        lastLineNo := itemJournalLine."Line No.";

        itemJournalLine.SetRange("Journal Template Name", itemJournalLine."Journal Template Name");
        itemJournalLine.SetRange("Journal Batch Name", itemJournalLine."Journal Batch Name");
        itemJournalLine.SetFilter("Line No.", '%1..%2', firstLineNo, lastLineNo);

        if PostAdjustment(itemJournalLine, itemLedgEntry) then
            Common.SetCreatedResponse(Request, Common.AdjustmentToJson(itemLedgEntry, false))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure InitItemJournalLine(
        json: JsonObject;
        var ItemJournalLine: Record "Item Journal Line"): Boolean

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        RetailIntegrationSetup.TestField("Item Adjustment Template");
        RetailIntegrationSetup.TestField("Item Adjustment Batch");

        ItemJournalLine.Init();
        ItemJournalLine."Journal Template Name" := RetailIntegrationSetup."Item Adjustment Template";
        ItemJournalLine."Journal Batch Name" := RetailIntegrationSetup."Item Adjustment Batch";
        ItemJournalLine."Line No." := GetLastLineNo(
            RetailIntegrationSetup."Item Adjustment Template",
            RetailIntegrationSetup."Item Adjustment Batch") + 10000;
        exit(true);
    end;


    local procedure GetLastLineNo(
        TemplateCode: Code[10];
        BatchCode: Code[10]): Integer

    var
        ItemJournalLine: Record "Item Journal Line";

    begin
        ItemJournalLine.SetRange("Journal Template Name", TemplateCode);
        ItemJournalLine.SetRange("Journal Batch Name", BatchCode);
        if ItemJournalLine.FindLast() then
            exit(ItemJournalLine."Line No.")
        else
            exit(0);
    end;


    local procedure SetAdjustmentType(
        json: JsonObject;
        var ItemJournalLine: Record "Item Journal Line")

    var
        quantity: Decimal;

    begin
        quantity := Common.GetJsonValue(json, '$.quantity').AsDecimal();
        if quantity > 0 then
            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Positive Adjmt.")
        else
            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
    end;


    local procedure SetDates(
        Json: JsonObject;
        var ItemJournalLine: Record "Item Journal Line")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.adjustmentDate').AsDate();
        if newDate <> 0D then begin
            ItemJournalLine.Validate("Posting Date", newDate);
            ItemJournalLine.Validate("Document Date", newDate);
        end;
    end;


    local procedure SetProduct(
        Json: JsonObject;
        var ItemJournalLine: Record "Item Journal Line")

    begin
        ItemJournalLine.Validate("Item No.", Common.GetJsonValue(json, '$.product.code').AsCode());
    end;


    local procedure SetSerialNumber(
        Json: JsonObject;
        var ItemJournalLine: Record "Item Journal Line")

    var
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.serialNumber');
        if not jValue.IsNull then
            ItemJournalLine.Validate("Serial No.", jValue.AsCode());
    end;


    local procedure PostAdjustment(
        var ItemJournalLine: Record "Item Journal Line";
        var ItemLedgEntry: Record "Item Ledger Entry") Ok: Boolean

    var
        ItemJournalLine2: Record "Item Journal Line";
        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";

    begin
        ClearLastError();
        ItemJournalLine2 := ItemJournalLine;

        Commit;
        if ItemJnlPostBatch.Run(ItemJournalLine) then begin
            ItemLedgEntry.SetRange("Document Type", ItemJournalLine2."Document Type");
            ItemLedgEntry.SetRange("Document No.", ItemJournalLine2."Document No.");
            exit(ItemLedgEntry.FindLast());
        end;
        if ItemJournalLine.Delete(true) then;
        exit(false);
    end;


    var
        Common: Codeunit "WSA Common";
}
