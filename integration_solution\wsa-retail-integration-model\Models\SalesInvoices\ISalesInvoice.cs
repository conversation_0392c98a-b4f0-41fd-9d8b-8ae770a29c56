﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public interface ISalesInvoice :
    ISalesDocument<SalesInvoiceLine>

{
    public new IEnumerable<string> ValidateRequiredProperties()
    {
        var documentErrors = ((ISalesDocument<SalesInvoiceLine>)this).ValidateRequiredProperties();
        var lineErrors = Lines.SelectMany(line => ((ISalesDocumentLine)line).ValidateRequiredProperties());
        return documentErrors.Concat(lineErrors);
    }
}