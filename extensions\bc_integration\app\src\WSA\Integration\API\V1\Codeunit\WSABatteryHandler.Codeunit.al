namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using WSA.Integration;


codeunit 50133 "WSA Battery Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Battery Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        Battery: Record "Battery";

    begin
        if TryHandlePost(Request, Battery) then
            Common.SetCreatedResponse(Request, Common.BatteryToJson(Battery))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        Battery: Record "Battery";

    begin
        if not TryHandleGet(Request, Battery) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Battery: Record "Battery")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Request."Battery Code" := Common.GetJsonValue(json, '$.code').AsCode();
            Commit();
        end;

        if not Battery.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Battery.Init();
            Battery.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Battery.Insert(false);
        end;

        recRef.GetTable(Battery);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Battery.FieldNo(Name), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Battery);
        Battery.Modify(true);

        Request."Battery Code" := Battery.Code;
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Battery: Record "Battery")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Battery.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.BatteryToJson(Battery));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Battery.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Battery.FindFirst() then begin
                Common.SetOkResponse(Request, Common.BatteryToJson(Battery));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Battery.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if Battery.FindFirst() then begin
                Common.SetOkResponse(Request, Common.BatteryToJson(Battery));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
