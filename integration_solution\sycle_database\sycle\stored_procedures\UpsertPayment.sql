﻿CREATE PROCEDURE [sycle].[sp_UpsertPayment]
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO sycle.payment (
        pmt_id, 
        payment_uuid, 
        transaction_no, 
        pmt_amount, 
        pmt_type, 
        pmt_description, 
        pmt_date, 
        hcfa_id, 
        adjusted_pmt_id, 
        last_update, 
        auth_code, 
        stan, 
        qb_edit_sequence, 
        account_number, 
        created_on, 
        check_number, 
        IntegrationRequired, 
        ModifiedOn
    )
    SELECT 
        s.pmt_id, 
        s.payment_uuid, 
        s.transaction_no, 
        s.pmt_amount, 
        s.pmt_type, 
        s.pmt_description, 
        s.pmt_date, 
        s.hcfa_id, 
        s.adjusted_pmt_id, 
        s.last_update, 
        s.auth_code, 
        s.stan, 
        s.qb_edit_sequence, 
        s.account_number, 
        s.created_on, 
        s.check_number, 
        1 AS IntegrationRequired, 
        SYSUTCDATETIME() AS ModifiedOn
    FROM sycle.Payment_staging s
    WHERE NOT EXISTS (
        SELECT 1 FROM sycle.payment p 
        WHERE s.pmt_id = p.pmt_id
    );
    UPDATE p
    SET 
        p.payment_uuid = s.payment_uuid,
        p.transaction_no = s.transaction_no,
        p.pmt_amount = s.pmt_amount,
        p.pmt_type = s.pmt_type,
        p.pmt_description = s.pmt_description,
        p.pmt_date = s.pmt_date,
        p.hcfa_id = s.hcfa_id,
        p.adjusted_pmt_id = s.adjusted_pmt_id,
        p.last_update = s.last_update,
        p.auth_code = s.auth_code,
        p.stan = s.stan,
        p.qb_edit_sequence = s.qb_edit_sequence,
        p.account_number = s.account_number,
        p.check_number = s.check_number,
        p.ModifiedOn = SYSUTCDATETIME(),
        p.IntegrationRequired = 1
    FROM sycle.payment p
    INNER JOIN sycle.Payment_staging s ON p.pmt_id = s.pmt_id
    WHERE (
        p.payment_uuid <> s.payment_uuid OR
        p.transaction_no <> s.transaction_no OR
        p.pmt_amount <> s.pmt_amount OR
        p.pmt_type <> s.pmt_type OR
        ISNULL(p.pmt_description, '') <> ISNULL(s.pmt_description, '') OR
        p.pmt_date <> s.pmt_date OR
        ISNULL(p.hcfa_id, -1) <> ISNULL(s.hcfa_id, -1) OR
        ISNULL(p.adjusted_pmt_id, -1) <> ISNULL(s.adjusted_pmt_id, -1) OR
        p.last_update < s.last_update OR
        ISNULL(p.auth_code, '') <> ISNULL(s.auth_code, '') OR
        ISNULL(p.stan, '') <> ISNULL(s.stan, '') OR
        ISNULL(p.qb_edit_sequence, '') <> ISNULL(s.qb_edit_sequence, '') OR
        ISNULL(p.account_number, '') <> ISNULL(s.account_number, '') OR
        ISNULL(p.check_number, '') <> ISNULL(s.check_number, '')
    );

END;
GO