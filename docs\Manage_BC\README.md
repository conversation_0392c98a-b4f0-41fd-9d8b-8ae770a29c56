# Manage-BC Data Transfer Documentation

This documentation provides comprehensive details about all entities and their properties involved in data transfer between the Manage and Business Central (BC) systems.

## 📋 Table of Contents

1. [Overview](01_Overview.md) - System overview and data flow architecture
2. [Core Interfaces](02_Core_Interfaces.md) - Base interfaces and abstractions
3. [Master Data Entities](03_Master_Data_Entities.md) - Products, Clinics, Patients, etc.
4. [Transaction Entities](04_Transaction_Entities.md) - Orders, Invoices, Receipts, etc.
5. [Entity Configurations](05_Entity_Configurations.md) - Database mappings and constraints
6. [Adapters and Mappings](06_Adapters_And_Mappings.md) - Data transformation logic
7. [Event Processing](07_Event_Processing.md) - Event-driven data processing
8. [Data Flow Diagrams](08_Data_Flow_Diagrams.md) - Visual representation of data flows

## 🎯 Purpose

This documentation serves as a comprehensive reference for:

- **Developers** working on the integration between Manage and BC systems
- **Business Analysts** understanding data structures and constraints
- **QA Engineers** validating data transfer processes
- **System Architects** designing integration patterns

## 🔄 Auto-Update with docxf

This documentation is configured to automatically update when source code changes using [docxf](https://github.com/docxf/docxf). The configuration is defined in `docxf.json`.

### Setup docxf

```bash
# Install docxf globally
npm install -g docxf

# Initialize watching for changes
docxf watch

# Generate documentation manually
docxf generate
```

## 🏗️ System Architecture

The integration follows a layered architecture:

```
┌─────────────────┐    ┌─────────────────┐
│     Manage      │    │ Business Central│
│    System       │    │      (BC)       │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          │    ┌─────────────────┐│
          └────┤  Integration    ├┘
               │     Layer       │
               └─────────────────┘
```

## 📊 Key Entity Categories

### Master Data Entities
- **Products**: Product catalog and inventory items
- **Clinics**: Healthcare facility information
- **Patients**: Customer/patient records
- **Vendors**: Supplier information
- **Categories**: Product categorization

### Transaction Entities
- **Purchase Orders**: Procurement transactions
- **Sales Invoices**: Billing transactions
- **Purchase Receipts**: Goods receipt transactions
- **Sales Credits**: Credit note transactions

### Configuration Entities
- **Category Mappings**: Business logic mappings
- **Inventory Countries**: Geographic configurations
- **Event Processing**: Integration event tracking

## 🔗 Related Documentation

- [Simply Integration Documentation](../../retail-integration-simply/Documentation/)
- [PIM Integration Documentation](../../retail-integration-pim/)
- [Sycle Integration Documentation](../../retail-integration-sycle/)

## 📝 Contributing

When updating this documentation:

1. Ensure all entity properties are documented with their constraints
2. Include validation rules and business logic
3. Update diagrams when data flow changes
4. Test docxf configuration after structural changes

---

*Last updated: 2025-01-15*
*Auto-generated sections will be updated by docxf when source code changes*
