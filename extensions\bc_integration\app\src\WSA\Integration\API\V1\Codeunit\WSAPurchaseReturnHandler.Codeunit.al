namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using Microsoft.Purchases.Document;
using Microsoft.Purchases.Vendor;
using WSA.Integration;


codeunit 50128 "WSA Purchase Return Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Purchase Return Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        purchaseReturn: Record "Purchase Header";

    begin
        if TryHandlePost(Request, purchaseReturn) then
            Common.SetCreatedResponse(Request, Common.PurchaseReturnToJson(purchaseReturn))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        purchaseReturn: Record "Purchase Header";

    begin
        if not TryHandleGet(Request, purchaseReturn) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var PurchaseReturn: Record "Purchase Header")

    var
        purchaseLine: Record "Purchase Line";
        recRef: RecordRef;
        json: JsonObject;
        lines: JsonToken;
        line: JsonToken;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        ValidateExternalReferences(json);

        if not PurchaseReturn.Get(PurchaseReturn."Document Type"::"Return Order", Common.GetJsonValue(json, '$.documentNumber').AsCode()) then begin
            PurchaseReturn.Init();
            PurchaseReturn."Document Type" := PurchaseReturn."Document Type"::"Return Order";
            PurchaseReturn."No." := Common.GetJsonValue(json, '$.documentNumber').AsCode();
            PurchaseReturn.Validate("Buy-from Vendor No.", Common.GetJsonValue(json, '$.vendor.code').AsCode());
            PurchaseReturn.Validate("Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
            PurchaseReturn.Insert(false);
        end;

        if PurchaseReturn.Status <> PurchaseReturn.Status::Open then
            PurchaseReturn.Status := PurchaseReturn.Status::Open;

        recRef.GetTable(PurchaseReturn);
        Common.ValidateFieldFromJson(json, '$.alternateNumber', recRef, PurchaseReturn.FieldNo("Vendor Order No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseReturn.FieldNo("Posting Date"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseReturn.FieldNo("Document Date"), TempFieldSet);
        recRef.SetTable(PurchaseReturn);
        PurchaseReturn.Modify();

        if json.SelectToken('$.purchaseReturnLines', lines) then begin
            if lines.IsArray then begin
                foreach line in lines.AsArray() do begin
                    if not purchaseLine.Get(PurchaseReturn."Document Type", PurchaseReturn."No.",
                            Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger()) then begin
                        purchaseLine.Init();
                        purchaseLine."Document Type" := PurchaseReturn."Document Type";
                        purchaseLine."Document No." := PurchaseReturn."No.";
                        purchaseLine."Line No." := Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger();
                        purchaseLine.Insert();
                    end;

                    if (purchaseLine.Type <> purchaseLine.Type::Item) then
                        purchaseLine.Validate(Type, purchaseLine.Type::Item);
                    recRef.GetTable(purchaseLine);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.product.code', recRef, purchaseLine.FieldNo("No."), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.description', recRef, purchaseLine.FieldNo(Description), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.quantity', recRef, purchaseLine.FieldNo(Quantity), TempFieldSet);
                    recRef.SetTable(purchaseLine);

                    purchaseLine.Validate("Qty. to Receive", 0);
                    purchaseLine.Validate("Qty. to Invoice", 0);
                    purchaseLine.Modify();
                end;
            end;
        end;

        PurchaseReturn.Status := PurchaseReturn.Status::Released;
        PurchaseReturn.Modify();

        Request."Document No." := PurchaseReturn."No.";
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var PurchaseReturn: Record "Purchase Header")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if PurchaseReturn.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Request."Purchase Order No." := PurchaseReturn."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PurchaseReturnToJson(PurchaseReturn));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.documentNumber').IsNull) then begin
            PurchaseReturn.SetRange("Document Type", PurchaseReturn."Document Type"::"Return Order");
            PurchaseReturn.SetRange("No.", Common.GetJsonValue(json, '$.documentNumber').AsCode());
            if PurchaseReturn.FindFirst() then begin
                Request."Purchase Order No." := PurchaseReturn."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PurchaseReturnToJson(PurchaseReturn));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;

    local procedure ValidateExternalReferences(json: JsonObject)
    begin
        ValidateVendor(json);
        ValidateProducts(json);
    end;


    local procedure ValidateVendor(json: JsonObject): Boolean
    var
        Vendor: Record Vendor;
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.vendor.code');
        if jValue.IsNull() then
            exit(false);

        if not Vendor.Get(jValue.AsCode()) then
            exit(false);
    end;


    local procedure ValidateProducts(json: JsonObject): Boolean
    var
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        Json.SelectToken('$.purchaseReturnLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            ValidateProduct(lineToken.AsObject());
        end;
    end;


    local procedure ValidateProduct(json: JsonObject): Boolean
    var
        Item: Record Item;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.product.code');
        if jValue.IsNull() then
            exit(false);

        if not Item.Get(jValue.AsCode()) then
            exit(IntegrationManagement.GetProduct(jValue.AsCode(), Item));
    end;



    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
