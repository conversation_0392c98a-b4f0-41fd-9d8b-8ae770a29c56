﻿using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Categories;

public class CategoryTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly CategoryService _categoryService;

    public CategoryTests()
    {
        _serviceFactory = new ServiceFactory();
        _categoryService = _serviceFactory.CreateCategoryService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnClinic()
    {
        // Act
        var result = await _categoryService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "1110");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("38AD1695-B7CE-4355-AC4F-9D14A2220BB2"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnClinic()
    {
        // Act
        var result = await _categoryService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-1110");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("38AD1695-B7CE-4355-AC4F-9D14A2220BB2"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _categoryService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "1110");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("38AD1695-B7CE-4355-AC4F-9D14A2220BB2"), result.Id);
    }
}

