using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Simply.Models.PurchaseReceipts;

public class SimplyPurchaseReceiptRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPurchaseReceiptRepository> logger,
    SimplyContext simplyContext)
    : ISimplyPurchaseReceiptRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPurchaseReceiptRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<IGrouping<(
        string OrderType, 
        int DocNumber, 
        DateTime TransactionDate), SimplyPurchaseReceiptEntity>>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplyPurchaseReceiptEntity
                .Where(x => x.IntegrationRequired == true)
                .Where(x => x.OrderType != null)
                .Where(x => x.DocNumber != null)
                .Where(x => x.TransactionDate != null)
                .ToListAsync();

            var groupedList = list
                .GroupBy(x => ((string)x.OrderType!, (int)x.DocNumber!, (DateTime)x.TransactionDate!))
                .ToList();

            return groupedList ?? [];
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplyPurchaseReceiptEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}