﻿CREATE TABLE [bc].[PurchaseOrder](
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[CompanyId] UNIQUEIDENTIFIER,
	[ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
	[ExternalId] UNIQUEIDENTIFIER NOT NULL,
	[DocumentNumber] NVARCHAR(20),
	[DocumentDate] DATETIME2(7),
	[PostingDate] DATETIME2(7),
	[VendorId] UNIQUEIDENTIFIER,
	[ExternalVendorId] UNIQUEIDENTIFIER, 
	[BuyFromVendorId] UNIQUEIDENTIFIER,
	[ExternalBuyFromVendorId] UNIQUEIDENTIFIER,
	[ResponsibilityCenterId] UNIQUEIDENTIFIER,
	[ExternalResponsibilityCenterId] UNIQUEIDENTIFIER,
	[CurrencyId] UNIQUEIDENTIFIER,
	[ExternalCurrencyId] UNIQUEIDENTIFIER,
	[PaymentTermsId] UNIQUEIDENTIFIER,
	[ExternalPaymentTermsId] UNIQUEIDENTIFIER,
	[DimensionSetId] UNIQUEIDENTIFIER,
	[ExternalDimensionSetNumber] INT,
	[ExternalCreatedOn] DATETIME2(7),
	[ExternalModifiedOn] DATETIME2(7),
	[CreatedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ModifiedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	CONSTRAINT [PK_PurchaseOrder_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
	)
GO

CREATE UNIQUE INDEX [IX_PurchaseOrder_CompanyId_ExternalId]
ON [bc].[PurchaseOrder] ([CompanyId], [ExternalId])
GO

CREATE INDEX [IX_PurchaseOrder_PostingDate_DocumentNumber]
ON [bc].[PurchaseOrder] ([PostingDate], [DocumentNumber])
GO