﻿using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.Payors;

public class PayorEventHubEvent
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }
    public string? Code { get; set; }
    public string? Type { get; set; }
    public bool? IsActive { get; set; }
    public AddressEventHubDto? Address { get; set; }

    public string? PayorCode
    {
        get
        {
            if (Code != null)
            {
                return Common.GetCode(Code, "PAY");
            }
            return null;
        }
    }
}