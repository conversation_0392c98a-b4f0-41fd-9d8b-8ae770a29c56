﻿CREATE TABLE [bc].[Customer_Staging] (
    [Id] UNIQUEIDENTIFIER NOT NULL,
    [CompanyId] UNIQUEIDENTIFIER,
    [CustomerNumber] NVARCHAR(20),
    [Name] NVARCHAR(100),
    [Name2] NVARCHAR(100),
    [Address] NVARCHAR(100),
    [Address2] NVARCHAR(100),
    [City] NVARCHAR(50),
    [PostCode] NVARCHAR(20),
    [Region] NVARCHAR(30),
    [CountryRegionCode] NVARCHAR(10),
    [PhoneNumber] NVARCHAR(30),
    [Email] NVARCHAR(80),
    [CustomerType] NVARCHAR(20),
    [Blocked] NVARCHAR(20),
    [SystemCreatedAt] DATETIME2(7),
    [SystemModifiedAt] DATETIME2(7),
    [ETL_LoadId] NVARCHAR(40),
    [ETL_CreatedAt] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
    [ETL_Status] NVARCHAR(40)
);
GO

CREATE INDEX [IX_CustomerStaging_ETLLoadId_CompanyId_Id]
ON [bc].[Customer_Staging] ([ETL_LoadId], [CompanyId], [Id]);
GO