{"version": "0.2.0", "configurations": [{"name": "DK DEV", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "dk_dev", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "DK ATTACH", "request": "attach", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "dk_dev", "breakOnError": "ExcludeTry", "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "breakOnNext": "WebServiceClient", "userId": "b843c21a-f518-4217-a5b2-fb717326693d"}, {"name": "FR DEV", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_dev", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "FR UAT", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_uat", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "FR TEST", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_test", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "FR ATTACH", "request": "attach", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_dev", "breakOnError": "ExcludeTry", "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "breakOnNext": "WebServiceClient", "userId": "b843c21a-f518-4217-a5b2-fb717326693d"}, {"name": "FR SNAP", "request": "snapshotInitialize", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Production", "environmentName": "fr_prod", "breakOnNext": "WebClient", "executionContext": "DebugAndProfile", "userId": "95d00264-0d8a-4406-b894-1db76883cb5e"}, {"name": "W1 Sandbox", "request": "launch", "type": "al", "environmentType": "OnPrem", "server": "http://w1sandbox", "serverInstance": "BC", "authentication": "UserPassword", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "tenant": "default", "usePublicURLFromServer": true}, {"name": "W1 ATTACH", "request": "attach", "type": "al", "environmentType": "OnPrem", "server": "http://w1sandbox", "serverInstance": "BC", "authentication": "UserPassword", "breakOnError": "ExcludeTry", "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "tenant": "default", "breakOnNext": "WebServiceClient"}]}