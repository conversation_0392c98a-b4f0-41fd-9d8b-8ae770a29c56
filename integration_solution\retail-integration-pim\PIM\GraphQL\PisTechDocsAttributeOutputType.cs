﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisTechDocsAttributeOutputType
{
    [JsonPropertyName("assetName")] public string? AssetName { get; set; }

    [JsonPropertyName("cdnUrl")] public string? CdnUrl { get; set; }

    [JsonPropertyName("docSize")] public int? DocSize { get; set; }

    [JsonPropertyName("language")] public string? Language { get; set; }

    [JsonPropertyName("techDocType")] public string? TechDocType { get; set; }
}