# Patient Integration Sequence Diagram

This sequence diagram illustrates the process of integrating patient data from the Simply system.

```mermaid
sequenceDiagram
    participant Timer
    participant EventProcessor as FromSimplyEventProcessor
    participant PatientHandler
    participant Repository as SimplyCustomerRepository
    participant DB as Simply Database
    participant PatientService as External Patient Service
    participant EntityService as External Entity Service
    
    Timer->>EventProcessor: FromSimplyTimerEventAsync(myTimer)
    EventProcessor->>PatientHandler: ProcessNewRecordsAsync()
    
    PatientHandler->>Repository: GetListAsync()
    Repository->>DB: Query IntegrationRequired=true
    DB-->>Repository: Return SimplyCustomerEntity list
    Repository-->>PatientHandler: Return SimplyCustomerEntity list
    
    alt Has records to process
        PatientHandler->>PatientHandler: Create patient models from entities
        PatientHandler->>EntityService: GetAsync(ExternalSystemCode, EntityType.Patient)
        EntityService-->>PatientHandler: Return EntitySubscriber
        
        alt FromExternalSystem is true
            loop For each patient
                PatientHandler->>PatientHandler: ProcessPatientAsync(patient)
                PatientHandler->>PatientService: UpsertAsync(patient)
                PatientService-->>PatientHandler: Return result
                
                alt Upsert successful
                    PatientHandler->>Repository: UpdateIsIntegratedAsync(id)
                    Repository->>DB: Update IntegrationRequired=false
                    DB-->>Repository: Return success
                    Repository-->>PatientHandler: Return success
                else Upsert failed
                    PatientHandler->>PatientHandler: Log error
                end
            end
        end
        
        PatientHandler-->>EventProcessor: Return integration result
    else No records to process
        PatientHandler-->>EventProcessor: Log "No new patients" and return
    end
    
    EventProcessor->>EventProcessor: Log completion
```

This sequence diagram shows the patient integration process:

1. A timer triggers the `FromSimplyEventProcessor`
2. The processor calls `ProcessNewRecordsAsync()` on the `PatientHandler`
3. The handler fetches customer records from the Simply database through the repository
4. For each customer, it:
   - Creates a patient model
   - Validates the patient data
   - Sends the patient to the external system through the Patient Service
   - Updates the integration status on success
5. The process completes and logs the results

This implementation follows a clear separation of concerns pattern, with the repository handling data access, the handler managing business logic, and the service layer handling external communication.