﻿CREATE TABLE [bc].[SalesInvoice_Staging]
(
	[Id] UNIQUEIDENTIFIER NOT NULL,
	[CompanyId] UNIQUEIDENTIFIER,
	[DocumentNumber] NVARCHAR(20),
	[ExternalDocumentNumber] NVARCHAR(35),
	[DocumentDate] DATETIME2(7),
	[PostingDate] DATETIME2(7),
	[CustomerNumber] NVARCHAR(20),
	[CustomerId] UNIQUEIDENTIFIER,
	[SellToCustomerNumber] NVARCHAR(20),
	[SellToCustomerId] UNIQUEIDENTIFIER,
	[ResponsibilityCenter] NVARCHAR(20),
	[ResponsibilityCenterId] UNIQUEIDENTIFIER,
	[CurrencyCode] NVARCHAR(20),
	[CurrencyId] UNIQUEIDENTIFIER,
	[PaymentTermsCode] NVARCHAR(20),
	[PaymentTermsId] UNIQUEIDENTIFIER,
	[DimensionSetId] INT,
	[AmountExcludingTax] DECIMAL(18, 4),
	[AmountIncludingTax] DECIMAL(18, 4),
	[SystemCreatedAt] DATETIME2(7),
	[SystemModifiedAt] DATETIME2(7),
	[ETL_LoadId] NVARCHAR(40),
	[ETL_CreatedAt] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ETL_Status] NVARCHAR(40)
)
GO

CREATE INDEX [IX_SalesInvoiceStaging_ETLLoadId_CompanyId_Id]
ON [bc].[SalesInvoice_Staging] ([ETL_LoadId], [CompanyId], [Id])
GO