using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.ProductModels
{
    public class ProductModelEntityConfiguration : IEntityTypeConfiguration<ProductModelEntity>
    {
        public void Configure(EntityTypeBuilder<ProductModelEntity> builder)
        {
            builder.ToTable("ProductModel", "dbo");

            builder.<PERSON><PERSON><PERSON>(m => m.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();
        }
    }
}
