using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Colors
{
    public class PimColorConfiguration : IEntityTypeConfiguration<PimColorEntity>
    {
        public void Configure(EntityTypeBuilder<PimColorEntity> builder)
        {
            builder.ToTable("Color", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureCodeIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.HexCode)
                .HasColumnName("HexCode")
                .HasColumnType("nvarchar(20)");
        }
    }
}