﻿using Microsoft.Extensions.Options;
using Moq;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.SalesInvoices;

public class SalesInvoiceTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly SalesInvoiceService _salesInvoiceService;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;

    private readonly IEnumerable<ClinicEntity> _clinics;
    private readonly IEnumerable<PatientEntity> _patients;
    private readonly IEnumerable<ProductEntity> _products;

    public SalesInvoiceTests()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());

        _serviceFactory = new ServiceFactory();
        _salesInvoiceService = _serviceFactory.CreateSalesInvoiceService();

        _clinics = TestDataLoader.Clinics();
        _patients = TestDataLoader.Patients();
        _products = TestDataLoader.Products();
    }

    [Fact]
    public async Task UpsertAsync_InsertShouldReturnSalesInvoice()
    {
        // Arange
        var salesInvoice = new SalesInvoice()
        {
            ExternalSystemCode = _optionsMock.Object.Value.ExternalSystemCode,
            DocumentNumber = "TESTSINV1",
            ExternalReference = "EXT-TESTSINV1",
            Patient = new() { Code = _patients.First().Code },
            Clinic = new() { Code = _clinics.First().Code },
            DocumentDate = DateTime.Now.Date
        };
        salesInvoice.Lines.Add(new SalesInvoiceLine()
        {
            Sequence = 1,
            ExternalReference = "EXT-TESTSINV1-1",
            Product = new() { Code = _products.First().Code },
            Quantity = 1,
            SerialNumber = "SN-TESTINV1-1",
            UnitPrice = 100.00m,
            GrossAmount = 100.00m,
            DiscountAmount = 0.00m,
            AmountExclTax = 100.00m,
            TaxAmount = 0.00m,
            AmountInclTax = 100.00m
        });

        // Act
        var result = await _salesInvoiceService.UpsertAsync(salesInvoice);

        // Assert result exists
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);

        // Assert the SINV really exists in the db
        var getResult = await _salesInvoiceService.GetAsync(
            externalSystemCode: _optionsMock.Object.Value.ExternalSystemCode,
            documentNumber: "TESTSINV1");

        Assert.NotNull(getResult);
        Assert.Equal(result.Id, getResult.Id);

        // Assert event was raised
        _serviceFactory.EventGridPublisherMock.Verify(p => p.RaiseEventAsync(
            It.IsAny<AppSettings>(),
            It.Is<string>(s => s == EntityType.SalesInvoice.GetEventName()),
            It.IsAny<string>(),
            It.IsAny<SalesInvoice>()),
            Times.Once);
    }
}