using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Images
{
    public class PimImageConfiguration : IEntityTypeConfiguration<PimImageEntity>
    {
        public void Configure(EntityTypeBuilder<PimImageEntity> builder)
        {
            builder.ToTable("Image", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.BrandId)
                .HasColumnName("BrandId")
                .HasColumnType("uniqueidentifier");

            builder.Property(x => x.CDNUrl)
                .HasColumnName("CDNUrl")
                .HasColumnType("nvarchar(250)");

            builder.Property(x => x.IsDefault)
                .HasColumnName("IsDefault")
                .HasColumnType("bit");
        }
    }
}