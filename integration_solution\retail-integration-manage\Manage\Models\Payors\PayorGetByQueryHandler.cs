﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Payors;

namespace WSA.Retail.Integration.Manage.Models.Payors;

public class PayorGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IPayorService entityService)
    : BaseApiGetByQuery<
        Payor,
        IPayorService>(appSettings, entityService)
{
}