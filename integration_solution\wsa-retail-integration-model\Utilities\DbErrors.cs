﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data.Common;

namespace WSA.Retail.Integration.Utilities;

public static class DbErrors
{
    public static bool IsUniqueConstraintViolation(DbUpdateException ex)
    {
        if (ex.InnerException is SqlException sqlEx)
        {
            return sqlEx.Number == 2627 || sqlEx.Number == 2601;
        }

        // If you're using Azure SQL and EF Core 7+, you might also check for the base DbException:
        if (ex.InnerException is DbException dbEx)
        {
            // Cross-database detection, not just SQL Server
            return dbEx.ErrorCode == 2627 || dbEx.ErrorCode == 2601;
        }

        return false;
    }
}
