﻿using GraphQL;
using GraphQL.Client.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.PIM.GraphQL;
using WSA.Retail.Integration.PIM.Models.Categories;


namespace WSA.Retail.Integration.PIM.Queries;

public class CategoryQueryService(
    IOptions<AppSettings> appSettings,
    ILogger<CategoryQueryService> logger,
    ITokenService tokenService,
    GraphQLHttpClient graphQLClient,
    IPimCategoryRepository pimCategoryRepository) : 
    LoggingBase<GraphQLCategoryQueryData>(logger), 
    ICategoryQueryService

{
    public readonly AppSettings _appSettings = appSettings.Value;
    public readonly ITokenService _tokenService = tokenService;
    private readonly GraphQLHttpClient _graphQLClient = graphQLClient;
    private readonly IPimCategoryRepository _pimCategoryRepository = pimCategoryRepository;


    public async Task UpdateCategoriesFromPIMAsync(string countryCode, string brandCode)
    {
        LogMethodStart();

        var token = await _tokenService.GetTokenAsync();
        _graphQLClient.HttpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", token);

        try
        {
            var response = await SendQueryAsync(countryCode, brandCode);
            ArgumentNullException.ThrowIfNull(response);
            var categories = GetCategoriesFromQuery(response);
            await UpsertCategoriesAsync(categories);
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
        }
    }

    public async Task<GraphQLCategoryQueryData?> SendQueryAsync(string countryCode, string brandCode)
    {
        LogMethodStart();

        try
        {
            var response = await _graphQLClient.SendQueryAsync<GraphQLCategoryQueryData>(GetQueryRequest(countryCode, brandCode));
            return response.Data;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    private GraphQLRequest GetQueryRequest(string countryCode, string brandCode)
    {
        LogMethodStart();

        var request = new GraphQLRequest()
        { 
            Query = @"
                query ($marketFilter: MarketFilterInputType, $languageFilter: LanguageFilterInputType!)
                {
                    category
                    {
                        getCategoryHierarchyByMarket(marketFilter: $marketFilter, languageFilter: $languageFilter)
                        {
                            categories
                            {
                                categoryId,
                                categoryName,
                                coreBrandId,
                                isLeaf,
                                isVisible,
                                parentCategoryId,
                                ranking,
                                state,
                                supportedBrandIds
                            }
                        }
                    }
                }",
            Variables = new
            {
                marketFilter = new
                {
                    brandCode,
                    countryCode
                },
                languageFilter = new
                {
                    languageCode = "en_GB"
                }
            }
        };

        return request;
    }

    private List<GraphQLCategory> GetCategoriesFromQuery(GraphQLCategoryQueryData queryData)
    {
        LogMethodStart();

        List<GraphQLCategory> Categories = [];

        var rootCategories = queryData.Category.GetCategoryHierarchyByMarket;
        if (rootCategories?.Count > 0)
        {
            foreach(var rootCategory in rootCategories) 
            { 
                if (rootCategory != null)
                {
                    var categories = rootCategory.Categories;
                    if (categories != null)
                    {
                        foreach(var category in categories)
                        {
                            if (category != null) 
                            { 
                                Categories.Add(category);
                            }
                        }
                    }
                }
            }
        }

        return Categories;
    }

    public async Task UpsertCategoriesAsync(List<GraphQLCategory> categories)
    {
        LogMethodStart();

        var tasks = categories
            .Where(category => category.CategoryId != Guid.Empty)
            .Select(async category => await _pimCategoryRepository.UpsertAsync(category))
            .ToList();

        await Task.WhenAll(tasks);
    }
}