﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Companies
{
    public class CompanyEntityConfiguration : IEntityTypeConfiguration<CompanyEntity>
    {
        public void Configure(EntityTypeBuilder<CompanyEntity> builder)
        {
            builder.ToTable("Company");
            builder.HasKey(x => x.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAddressFields();
            builder.ConfigureAuditInfoFields();
        }
    }
}