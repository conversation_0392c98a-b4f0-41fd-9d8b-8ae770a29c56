﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Models.Patients;
using WSA.Retail.Integration.Simply.Models.PurchaseOrders;
using WSA.Retail.Integration.Simply.Models.PurchaseReceipts;
using WSA.Retail.Integration.Simply.Models.SalesInvoices;
using WSA.Retail.Integration.Simply.Models.Payments;

namespace WSA.Retail.Integration.Simply.Data
{
    public partial class SimplyContext(DbContextOptions<SimplyContext> options) : DbContext(options)
    {
        public DbSet<SimplyCustomerEntity> SimplyCustomerEntity { get; set; }
        public DbSet<SimplySalesHeaderEntity> SimplySalesHeaderEntity { get; set; }
        public DbSet<SimplySalesDetailEntity> SimplySalesDetailEntity { get; set; }
        public DbSet<SimplySalesFunderEntity> SimplySalesFunderEntity { get; set; }
        public DbSet<SimplyPOHeaderEntity> SimplyPOHeaderEntity { get; set; }
        public DbSet<SimplyPODetailsEntity> SimplyPODetailsEntity { get; set; }
        public DbSet<SimplyPurchaseReceiptEntity> SimplyPurchaseReceiptEntity { get; set; }
        public DbSet<SimplyPaymentEntity> SimplyPaymentEntity { get; set; }
        public DbSet<SimplyCashReceiptsEntity> SimplyCashReceiptsEntity { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("simply");
            
            // Apply configurations
            modelBuilder.ApplyConfiguration(new SimplyCustomerConfiguration());
            modelBuilder.ApplyConfiguration(new SimplySalesHeaderConfiguration());
            modelBuilder.ApplyConfiguration(new SimplySalesDetailConfiguration());
            modelBuilder.ApplyConfiguration(new SimplySalesFunderConfiguration());
            modelBuilder.ApplyConfiguration(new SimplyPOHeaderConfiguration());
            modelBuilder.ApplyConfiguration(new SimplyPODetailsConfiguration());
            modelBuilder.ApplyConfiguration(new SimplyPurchaseReceiptConfiguration());
            modelBuilder.ApplyConfiguration(new SimplyPaymentConfiguration());
            modelBuilder.ApplyConfiguration(new SimplyCashReceiptsConfiguration());
            base.OnModelCreating(modelBuilder);
        }
    }
}