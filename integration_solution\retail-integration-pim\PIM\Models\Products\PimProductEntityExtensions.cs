﻿namespace WSA.Retail.Integration.PIM.Models.Products;

public static class PimProductEntityExtensions
{
    public static string? GetAttributeValue(this PimProductEntity product, string attributeName)
    {
        if (product.Attributes == null)
        {
            return null;
        }
        var attribute = product.Attributes
            .Where(pa => pa.PimAttributeEntity != null)
            .Where(pa => pa.PimAttributeEntity!.Code == attributeName)
            .FirstOrDefault();
        return attribute?.Value;
    }
}
