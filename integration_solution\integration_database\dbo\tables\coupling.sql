﻿CREATE TABLE [dbo].[Coupling]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Coupling_Id] DEFAULT NEWID() NOT NULL,
    [ExternalSystemId]      UNIQUEIDENTIFIER NOT NULL,
    [EntityId]              UNIQUEIDENTIFIER NOT NULL,
    [RecordId]              UNIQUEIDENTIFIER NOT NULL,
    [ExternalRecordId]      NVARCHAR(100),
    [Status]                INT CONSTRAINT [DF_Coupling_Status] DEFAULT 0 NULL,
    [ErrorDetails]          NVARCHAR(MAX) NULL,
    [IntegrationDate]       DATETIME2(7) NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_Coupling_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_Coupling_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Coupling_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_Coupling_ExternalSystem] FOREIGN KEY (ExternalSystemId) REFERENCES [dbo].[ExternalSystem](Id),
    CONSTRAINT              [FK_Coupling_Entity] FOREIGN KEY (EntityId) REFERENCES [dbo].[Entity](Id)
)
GO

CREATE INDEX IX_Coupling_ExternalSystemId_RecordId
ON [dbo].[Coupling] ([ExternalSystemId], [RecordId])
GO

CREATE INDEX IX_Coupling_Status_ExternalSystemId_RecordId
ON [dbo].[Coupling] ([Status], [ExternalSystemId], [RecordId])
GO

CREATE INDEX IX_Coupling_ExternalSystemId_ExternalRecordId
ON [dbo].[Coupling] ([ExternalSystemId], [ExternalRecordId])
GO