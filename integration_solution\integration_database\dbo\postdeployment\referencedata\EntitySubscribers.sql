﻿INSERT INTO dbo.EntitySubscriber (
       EntityId,
       ExternalSystemId,
       FromExternalSystem,
       ToExternalSystem)

SELECT Entity.Id AS EntityId,
       ExternalSystem.Id AS ExternalSystemId,
       1 AS FromExternalSystem,
       1 AS ToExternalSystem

  FROM dbo.Entity,

       dbo.ExternalSystem

 WHERE NOT EXISTS (SELECT * FROM EntitySubscriber 
                    WHERE EntityId = Entity.Id 
                      AND ExternalSystemId = ExternalSystem.Id)