﻿using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Batteries;

public class BatteryTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly BatteryService _batteryService;

    public BatteryTests()
    {
        _serviceFactory = new ServiceFactory();
        _batteryService = _serviceFactory.CreateBatteryService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnClinic()
    {
        // Act
        var result = await _batteryService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "312");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("E12BBD09-A410-4D2E-80A8-AA1DD97F5041"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnClinic()
    {
        // Act
        var result = await _batteryService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-312");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("E12BBD09-A410-4D2E-80A8-AA1DD97F5041"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _batteryService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "312");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("E12BBD09-A410-4D2E-80A8-AA1DD97F5041"), result.Id);
    }
}

