{"dependencies": {"appInsights1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/retail-integration-pim-we-dev", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}, "storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/wsawedatabizcentral9942", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}}}