﻿using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Core;

public interface IDocumentQueryBuilder<TModel, TLineModel, TEntity, TLineEntity>
    where TModel : IDocument<TLineModel>
    where TLineModel : IDocumentLine
    where TEntity : IDocumentEntity<TLineEntity>
    where TLineEntity : IDocumentLineEntity
{
    IQueryable<TModel> BuildQuery(
        IntegrationContext context,
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null);
}