﻿using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Models.PurchaseOrders;

namespace WSA.Retail.Integration.Manage.Models.PurchaseOrders;

public static class PurchaseOrderMapping
{
    public static PurchaseOrder ToPurchaseOrder(this PurchaseOrderEventHubEvent purchaseOrderEventHubEvent)
    {
        return new PurchaseOrder
        {
            ExternalReference = purchaseOrderEventHubEvent.Id.ToString(),
            Id = purchaseOrderEventHubEvent.Id,
            DocumentNumber = purchaseOrderEventHubEvent.Number,
            Lines = [.. purchaseOrderEventHubEvent.LineItems.Select(x => x.ToPurchaseOrderLine())]
        };
    }

    public static PurchaseOrderLine ToPurchaseOrderLine(this PurchaseOrderLineItemEventHubEvent purchaseOrderLineItemEventHubEvent)
    {
        return new PurchaseOrderLine
        {
            ExternalReference = purchaseOrderLineItemEventHubEvent.Id.ToString(),
            Product = new()
            {
                ExternalCode = purchaseOrderLineItemEventHubEvent.ProductId.ToString()
            }
        };
    }

    public static PurchaseOrder ToPurchaseOrder(
        this OrderResponse orderResponse,
        string externalSystemCode)
    {
        return new PurchaseOrder
        {
            ExternalSystemCode = externalSystemCode,
            ExternalReference = orderResponse.Id.ToString(),
            DocumentNumber = orderResponse.Number,
            AlternateNumber = orderResponse.ExternalNumber,
            Clinic = new()
            {
                ExternalCode = orderResponse.Location.Id.ToString()
            },
            Vendor = new()
            {
                ExternalCode = orderResponse.Supplier.Id.ToString()
            },
            Lines = [
                .. orderResponse.LineItems.Select((x, i) =>
                {
                    var line = x.ToPurchaseOrderLine();
                    line.Sequence = i + 1;
                    return line;
                })
            ]
        };
    }

    public static PurchaseOrderLine ToPurchaseOrderLine(this OrderLineItemResponse orderLineItemResponse)
    {
        var line = new PurchaseOrderLine
        {
            ExternalReference = orderLineItemResponse.Id.ToString(),
            Product = new()
            {
                ExternalCode = orderLineItemResponse.Product.Id.ToString()
            },
            Quantity = orderLineItemResponse.Quantity
        };
        if (!string.IsNullOrWhiteSpace(orderLineItemResponse.Sku))
        {
            line.Product.Code = orderLineItemResponse.Sku;
            line.Product.ExternalCode = null;
        }
        return line;
    }
}