﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Data;

public static class EntityConfigurationExtensions
{
    public static EntityTypeBuilder<T> ConfigureMasterDataFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IMasterDataEntity
    {
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.Code)
            .HasColumnName("Code")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(100)");

        builder.Property(x => x.AlternateCode)
            .HasColumnName("AlternateCode")
            .HasColumnType("nvarchar(50)");

        return builder;
    }

    public static EntityTypeBuilder<TDocument> ConfigureSalesDocumentFields<TDocument, TLineEntity>(
        this EntityTypeBuilder<TDocument> builder)
        where TDocument : class, ISalesDocumentEntity<TLineEntity>
        where TLineEntity : class, ISalesDocumentLineEntity
    {
        builder.ConfigureDocumentFields<TDocument, TLineEntity>();
        builder.ConfigurePatientAssociatedFields();
        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureSalesDocumentLineFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, ISalesDocumentLineEntity
    {
        builder.ConfigureDocumentLineFields();
        builder.ConfigureProductAssociatedFields();
        builder.ConfigureDescribableFields();
        builder.ConfigureQuantifiableFields();
        builder.ConfigureSerializableFields();
        builder.ConfigurePricedFields();
        return builder;
    }

    public static EntityTypeBuilder<TDocument> ConfigureDocumentFields<TDocument, TLineEntity>(
        this EntityTypeBuilder<TDocument> builder)
        where TDocument : class, IDocumentEntity<TLineEntity>
        where TLineEntity : class, IDocumentLineEntity
    {
        builder.ConfigureIdentifiableFields();
        builder.ConfigureDocumentNumberedFields();
        builder.ConfigureDocumentDatedFields();
        builder.ConfigureClinicAssociatedFields();
        builder.ConfigureAuditInfoFields();
        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureDocumentLineFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IDocumentLineEntity
    {
        builder.ConfigureIdentifiableFields();
        builder.ConfigureSequenceFields();
        builder.ConfigureAuditInfoFields();
        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureIdentifiableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IIdentifiable
    {
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureDocumentNumberedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IDocumentNumberedEntity
    {
        builder.Property(x => x.DocumentNumber)
            .HasColumnName("DocumentNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.AlternateNumber)
            .HasColumnName("AlternateNumber")
            .HasColumnType("nvarchar(50)");

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureDocumentDatedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IDocumentDatedEntity
    {
        builder.Property(x => x.DocumentDate)
            .HasColumnName("DocumentDate")
            .HasColumnType("datetime2(7)");

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureClinicAssociatedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IClinicAssociatedEntity
    {
        builder.Property(x => x.ClinicId)
            .HasColumnName("ClinicId")
            .HasColumnType("uniqueidentifier");

        builder.HasOne(po => po.ClinicEntity)
            .WithMany()
            .HasForeignKey(po => po.ClinicId);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigurePatientAssociatedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IPatientAssociatedEntity
    {
        builder.Property(x => x.PatientId)
            .HasColumnName("PatientId")
            .HasColumnType("uniqueidentifier");

        builder.HasOne(po => po.PatientEntity)
            .WithMany()
            .HasForeignKey(po => po.PatientId);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureProductAssociatedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IProductAssociatedEntity
    {
        builder.Property(x => x.ProductId)
            .HasColumnName("ProductId")
            .HasColumnType("uniqueidentifier");

        builder.HasOne(po => po.ProductEntity)
            .WithMany()
            .HasForeignKey(po => po.ProductId);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureSequenceFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, ISequencedEntity
    {
        builder.Property(b => b.Sequence)
            .HasColumnName("Sequence")
            .HasColumnType("int")
            .IsRequired();

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureDescribableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IDescribableEntity
    {
        builder.Property(b => b.Description)
            .HasColumnName("Description")
            .HasColumnType("nvarchar(100)")
            .HasMaxLength(100);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureQuantifiableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IQuantifiableEntity
    {
        builder.Property(b => b.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("money");

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigureSerializableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, ISerializableEntity
    {
        builder.Property(b => b.SerialNumber)
            .HasColumnName("SerialNumber")
            .HasColumnType("nvarchar(30)")
            .HasMaxLength(30);

        return builder;
    }

    private static EntityTypeBuilder<T> ConfigurePricedFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IPricedEntity
    {
        builder.Property(b => b.UnitPrice)
            .HasColumnName("UnitPrice")
            .HasColumnType("money");

        builder.Property(b => b.GrossAmount)
            .HasColumnName("GrossAmount")
            .HasColumnType("money");

        builder.Property(b => b.DiscountAmount)
            .HasColumnName("DiscountAmount")
            .HasColumnType("money");

        builder.Property(b => b.AmountExclTax)
            .HasColumnName("AmountExclTax")
            .HasColumnType("money");

        builder.Property(b => b.TaxAmount)
            .HasColumnName("TaxAmount")
            .HasColumnType("money");

        builder.Property(b => b.AmountInclTax)
            .HasColumnName("AmountInclTax")
            .HasColumnType("money");

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureTransactionFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, ITransactionEntity
    {
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.DocumentNumber)
            .HasColumnName("DocumentNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.DocumentDate)
            .HasColumnName("DocumentDate")
            .HasColumnType("datetime2(7)");

        builder.Property(x => x.AlternateNumber)
            .HasColumnName("AlternateCode")
            .HasColumnType("nvarchar(50)");

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureAddressFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IAddress
    {
        builder.Property(x => x.Address)
            .HasColumnName("Address")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.Address2)
            .HasColumnName("Address2")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.City)
            .HasColumnName("City")
            .HasColumnType("nvarchar(30)")
            .IsRequired(false);

        builder.Property(x => x.Region)
            .HasColumnName("Region")
            .HasColumnType("nvarchar(30)")
            .IsRequired(false);

        builder.Property(x => x.Country)
            .HasColumnName("Country")
            .HasColumnType("nvarchar(10)")
            .IsRequired(false);

        builder.Property(x => x.PostalCode)
            .HasColumnName("PostalCode")
            .HasColumnType("nvarchar(20)")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureContactFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IContact
    {
        builder.Property(x => x.Phone)
            .HasColumnName("Phone")
            .HasColumnType("nvarchar(30)")
            .IsRequired(false);

        builder.Property(x => x.Email)
            .HasColumnName("Email")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureAuditInfoFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IAuditInfo
    {
        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        return builder;
    }
}
