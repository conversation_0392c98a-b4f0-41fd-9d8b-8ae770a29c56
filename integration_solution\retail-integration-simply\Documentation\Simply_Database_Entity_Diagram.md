# Simply Database Entity Diagram

This diagram shows the main database entities in the retail-integration-simply project.

```mermaid
classDiagram
    class SimplyCustomerEntity {
        +Guid Id
        +int CustomerNumber
        +string Name
        +string? Address
        +string? AddressLine2
        +string? City
        +string? StateRegion
        +string? Phone
        +string? Email
        +string? ContactName
        +string? ClinicCode
        +string? FileName
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
    }
    
    class SalesHeader {
        +Guid? Id
        +int? InvoiceNumber
        +string? SalesType
        +int? Line
        +string? PatientNumber
        +DateOnly? DocumentDate
        +DateOnly? DeliveryDate
        +int? Quantity
        +decimal? Amount
        +decimal? GST
        +string? Clinic
        +string? ReferralSource
        +char? HearingAidSale
        +string? InvoiceCreatedBy
        +string? InvoiceSpecialist
        +string? CreditReasonCode
        +string? OHSClaim
        +string? AppliesToDocument
        +decimal? Gross
        +decimal? DiscountAmount
        +string? DateTimeCreated
        +string? InventoryLocation
        +string? FileName
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
        +ICollection~SalesDetail~? SalesDetails
        +ICollection~SalesFunder~? SalesFunders
    }
    
    class SalesDetail {
        +Guid? Id
        +Guid? SalesInvoiceId
        +int? InvoiceNumber
        +string? SalesType
        +int? Line
        +string? ProductNumber
        +int? Quantity
        +decimal? UnitPrice
        +decimal? Amount
        +decimal? GST
        +decimal? OHSBenefitAmount
        +decimal? OHSTopUpAmount
        +decimal? OHSBenefitAmountInclGST
        +decimal? OHSTopUpAmountInclGST
        +bool? NonStock
        +decimal? Gross
        +decimal? DiscountAmount
        +string? SerialNumber
        +DateTime? WarrantyExpireDate
        +string? FileName
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +SalesHeader? SalesHeader
    }
    
    class SalesFunder {
        +Guid? Id
        +Guid? SalesInvoiceId
        +int? InvoiceNumber
        +string? SalesType
        +int? FunderID
        +decimal? Amount
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +SalesHeader? SalesHeader
    }
    
    class POHeader {
        +Guid Id
        +string PONumber
        +DateTime PODate
        +string Status
        +string Supplier
        +string Clinic
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
        +ICollection~PODetail~? PODetails
    }
    
    class PODetail {
        +Guid Id
        +Guid POHeaderId
        +string PONumber
        +int LineNumber
        +string ProductCode
        +int Quantity
        +decimal UnitCost
        +decimal LineAmount
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +POHeader? POHeader
    }
    
    class PurchaseReceiptHeader {
        +Guid Id
        +string ReceiptNumber
        +DateTime ReceiptDate
        +string PONumber
        +string Supplier
        +string Clinic
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
        +ICollection~PurchaseReceiptLine~? PurchaseReceiptLines
    }
    
    class PurchaseReceiptLine {
        +Guid Id
        +Guid ReceiptHeaderId
        +string ReceiptNumber
        +int LineNumber
        +string ProductCode
        +int Quantity
        +decimal UnitCost
        +decimal LineAmount
        +string? SerialNumber
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +PurchaseReceiptHeader? PurchaseReceiptHeader
    }
    
    class CashReceipts {
        +Guid Id
        +string DocumentNumber
        +DateTime DocumentDate
        +string PatientNumber
        +string PayerNumber
        +string ClinicCode
        +string PaymentMethod
        +decimal Amount
        +string AppliesToDocNumber
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
    }
    
    SalesHeader "1" --> "0..*" SalesDetail : contains
    SalesHeader "1" --> "0..*" SalesFunder : contains
    POHeader "1" --> "0..*" PODetail : contains
    PurchaseReceiptHeader "1" --> "0..*" PurchaseReceiptLine : contains
```

This diagram shows the main database entities in the Simply Integration project:

1. **SimplyCustomerEntity**: Represents customer information for patient integration
2. **SalesHeader/SalesDetail/SalesFunder**: Represents sales invoices, credit memos, and their lines and funders
3. **POHeader/PODetail**: Represents purchase orders and their lines
4. **PurchaseReceiptHeader/PurchaseReceiptLine**: Represents purchase receipts and their lines
5. **CashReceipts**: Represents payment transactions

Each entity includes integration tracking fields (IntegrationRequired, IntegrationDate) to manage the synchronization process.