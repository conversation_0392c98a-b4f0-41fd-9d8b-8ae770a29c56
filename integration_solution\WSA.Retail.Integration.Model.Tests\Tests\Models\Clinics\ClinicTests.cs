﻿using Moq;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Clinics;

public class ClinicTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly ClinicService _clinicService;

    public ClinicTests()
    {
        _serviceFactory = new ServiceFactory();
        _clinicService = _serviceFactory.CreateClinicService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnClinic()
    {
        // Act
        var result = await _clinicService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "CLI1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("AC6CC27F-473C-46FD-B262-23E4380F7A94"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnClinic()
    {
        // Act
        var result = await _clinicService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-CLI1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("AC6CC27F-473C-46FD-B262-23E4380F7A94"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _clinicService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "CLI1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("AC6CC27F-473C-46FD-B262-23E4380F7A94"), result.Id);
    }

    [Fact]
    public async Task UpsertAsync_NewClinicShouldInsertNewClinic()
    {
        // GIVEN a new clinic
        var clinic = new Clinic()
        {
            ExternalSystemCode = _serviceFactory.AppSettings.ExternalSystemCode,
            Code = "TEST1",
            Name = "Test Clinic"
        };

        // WHEN UpsertAsync is called
        var result = await _clinicService.UpsertAsync(clinic);

        // THEN a new clinic is returned
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);

        // THEN an event was raised
        _serviceFactory.EventGridPublisherMock.Verify(p => p.RaiseEventAsync(
            It.IsAny<AppSettings>(),
            It.Is<string>(s => s == EntityType.Clinic.GetEventName()),
            It.IsAny<string>(),
            It.IsAny<Clinic>()),
            Times.Once);
    }

    [Fact]
    public async Task UpsertAsync_ModifyClinicShouldReturnClinic()
    {
        // GIVEN a clinic
        var clinic = await _clinicService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "CLI1");
        Assert.NotNull(clinic);
        Assert.NotEqual(Guid.Empty, clinic.Id);

        // WHEN that clinic is modified and UpsertAsync called
        clinic.Address = "Modified Address";
        var result = await _clinicService.UpsertAsync(clinic);

        // THEN the updated clinic is returned
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);
        Assert.Equal(clinic.Address, result.Address);

        // THEN the database includes the updated clinic
        result = await _clinicService.GetAsync(
            externalSystemCode: clinic.ExternalSystemCode!,
            code: clinic.Code);
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);
        Assert.Equal(clinic.Address, result.Address);

        // THEN an event was raised
        _serviceFactory.EventGridPublisherMock.Verify(p => p.RaiseEventAsync(
            It.IsAny<AppSettings>(),
            It.Is<string>(s => s == EntityType.Clinic.GetEventName()),
            It.IsAny<string>(),
            It.IsAny<Clinic>()),
            Times.Once);
    }

    [Fact]
    public async Task UpsertAsync_NoChangesDoesNotRaiseEvent()
    {
        // GIVEN a clinic
        var clinic = await _clinicService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "CLI2");
        Assert.NotNull(clinic);
        Assert.NotEqual(Guid.Empty, clinic.Id);

        // WHEN UpsertAsync is called without changing anything
        var result = await _clinicService.UpsertAsync(clinic);

        // THEN the clinic is returned
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);

        // THEN an event was not raised
        _serviceFactory.EventGridPublisherMock.Verify(p => p.RaiseEventAsync(
            It.IsAny<AppSettings>(),
            It.Is<string>(s => s == EntityType.Clinic.GetEventName()),
            It.IsAny<string>(),
            It.IsAny<Clinic>()),
            Times.Never);
    }
}

