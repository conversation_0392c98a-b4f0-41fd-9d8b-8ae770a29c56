﻿CREATE VIEW [cosium].[Products]
AS

SELECT Source.id,
       CONCAT('ITEM-', FORMAT(CAST(Source.id AS INT), '000000')) AS Code,
       Source.id AS ExternalCode,
       IIF(ISNULL(Source.libelle, '') <> '', Source.libelle, Source.codeproduit) AS [Name],
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.produit AS Source

 OUTER APPLY  (
       SELECT (
              SELECT CONCAT('ITEM-', FORMAT(CAST(produit.id AS INT), '000000')) AS 'code',
                     produit.id AS 'externalCode',
                     IIF(ISNULL(produit.libelle, '') <> '', produit.libelle, produit.codeproduit) AS 'name',
                     NULL AS 'pimProductId',
                     typefamille.id AS 'category.externalCode',
                     typefamille.code AS 'category.name',
                     appareildivers.id AS 'subcategory.externalCode',
                     appareildivers.description AS 'subcategory.name',
                     fournisseur.id AS 'vendor.externalCode',
                     fournisseur.[libelleinterne] AS 'vendor.name',
                     marque.id AS 'manufacturer.externalCode',
                     marque.libelleinterne AS 'manufacturer.name',
                     produit.codegtin AS 'gtin',
                     TauxTVA.id AS 'taxGroup.externalCode',
                     TauxTVA.taux AS 'taxGroup.Rate'

                FROM cosium.produit

                LEFT JOIN cosium.appareildivers
                       ON produit.famille = appareildivers.id

                LEFT JOIN cosium.typefamille
                       ON appareildivers.typefamille = typefamille.id

                LEFT JOIN cosium.fournisseur
                       ON produit.reffournisseur = fournisseur.id
                      AND fournisseur.id NOT IN (1, 616, 617)

                LEFT JOIN cosium.marque
                       ON produit.refmarque = marque.id

                LEFT JOIN cosium.TauxTVA
                       ON reftva = TauxTVA.id

               WHERE produit.id = Source.id

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                     ) AS JSON
              ) AS t1