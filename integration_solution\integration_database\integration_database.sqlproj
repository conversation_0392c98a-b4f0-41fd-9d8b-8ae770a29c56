﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>integration_database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{aa403ac7-dd2d-4ca2-bd2a-c2d5fb3671cb}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>integration_database</RootNamespace>
    <AssemblyName>integration_database</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
    <IsChangeTrackingOn>True</IsChangeTrackingOn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\sproc" />
    <Folder Include="dbo\tables" />
    <Folder Include="dbo\views" />
    <Folder Include="dbo\postdeployment" />
    <Folder Include="dbo\postdeployment\referencedata" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\tables\Adjustment.sql" />
    <Build Include="dbo\tables\Clinic.sql" />
    <Build Include="dbo\tables\Color.sql" />
    <Build Include="dbo\tables\Company.sql" />
    <Build Include="dbo\tables\Country.sql" />
    <Build Include="dbo\tables\Coupling.sql" />
    <Build Include="dbo\tables\Entity.sql" />
    <Build Include="dbo\tables\EntitySubscriber.sql" />
    <Build Include="dbo\tables\ExternalSystem.sql" />
    <Build Include="dbo\tables\Manufacturer.sql" />
    <Build Include="dbo\tables\Patient.sql" />
    <Build Include="dbo\tables\Payment.sql" />
    <Build Include="dbo\tables\Payor.sql" />
    <Build Include="dbo\tables\Product.sql" />
    <Build Include="dbo\tables\ProductCategory.sql" />
    <Build Include="dbo\tables\PurchaseOrder.sql" />
    <Build Include="dbo\tables\PurchaseOrderLine.sql" />
    <Build Include="dbo\tables\PurchaseReceipt.sql" />
    <Build Include="dbo\tables\PurchaseReceiptLine.sql" />
    <Build Include="dbo\tables\PurchaseReturn.sql" />
    <Build Include="dbo\tables\PurchaseReturnLine.sql" />
    <Build Include="dbo\tables\ReconciliationPaymentTable.sql" />
    <Build Include="dbo\tables\SalesCredit.sql" />
    <Build Include="dbo\tables\SalesCreditLine.sql" />
    <Build Include="dbo\tables\SalesInvoice.sql" />
    <Build Include="dbo\tables\SalesInvoiceLine.sql" />
    <Build Include="dbo\tables\SalesOrder.sql" />
    <Build Include="dbo\tables\SalesOrderLine.sql" />
    <Build Include="dbo\tables\SalesReconciliation.sql" />
    <Build Include="dbo\tables\TaxGroup.sql" />
    <Build Include="dbo\tables\TaxLine.sql" />
    <Build Include="dbo\tables\Vendor.sql" />
    <Build Include="dbo\tables\Watermark.sql" />
    <Build Include="dbo\views\Categories.sql" />
    <Build Include="dbo\views\Clinics.sql" />
    <Build Include="dbo\views\Colors.sql" />
    <Build Include="dbo\views\Companies.sql" />
    <Build Include="dbo\views\Entities.sql" />
    <Build Include="dbo\views\EntitySubscribers.sql" />
    <Build Include="dbo\views\ExternalSystems.sql" />
    <Build Include="dbo\views\Manufacturers.sql" />
    <Build Include="dbo\views\Patients.sql" />
    <Build Include="dbo\views\Payors.sql" />
    <Build Include="dbo\views\Products.sql" />
    <Build Include="dbo\views\PurchaseOrders.sql" />
    <Build Include="dbo\views\PurchaseReturns.sql" />
    <Build Include="dbo\views\SalesCredits.sql" />
    <Build Include="dbo\views\SalesInvoices.sql" />
    <Build Include="dbo\views\SalesOrders.sql" />
    <Build Include="dbo\views\SalesReconciliations.sql" />
    <Build Include="dbo\views\TaxGroups.sql" />
    <Build Include="dbo\views\Vendors.sql" />
    <Build Include="dbo\tables\Claim.sql" />
    <Build Include="dbo\tables\PurchaseShipment.sql" />
    <Build Include="dbo\tables\PurchaseShipmentLine.sql" />
    <Build Include="dbo\tables\Battery.sql" />
    <Build Include="dbo\tables\ProductModel.sql" />
    <Build Include="dbo\views\Batteries.sql" />
    <None Include="dbo\postdeployment\referencedata\Entities.sql" />
    <Build Include="dbo\tables\PaymentMethod.sql" />
    <Build Include="dbo\views\Adjustments.sql" />
    <Build Include="dbo\views\PaymentMethods.sql" />
    <Build Include="dbo\views\ProductModels.sql" />
    <Build Include="dbo\sproc\GetWatermark.sql" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="dbo\postdeployment\Seed.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="dbo\postdeployment\referencedata\ExternalSystems.sql" />
    <None Include="dbo\postdeployment\referencedata\EntitySubscribers.sql" />
    <None Include="dbo\postdeployment\referencedata\Categories.sql" />
  </ItemGroup>
</Project>