﻿using GraphQL;
using GraphQL.Client.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.PIM.GraphQL;
using WSA.Retail.Integration.PIM.Models.Attributes;
using WSA.Retail.Integration.PIM.Models.Brands;
using WSA.Retail.Integration.PIM.Models.BundleSets;
using WSA.Retail.Integration.PIM.Models.Colors;
using WSA.Retail.Integration.PIM.Models.Images;
using WSA.Retail.Integration.PIM.Models.Products;


namespace WSA.Retail.Integration.PIM.Queries;

public class ProductQueryService(
    IOptions<AppSettings> appSettings,
    ILogger<ProductQueryService> logger,
    ITokenService tokenService,
    GraphQLHttpClient graphQLClient,
    IPimAttributeRepository pimAttributeRepository,
    IPimBrandRepository pimBrandRepository,
    IPimBundleSetRepository pimBundleSetRepository,
    IPimColorRepository pimColorRepository,
    IPimImageRepository pimImageRepository,
    IPimProductRepository pimProductRepository,
    IPimParentProductRepository parentProductRepository
    ) : LoggingBase<GraphQLRequest>(logger), IProductQueryService
{
    public readonly AppSettings _appSettings = appSettings.Value;
    public readonly ITokenService _tokenService = tokenService;
    private readonly GraphQLHttpClient _graphQLClient = graphQLClient;
    private readonly IPimAttributeRepository _pimAttributeRepository = pimAttributeRepository;
    private readonly IPimBrandRepository _pimBrandRepository = pimBrandRepository;
    private readonly IPimBundleSetRepository _pimBundleSetRepository = pimBundleSetRepository;
    private readonly IPimColorRepository _pimColorRepository = pimColorRepository;
    private readonly IPimImageRepository _pimImageRepository = pimImageRepository;
    private readonly IPimProductRepository _pimProductRepository = pimProductRepository;
    private readonly IPimParentProductRepository _parentProductRepository = parentProductRepository;

    public async Task UpdateProductsFromPIMAsync(string countryCode, string brandCode)
    {
        LogMethodStart();

        var token = await _tokenService.GetTokenAsync();
        _graphQLClient.HttpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", token);

        var pimProducts = await GetProductsFromPIMAsync(countryCode, brandCode);
        var tasks = new List<Task>
        {
            _pimAttributeRepository.UpsertListAsync(pimProducts),
            _pimBrandRepository.UpsertListAsync(pimProducts),
            _pimBundleSetRepository.UpsertListAsync(pimProducts),
            _pimColorRepository.UpsertListAsync(pimProducts)
        };
        await Task.WhenAll(tasks);
        await _pimImageRepository.UpsertListAsync(pimProducts);
        await _pimProductRepository.UpsertListAsync(pimProducts);
        await _parentProductRepository.UpsertListAsync(pimProducts);
    }

    public async Task<List<PisProductResponseForAllProductsOutputType>> GetProductsFromPIMAsync(string countryCode, string brandCode)
    {
        LogMethodStart();

        List<PisProductResponseForAllProductsOutputType> pimProducts = [];
        int pageCount = 0;
        try
        {
            var response = await SendQueryAsync(countryCode, brandCode, 1);
            ArgumentNullException.ThrowIfNull(response);
            foreach (var product in response.Product.ProductsByMarketResult.Products)
            {
                pimProducts.Add(product);
            }
            pageCount = response.Product.ProductsByMarketResult.Pagination.PageCount ?? 0;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            throw;
        }

        if (pageCount > 1)
        {
            var tasks = new List<Task>();
            await Parallel.ForAsync(
                2,
                pageCount + 1,
                new ParallelOptions { MaxDegreeOfParallelism = 5 },
                async (i, cancellationToken) =>
                {
                    try
                    {
                        var pagedResponse = await SendQueryAsync(countryCode, brandCode, i);
                        ArgumentNullException.ThrowIfNull(pagedResponse);
                        foreach (var product in pagedResponse.Product.ProductsByMarketResult.Products)
                        {
                            pimProducts.Add(product);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogCustomError(ex);
                    }
                }
            );
        }

        return pimProducts;
    }

    public async Task<GraphQLProductQueryData?> SendQueryAsync(string countryCode, string brandCode, int pageNumber)
    {
        LogMethodStart();
        LogCustomInformation($"Getting products for {countryCode} and {brandCode}, pageNumber {pageNumber}");

        try
        {
            var response = await _graphQLClient.SendQueryAsync<GraphQLProductQueryData>(GetQueryRequest(countryCode, brandCode, pageNumber));
            return response.Data;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }

    }
    private  GraphQLRequest GetQueryRequest(string countryCode, string brandCode, int pageNo)
    {
        LogMethodStart();

        int pageSize = Convert.ToInt32(Environment.GetEnvironmentVariable("PageSize") ?? "100");

        var request = new GraphQLRequest()
        {
            Query = @"
                query ($marketFilter: MarketFilterInputType, 
                       $languageFilter: LanguageFilterInputType!,
                       $paginationFilter: PaginationFilterInput!, 
                       $productName: String, 
                       $isOnlyValidProducts: Boolean) 
                {
                    product 
                    {
                        getAllProductsByMarket(marketFilter: $marketFilter,
                                               languageFilter: $languageFilter,
                                               paginationFilter: $paginationFilter,
                                               productName: $productName,
                                               isOnlyValidProducts: $isOnlyValidProducts) 
                        {
                            pagination 
                            {
                                pageCount
                            }
                            products
                            {
                                productId,
                                name,
                                isAvailable,
                                isPhasedOut,
                                listPrice,
                                parentProductSku,
                                productSource,
                                productType,
                                ranking,
                                releaseDate,
                                sku,
                                state,
                                createdAt,
                                updatedAt,
                                assets 
                                {
                                    images 
                                    {
                                        brandCode,
                                        cdnUrl,
                                        isDefault
                                    }
                                },
                                attributes 
                                {
                                    attributeCode,
                                    dataType,
                                    name,
                                    value,
                                    valueCode,
                                    values
                                },
                                brands,
                                bundleSets,
                                {
                                    bundleSetId,
                                    bundleSetName,
                                    bundleSetType,
                                    createdAt,
                                    isDefault,
                                    isMandatory,
                                    isMasterData,
                                    ranking,
                                    state,
                                    updatedAt,
                                    childProducts 
                                    {
                                        bundleSetId,
                                        createdAt,
                                        isAvailable,
                                        isDefault,
                                        isPhasedOut,
                                        productId,
                                        ranking,
                                        sku,
                                        state,
                                        updatedAt
                                    }
                                },  
                                categoryIds 
                                {
                                    categoryId,
                                    coreBrandId,
                                    createdAt,
                                    isVisible,
                                    name,
                                    ranking,
                                    state,
                                    supportedBrandIds,
                                    updatedAt
                                },
                                childProducts 
                                {
                                    createdAt,
                                    defaultChild,
                                    isAvailable,
                                    isPhasedOut,
                                    ranking,
                                    sku,
                                    state,
                                    updatedAt
                                },
                                color 
                                {
                                    agileName,
                                    attributeValueCode,
                                    hexCode
                                },
                                relatedProducts 
                                {
                                    categories,
                                    createdAt,
                                    isAvailable,
                                    isPhasedOut,
                                    isProductRelationAvailable,
                                    ranking,
                                    relationName,
                                    sku,
                                    state,
                                    updatedAt
                                }
                            }
                        }
                    }
                }",

            Variables = new
            {
                marketFilter = new
                {
                    brandCode,
                    countryCode
                },
                languageFilter = new
                {
                    languageCode = "en_GB"
                },
                paginationFilter = new
                {
                    pageNumber = pageNo,
                    pageSize,
                    sortBy = "UpdatedAt",
                    sortDirection = "DESCENDING"
                },
                isOnlyValidProducts = true
            }
        };

        return request;
    }
}