using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductBundlesetConfiguration : IEntityTypeConfiguration<PimProductBundleSetEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductBundleSetEntity> builder)
        {
            builder.ToTable("ProductBundleset", "pim");

            builder.Has<PERSON>ey(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.BundleSetId)
                .HasColumnName("BundleSetId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.IsAvailable)
                .HasColumnName("IsAvailable")
                .HasColumnType("bit");

            builder.Property(x => x.IsDefault)
                .HasColumnName("IsDefault")
                .HasColumnType("bit");

            builder.Property(x => x.IsPhasedOut)
                .HasColumnName("IsPhasedOut")
                .HasColumnType("bit");

            builder.Property(x => x.Ranking)
                .HasColumnName("Ranking")
                .HasColumnType("int");

            builder.Property(x => x.Sku)
                .HasColumnName("Sku")
                .HasColumnType("nvarchar(50)");

            builder.Property(x => x.State)
                .HasColumnName("State")
                .HasColumnType("nvarchar(50)");

            builder.Property(x => x.CreatedAt)
                .HasColumnName("CreatedAt")
                .HasColumnType("datetime2");

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("UpdatedAt")
                .HasColumnType("datetime2");

            builder.HasOne(x => x.ProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ProductId);

            builder.HasOne(x => x.BundleSetEntity)
                .WithMany()
                .HasForeignKey(x => x.BundleSetId);
        }
    }
}