﻿namespace WSA.Retail.Integration.Manage.Models.PurchaseOrders;

public class PurchaseOrderLineItemEventHubEvent
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal? UnitCost { get; set; }
    public Guid? ColorId { get; set; }
    public Guid? BatteryTypeId { get; set; }
    public string? Sku { get; set; }
    public List<object> Attributes { get; set; } = [];
}
