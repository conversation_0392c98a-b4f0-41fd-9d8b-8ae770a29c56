﻿using Microsoft.Identity.Client;
using BaseConfiguration = WSA.Retail.Integration.Configuration;

namespace WSA.Retail.Integration.Manage.Configuration
{
    public class AppSettings : BaseConfiguration.AppSettings
    {
        public required string EventHubConnectionString { get; set; }
        public required string EventHubConsumerGroup { get; set; }
        public required string EventHubName { get; set; }
        public required string EventArchiveContainerName { get; set; }
        public required string ManageApiBaseUrl { get; set; }
        public required string ManageApiKey { get; set; }
        public required Guid ManageTenantId { get; set; }
        public required string LocalTimeZone { get; set; }
        public string? DefaultManufacturer { get; set; }
    }
}
