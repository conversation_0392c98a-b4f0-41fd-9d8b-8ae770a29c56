﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Manage.Models.CategoryMappings;

public class CategoryMappingConfiguration : IEntityTypeConfiguration<CategoryMappingEntity>
{
    public void Configure(EntityTypeBuilder<CategoryMappingEntity> builder)
    {
        builder.ToTable("CategoryMapping", "manage");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasDefaultValueSql("NEWID()")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.IsHearingAid)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(e => e.CreatedOn)
            .HasDefaultValueSql("sysutcdatetime()")
            .ValueGeneratedOnAdd()
            .IsRequired();

        builder.Property(e => e.ModifiedOn)
            .HasDefaultValueSql("sysutcdatetime()")
            .ValueGeneratedOnAdd()
            .IsRequired();

        // Indexes
        builder.HasIndex(e => e.CategoryId)
            .IsUnique()
            .HasDatabaseName("IX_CategoryMapping_CategoryId");
    }
}