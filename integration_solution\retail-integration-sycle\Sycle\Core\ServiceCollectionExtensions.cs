﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Sycle.Configuration;
using WSA.Retail.Integration.Sycle.Data;
using WSA.Retail.Integration.Sycle.Data.Repositories;
using WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Sycle.EventProcessing;
using WSA.Retail.Integration.Sycle.Models.Batteries;
using WSA.Retail.Integration.Sycle.Models.Clinics;
using WSA.Retail.Integration.Sycle.Models.Earmolds;
using WSA.Retail.Integration.Sycle.Models.HearingAids;
using WSA.Retail.Integration.Sycle.Models.Patients;
using WSA.Retail.Integration.Sycle.Models.Payments;
using WSA.Retail.Integration.Sycle.Models.Purchases;
using WSA.Retail.Integration.Sycle.Models.Receivers;
using WSA.Retail.Integration.Sycle.Models.Remotes;
using WSA.Retail.Integration.Sycle.Models.Services;

namespace WSA.Retail.Integration.Sycle.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSycleServices(
        this IServiceCollection services, 
        AppSettings appSettings)
    {
        // ==== DB CONTEXT =================================================
        bool enableDetailedDbLogging = Convert.ToBoolean(Environment.GetEnvironmentVariable("EnableDetailedDbLogging") ?? "false");

        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.SqlConnectionString, "SqlConnectionString");
        services.AddDbContextFactory<SycleContext>(options =>
        {
            var dbOptionsBuilder = options.UseSqlServer(appSettings.SqlConnectionString);
            if (enableDetailedDbLogging)
            {
                dbOptionsBuilder
                    .EnableDetailedErrors(true)
                    .EnableSensitiveDataLogging(true)
                    .LogTo(s => System.Diagnostics.Debug.WriteLine(s));
            }
        });

        // ==== REPOSITORIES =================================================
        services.AddScoped<ISycleBatteryRepository, SycleBatteryRepository>();
        services.AddScoped<ISycleClinicRepository, SycleClinicRepository>();
        services.AddScoped<ISycleEarmoldRepository, SycleEarmoldRepository>();
        services.AddScoped<ISycleHearingAidRepository, SycleHearingAidRepository>();
        services.AddScoped<ISyclePatientRepository, SyclePatientRepository>();
        services.AddScoped<ISyclePaymentRepository, SyclePaymentRepository>();
        services.AddScoped<ISycleReceiverRepository, SycleReceiverRepository>();
        services.AddScoped<ISycleRemoteRepository, SycleRemoteRepository>();
        services.AddScoped<ISycleServiceRepository, SycleServiceRepository>();
        services.AddScoped<ISyclePurchaseRepository, SyclePurchaseRepository>();
        services.AddScoped<ISyclePurchaseDeletedRepository, SyclePurchaseDeletedRepository>();
        services.AddScoped<ISycleWriteOffRepository, SycleWriteOffRepository>();

        // ==== SERVICES =================================================
        services.AddScoped<ISycleBatteryService, SycleBatteryService>();
        services.AddScoped<ISycleClinicService, SycleClinicService>();
        services.AddScoped<ISycleEarmoldService, SycleEarmoldService>();
        services.AddScoped<ISycleHearingAidService, SycleHearingAidService>();
        services.AddScoped<ISyclePatientService, SyclePatientService>();
        services.AddScoped<ISycleReceiverService, SycleReceiverService>();
        services.AddScoped<ISycleRemoteService, SycleRemoteService>();
        services.AddScoped<ISycleServiceService, SycleServiceService>();
 
        // ==== EVENT PROCESSORS =================================================
        services.AddScoped<ISycleClinicProcessor, SycleClinicProcessor>();
        services.AddScoped<ISyclePatientProcessor, SyclePatientProcessor>();
        services.AddScoped<ISycleBatteryProcessor, SycleBatteryProcessor>();
        services.AddScoped<ISycleEarmoldProcessor, SycleEarmoldProcessor>();
        services.AddScoped<ISycleReceiverProcessor, SycleReceiverProcessor>();
        services.AddScoped<ISycleRemoteProcessor, SycleRemoteProcessor>();
        services.AddScoped<ISycleHearingAidProcessor, SycleHearingAidProcessor>();
        services.AddScoped<ISycleServiceProcessor, SycleServiceProcessor>();
        services.AddScoped<ISyclePaymentProcessor, SyclePaymentProcessor>();
        services.AddScoped<ISyclePurchaseProcessor, SyclePurchaseProcessor>();
        services.AddScoped<ISycleWriteOffProcessor, SycleWriteOffProcessor>();
        services.AddScoped<FromSycleEventProcessor>();

        services.AddLogging();

        return services;
    }
}