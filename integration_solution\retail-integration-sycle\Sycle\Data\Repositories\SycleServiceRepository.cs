using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Sycle.Models.Services;

namespace WSA.Retail.Integration.Sycle.Data.Repositories;

public class SycleServiceRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SycleServiceRepository> logger,
    IDbContextFactory<SycleContext> dbContextFactory) : ISycleServiceRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SycleServiceRepository> _logger = logger;
    private readonly IDbContextFactory<SycleContext> _dbContextFactory = dbContextFactory;
       

    public async Task<List<SycleService>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetIntegrationRecordsAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entities = await context.SycleServiceEntity
            .Where(s => s.IntegrationRequired)
            .ToListAsync();

        var services = entities.Select(e => new SycleService
        {
            ServiceId = e.ServiceId,
            ServiceUuid = e.ServiceUuid,
            ClinicId = e.ClinicId,
            ServiceName = e.ServiceName,
            ServicePrice = e.ServicePrice,
            CptCode = e.CptCode,
            LastUpdate = e.LastUpdate
        }).ToList();

        return services ?? [];
    }

    public async Task<SycleService?> GetAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleServiceEntity
            .FirstOrDefaultAsync(s => s.ServiceId == id);

        if (entity == null)
        {
            return null;
        }

        var service = new SycleService
        {
            ServiceId = entity.ServiceId,
            ServiceUuid = entity.ServiceUuid,
            ClinicId = entity.ClinicId,
            ServiceName = entity.ServiceName,
            ServicePrice = entity.ServicePrice,
            CptCode = entity.CptCode,
            LastUpdate = entity.LastUpdate
        };

        return service;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpdateIntegrationStatusAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleServiceEntity
            .FirstOrDefaultAsync(s => s.ServiceId == id);

        if (entity == null)
        {
            return false;
        }

        entity.IntegrationRequired = false;
        entity.IntegratedOn = DateTime.UtcNow;
        entity.ModifiedOn = DateTime.UtcNow;

        await context.SaveChangesAsync();

        return true;
    }
}