using Moq;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Tests.Configuration;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseOrders;
using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Tests.Utilities;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Manage.Tests.PurchaseOrders;

namespace WSA.Retail.Integration.Tests.Tests.PurchaseOrders;

public class PurchaseOrderFromEventHandlerTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<IManageAPI> _manageAPIMock;
    private readonly PurchaseOrderEventHandler _handler;
    private readonly Mock<IPurchaseOrderService> _purchaseOrderServiceMock;
    private readonly Mock<IClinicService> _clinicServiceMock;
    private readonly Mock<IVendorService> _vendorServiceMock;
    private readonly Mock<IProductService> _productServiceMock;

    public PurchaseOrderFromEventHandlerTests()
    {
        _serviceFactory = new ServiceFactory();
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());

        _manageAPIMock = new Mock<IManageAPI>();
        _purchaseOrderServiceMock = new Mock<IPurchaseOrderService>();
        _clinicServiceMock = new Mock<IClinicService>();
        _vendorServiceMock = new Mock<IVendorService>();
        _productServiceMock = new Mock<IProductService>();

        _handler = new PurchaseOrderEventHandler(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PurchaseOrderEventHandler>>().Object,
            blobServiceClient: new Mock<BlobServiceClient>().Object,
            queueClients: new Mock<Dictionary<string, QueueClient>>().Object,
            manageApi: _manageAPIMock.Object,
            contextFactory: _serviceFactory.ManageDbContextFactory,
            purchaseOrderService: _purchaseOrderServiceMock.Object,
            clinicService: _clinicServiceMock.Object,
            vendorService: _vendorServiceMock.Object,
            productService: _productServiceMock.Object);
    }


    [Fact]
    public async Task HandleFromQueueAsync_Test()
    {
        // GIVEN a purchase order event
        Guid productId = Guid.NewGuid();
        var json = PurchaseOrderHelper.CreateOrderSubmittedEvent(
            orderNumber: "ORD12345",
            PurchaseOrderHelper.CreateSingleOrderLine(productId));

        var eventMessage = JsonSerializer.Deserialize<EventHubMessage>(json);
        ArgumentNullException.ThrowIfNull(eventMessage);

        // GIVEN the Manage API contains this purchase order
        var apiResponse = PurchaseOrderHelper.CreateOrder2GetResponse(
            eventMessage: eventMessage,
            locationId: Guid.NewGuid(),
            supplierId: Guid.NewGuid());

        _manageAPIMock
            .Setup(x => x.OrdersGET2Async(It.IsAny<Guid>()))
            .ReturnsAsync(apiResponse);

        // GIVEN clinic exists in the middle layer
        _clinicServiceMock
            .Setup(x => x.ValidateExternalReferenceAsync(It.IsAny<string>(), 
                It.Is<ExternalReference>(er => er.ExternalCode == apiResponse.Location.Id.ToString())))
            .ReturnsAsync(new ExternalReference()
            {
                Id = Guid.NewGuid(),
                Code = "TESTCLI",
                Name = "Test Clinic",
                ExternalCode = apiResponse.Location.Id.ToString()
            });

        // GIVEN vendor exists in the middle layer
        _vendorServiceMock
            .Setup(x => x.ValidateExternalReferenceAsync(It.IsAny<string>(),
                It.Is<ExternalReference>(er => er.ExternalCode == apiResponse.Supplier.Id.ToString())))
            .ReturnsAsync(new ExternalReference()
            {
                Id = Guid.NewGuid(),
                Code = "TESTVDR",
                Name = "Test Vendor",
                ExternalCode = apiResponse.Supplier.Id.ToString()
            });

        // GIVEN product exists in the middle layer
        _productServiceMock
            .Setup(x => x.ValidateExternalReferenceAsync(It.IsAny<string>(),
                It.Is<ExternalReference>(er => er.ExternalCode == apiResponse.LineItems.First().Product.Id.ToString())))
            .ReturnsAsync(new ExternalReference()
            {
                Id = Guid.NewGuid(),
                Code = "TESTPROD",
                Name = "Test Product",
                ExternalCode = apiResponse.LineItems.First().Product.Id.ToString()
            });

        // GIVEN middle layer accepts the upsert call
        _purchaseOrderServiceMock
            .Setup(x => x.UpsertAsync(It.IsAny<PurchaseOrder>()))
            .ReturnsAsync((PurchaseOrder po) =>
            {
                if (po.Id == Guid.Empty)
                {
                    po.Id = Guid.NewGuid();
                }
                po.CreatedOn = DateTime.UtcNow;
                po.ModifiedOn = DateTime.UtcNow;
                return po;
            });

        // WHEN the event is handled
        var result = await _handler.HandleFromQueueAsync(eventMessage!);

        // THEN the event is handled successfully
        Assert.True(result);
    }
}