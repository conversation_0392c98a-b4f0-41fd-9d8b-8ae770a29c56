﻿using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.Manage.Models.Clinics;
using WSA.Retail.Integration.Manage.Models.Colors;
using WSA.Retail.Integration.Manage.Models.Manufacturers;
using WSA.Retail.Integration.Manage.Models.Patients;
using WSA.Retail.Integration.Manage.Models.Payors;
using WSA.Retail.Integration.Manage.Models.Products;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;
using WSA.Retail.Integration.Manage.Models.PurchaseReceipts;
using WSA.Retail.Integration.Manage.Models.SalesInvoices;
using WSA.Retail.Integration.Manage.Models.Vendors;


namespace WSA.Retail.Integration.Manage.EventProcessing;

public class EventProcessor
{
    private readonly Dictionary<string, Type> _eventHandlerTypes;
    private readonly IServiceProvider _serviceProvider;

    public EventProcessor(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _eventHandlerTypes = new Dictionary<string, Type>
        {
            { "Manage.Locations:LocationCreated", typeof(ClinicEventHandler) },
            { "Manage.Locations:LocationUpdated", typeof(ClinicEventHandler) },
            { "clinics", typeof(ClinicEventHandler) },

            { "Manage.Inventory:ColorCreated", typeof(ColorEventHandler) },
            { "Manage.Inventory:ColorUpdated", typeof(ColorEventHandler) },
            { "colors", typeof(ColorEventHandler) },

            { "Manage.Inventory:ManufacturerCreated", typeof(ManufacturerEventHandler) },
            { "Manage.Inventory:ManufacturerUpdated", typeof(ManufacturerEventHandler) },
            { "manufacturers", typeof(ManufacturerEventHandler) },

            { "Manage.Patients:PatientCreated", typeof(PatientEventHandler) },
            { "Manage.Patients:PatientUpdated", typeof(PatientEventHandler) },
            { "patients", typeof(PatientEventHandler) },

            { "Manage.Invoicing:FunderCreated", typeof(PayorEventHandler) },
            { "Manage.Invoicing:FunderUpdated", typeof(PayorEventHandler) },
            { "payors", typeof(PayorEventHandler) },

            { "Manage.Orders:OrderSubmitted", typeof(IPurchaseOrderEventHandler) },

            { "Manage.Orders:OrderLineItemAccepted", typeof(IPurchaseReceiptEventHandler) },

            { "products", typeof(ProductEventHandler) },

            { "Manage.Invoicing:AUDeliveryNoteCreated", typeof(ISalesInvoiceEventHandler) },

            { "Manage.Inventory:SupplierCreated", typeof(VendorEventHandler) },
            { "Manage.Inventory:SupplierUpdated", typeof(VendorEventHandler) },
            { "vendors", typeof (VendorEventHandler) }
        };
    }

    private IEventHandler? GetHandler(string eventType)
    {
        if (_eventHandlerTypes.TryGetValue(eventType, out var handlerType))
        {
            return _serviceProvider.GetRequiredService(handlerType) as IEventHandler;
        }
        return null;
    }

    internal async Task<bool> ProcessFromQueueEventAsync(string eventType, EventHubMessage message)
    {
        var handler = GetHandler(eventType);
        if (handler == null) return true;
        return await handler.HandleFromQueueAsync(message);
    }

    internal async Task<bool> ProcessToQueueEventAsync(string eventType, Events.Event ev)
    {
        var handler = GetHandler(eventType);
        if (handler == null) return false;
        return await handler.HandleToQueueAsync(ev);
    }
}