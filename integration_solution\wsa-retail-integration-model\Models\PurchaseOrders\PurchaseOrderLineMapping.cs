﻿using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Models.PurchaseOrders;

public static class PurchaseOrderLineMapping
{
    public static PurchaseOrderLineEntity ToEntity(
        this PurchaseOrderLine purchaseOrderLine,
        Guid purchaseOrderId)
    {
        return new PurchaseOrderLineEntity()
        {
            Id = purchaseOrderLine.Id,
            PurchaseOrderId = purchaseOrderId,
            Sequence = purchaseOrderLine.Sequence,
            ProductId = purchaseOrderLine.Product?.Id,
            Description = purchaseOrderLine.Description,
            Quantity = purchaseOrderLine.Quantity,
            UnitPrice = purchaseOrderLine.UnitPrice,
            GrossAmount = purchaseOrderLine.GrossAmount,
            DiscountAmount = purchaseOrderLine.DiscountAmount,
            AmountExclTax = purchaseOrderLine.AmountExclTax,
            TaxAmount = purchaseOrderLine.TaxAmount,
            AmountInclTax = purchaseOrderLine.AmountInclTax,
            PurchaseOrderEntity = new PurchaseOrderEntity { Id = purchaseOrderId }
        };
    }

    public static ExternalDocumentLineReference ToExternalDocumentLineReference(
        this PurchaseOrderLine purchaseOrderLine)
    {
        return new ExternalDocumentLineReference()
        {
            Id = purchaseOrderLine.Id,
            Sequence = purchaseOrderLine.Sequence
        };
    }
}
