using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.SalesInvoices
{
    public class SalesInvoiceLineConfiguration : IEntityTypeConfiguration<SalesInvoiceLineEntity>
    {
        public void Configure(EntityTypeBuilder<SalesInvoiceLineEntity> builder)
        {
            builder.ToTable("SalesInvoiceLine", "dbo");

            builder.<PERSON><PERSON><PERSON>(b => b.Id);

            builder.ConfigureSalesDocumentLineFields();

            builder.Property(l => l.SalesInvoiceId)
                .HasColumnName("SalesInvoiceId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder
                .HasOne(l => l.SalesInvoiceEntity)
                .WithMany(h => h.Lines)
                .HasForeignKey(l => l.SalesInvoiceId);
        }
    }
}