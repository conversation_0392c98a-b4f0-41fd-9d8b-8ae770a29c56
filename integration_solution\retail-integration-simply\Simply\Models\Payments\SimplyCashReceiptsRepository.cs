using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Simply.Models.Payments;

public class SimplyCashReceiptsRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyCashReceiptsRepository> logger,
    SimplyContext simplyContext)
    : ISimplyCashReceiptsRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyCashReceiptsRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<SimplyCashReceiptsEntity>> GetIntegrationRecordsAsync()

    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplyCashReceiptsEntity
                .Where(x => x.IntegrationRequired == true && x.ClinicCode != null)
                .ToListAsync();

            return list ?? [];
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplyCashReceiptsEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}