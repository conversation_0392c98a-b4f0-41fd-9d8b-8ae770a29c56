﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.Claims;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoice : ISalesInvoice
{
    [JsonPropertyName("id")] public Guid Id { get; set; } = Guid.Empty;
    [JsonPropertyName("externalSystemCode")] public string? ExternalSystemCode { get; set; }
    [JsonPropertyName("documentNumber")] public string? DocumentNumber { get; set; }
    [JsonPropertyName("externalReference")] public string? ExternalReference { get; set; }
    [JsonPropertyName("alternateNumber")] public string? AlternateNumber { get; set; }
    [JsonPropertyName("patient")] public ExternalReference? Patient { get; set; }
    [JsonPropertyName("clinic")] public ExternalReference? Clinic { get; set; }
    [JsonPropertyName("documentDate"), JsonConverter(typeof(DateOnlyJsonConverter))] public DateTime? DocumentDate { get; set; }
    [JsonPropertyName("salesInvoiceLines")] public List<SalesInvoiceLine> Lines { get; set; } = [];
    [JsonPropertyName("claims")] public List<Claim> Claims { get; set; } = [];
    IReadOnlyList<IClaim> IHasClaims.Claims => Claims;
    [JsonPropertyName("createdOn")] public DateTime? CreatedOn { get; set; }
    [JsonPropertyName("modifiedOn")] public DateTime? ModifiedOn { get; set; }



    public bool HasChanges(IDocument<SalesInvoiceLine>? oldEntity)
    {
        if (oldEntity is not SalesInvoice oldSalesInvoice) return true;
        return HasChanges(oldSalesInvoice);
    }

    public bool HasChanges(SalesInvoice? oldSalesInvoice)
    {
        if (oldSalesInvoice == null) return true;
        if (((IDocument<SalesInvoiceLine>)this).HasChanges(oldSalesInvoice)) return true;
        if (((IPatientAssociated)this).HasChanges(oldSalesInvoice)) return true;

        if (HaveLinesChanged(oldSalesInvoice)) return true;
        if (HaveClaimsChanged(oldSalesInvoice)) return true;

        return false;
    }

    private bool HaveLinesChanged(SalesInvoice oldSalesInvoice)
    {
        if (Lines.Count != oldSalesInvoice.Lines.Count) return true;

        foreach (var line in Lines)
        {
            var matchingOldLine = oldSalesInvoice.Lines.Where(l => l.Sequence == line.Sequence).FirstOrDefault();
            if (matchingOldLine == null || line.HasChanges(matchingOldLine))
                return true;
        }

        foreach (var oldLine in oldSalesInvoice.Lines)
        {
            if (!Lines.Any(l => l.Sequence == oldLine.Sequence))
                return true;
        }

        return false;
    }

    private bool HaveClaimsChanged(SalesInvoice oldSalesInvoice)
    {
        if (Claims.Count != oldSalesInvoice.Claims.Count)
            return true;

        foreach (var claim in Claims)
        {
            var matchingOldClaim = oldSalesInvoice.Claims.Where(l => l.DocumentNumber == claim.DocumentNumber).FirstOrDefault();
            if (matchingOldClaim == null || claim.HasChanges(matchingOldClaim))
                return true;
        }

        foreach (var oldClaim in oldSalesInvoice.Claims)
        {
            if (!Claims.Any(l => l.DocumentNumber == oldClaim.DocumentNumber))
                return true;
        }

        return false;
    }


    public static ExternalDocumentReference GetExternalDocumentReferenceFromJson(JsonNode json)
    {
        return new ExternalDocumentReference()
        {
            Id = (Guid?)json.AsObject()["salesInvoice"]?["id"],
            DocumentNumber = (string?)json.AsObject()["salesInvoice"]?["documentNumber"],
            ExternalReference = (string?)json.AsObject()["salesInvoice"]?["externalReference"]
        };
    }

    public void AddTelemetryProperties(MetricTelemetry metricTelemetry)
    {
        metricTelemetry.Properties.Add("SalesInvoiceId", Id.ToString());
        metricTelemetry.Properties.Add("SalesInvoiceNumber", DocumentNumber);
        if (DocumentDate != null)
            metricTelemetry.Properties.Add("SalesInvoiceDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            metricTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            metricTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Patient?.Id != null)
            metricTelemetry.Properties.Add("PatientId", Patient.Id.ToString());
        if (Patient?.Code != null)
            metricTelemetry.Properties.Add("PatientCode", Patient.Code);
    }

    public void AddEventProperties(EventTelemetry eventTelemetry)
    {
        eventTelemetry.Properties.Add("SalesInvoiceId", Id.ToString());
        eventTelemetry.Properties.Add("SalesInvoiceNumber", DocumentNumber);
        if (DocumentDate != null)
            eventTelemetry.Properties.Add("SalesInvoiceDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            eventTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            eventTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Patient?.Id != null)
            eventTelemetry.Properties.Add("PatientId", Patient.Id.ToString());
        if (Patient?.Code != null)
            eventTelemetry.Properties.Add("PatientCode", Patient.Code);
    }
}