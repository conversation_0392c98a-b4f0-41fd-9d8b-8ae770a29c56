﻿using WSA.Retail.Integration.PIM.Models.BundleSets;

namespace WSA.Retail.Integration.PIM.Models.Products;

public class PimProductBundleSetEntity : IPimProductBundleSetEntity, WSA.Retail.Integration.PIM.Core.IIdentifiable
{
    public required Guid Id { get; set; } = Guid.Empty;
    public required Guid ProductId { get; set; }
    public required Guid BundleSetId { get; set; }
    public bool? IsAvailable { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsPhasedOut { get; set; }
    public int? Ranking { get; set; }
    public string? Sku {  get; set; }
    public string? State { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public required DateTime SystemCreatedOn { get; set; }
    public required DateTime SystemModifiedOn { get; set; }

    public virtual PimProductEntity ProductEntity { get; set; } = null!;
    public virtual PimBundleSetEntity BundleSetEntity { get; set; } = null!;
}