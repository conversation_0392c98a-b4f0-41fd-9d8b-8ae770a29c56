﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.Models.PurchaseReceipts;

public class OrderLineItemAcceptedEventHubEvent : IIdentifiable
{
    public Guid Id { get; set; }
    public required OrderReference Order { get; set; }
    public string? DeliveryNumber { get; set; }
    public DateTime? ShippedDate { get; set; }
    public int? Quantity { get; set; }
    public string? Note { get; set; }
    public List<string>? SerialNumbers { get; set; }
}
