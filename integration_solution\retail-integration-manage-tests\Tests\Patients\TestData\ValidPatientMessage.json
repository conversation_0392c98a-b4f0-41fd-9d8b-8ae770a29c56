{"message": {"id": "77f93932-7d34-ef11-86c3-00224811a82d", "patientNumber": "AU-1", "originalPatientId": null, "patientLead": false, "firstName": "<PERSON>", "lastName": "<PERSON>", "middleName": "Test", "shortName": "<PERSON>", "dateOfBirth": "1953-05-16T00:00:00", "city": "Brighton", "state": "Victoria", "stateId": "3d2c38c0-7a55-ef11-991a-0022489617c1", "postcode": "3074", "mobilePhone": "04448824915", "homePhone": null, "workPhone": null, "preferredPhone": "MobilePhone", "faxNumber": null, "emailAddress": "<EMAIL>", "hadAppointmentOnly": null, "gpId": "3ca98d90-72c5-4b2b-50ba-08dc99aaf205", "gpPracticeId": "e8ba546f-8ddb-4f41-bbe0-08dc99aaaf89", "note": null, "employer": "", "occupation": "", "homeFlatNumber": "39 Bloom street", "street": "", "county": "", "address4": "", "address5": "", "suburb": "", "leadSourceId": null, "campaignId": "fc7d0a59-8e85-4b04-898f-08dc96a0ce27", "titleId": "8c3a93f9-de1c-ef11-86d2-002248983436", "genderId": "a23993f9-de1c-ef11-86d2-002248983436", "preferredLanguageId": "e43993f9-de1c-ef11-86d2-002248983436", "employmentStatusId": "9b3993f9-de1c-ef11-86d2-002248983436", "locationId": "e4e3b037-ed8f-4b31-a04e-838ade539e4b", "countryId": "f33893f9-de1c-ef11-86d2-002248983436", "isoCountryCode": "AU", "specialistId": "30f97c35-9751-4f49-eac1-08dc9417c4af", "patientStatusId": "883a93f9-de1c-ef11-86d2-002248983436", "initialOwner": null, "initialOwnerLocation": null, "contactPerson": {"name": "<PERSON>", "city": "", "state": null, "stateId": null, "postcode": "", "mobilePhone": "**********", "homePhone": null, "workPhone": null, "preferredPhone": "MobilePhone", "emailAddress": null, "relationshipToPatient": "Wife", "preferredContact": false, "address1": "", "address2": "", "county": "", "address4": "", "address5": "", "suburb": "", "countryId": null, "isoCountryCode": null, "country": null}, "nhs": {"isNhs": false, "number": null, "modificationDate": null, "ethnicityCodeId": null, "batteryFulfillmentPreferenceId": null}, "dva": {"isDva": false, "number": null, "cardType": "", "expireDate": null}}, "event": "Manage.Patients:PatientUpdated", "version": "v1", "domain": "Patients", "messageId": "01000000-8fce-de65-1084-08dd5731f20c", "timestamp": "2025-02-27T13:23:38.2369413Z", "headers": {"tenantId": "b4f1cf87-d54b-4887-a3f5-a515a59d7f02", "country": "Australia", "version": "**********"}}