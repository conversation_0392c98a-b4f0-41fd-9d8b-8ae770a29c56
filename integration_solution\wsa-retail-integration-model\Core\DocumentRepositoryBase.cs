﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Core;

public class DocumentRepositoryBase<TModel, TLineModel, TEntity, TLineEntity> (
    ILogger logger,
    IDbContextFactory<IntegrationContext> dbContextFactory,
    IDocumentQueryBuilder<TModel, TLineModel, TEntity, TLineEntity> queryBuilder,
    IDocumentMapper<TModel, TLineModel, TEntity, TLineEntity> mapper) :
    LoggingBase<TEntity>(logger)
    where TModel : class, IDocument<TLineModel>
    where TLineModel : class, IDocumentLine
    where TEntity : class, IDocumentEntity<TLineEntity>
    where TLineEntity : class, IDocumentLineEntity
{
    protected readonly IDbContextFactory<IntegrationContext> _dbContextFactory = dbContextFactory;
    protected readonly IDocumentQueryBuilder<TModel, TLineModel, TEntity, TLineEntity> _queryBuilder = queryBuilder;
    protected readonly IDocumentMapper<TModel, TLineModel, TEntity, TLineEntity> _mapper = mapper;

    public async Task<TModel?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null)
    {
        LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        return await _queryBuilder
            .BuildQuery(
                context: context,
                externalSystemCode: externalSystemCode,
                id: id,
                documentNumber: documentNumber,
                externalReference: externalReference)
            .FirstOrDefaultAsync();
    }

    public async Task<List<TModel>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        return await _queryBuilder
            .BuildQuery(
                context: context,
                externalSystemCode: externalSystemCode,
                id: id,
                documentNumber: documentNumber,
                externalReference: externalReference,
                documentDate: documentDate)
            .ToListAsync() ?? [];
    }

    public async Task<bool> InsertAsync(TModel domainEntity)
    {
        LogMethodStart();

        using var context = _dbContextFactory.CreateDbContext();
        var dbEntity = _mapper.ToEntity(domainEntity);

        try
        {
            context.Set<TEntity>().Add(dbEntity);
            LogCustomInformation($"Before inserting {typeof(TEntity).Name} record.", dbEntity);
            await context.SaveChangesAsync();
            LogCustomInformation($"Successfully inserted {typeof(TEntity).Name} record.", dbEntity);
            domainEntity.Id = dbEntity.Id;
            return true;
        }
        catch (DbUpdateException ex)
        {
            LogCustomError($"Encountered a database error while inserting {typeof(TModel).Name} record: {ex.Message}", dbEntity);
            return false;
        }
        catch (Exception ex)
        {
            LogCustomError(ex, dbEntity);
            return false;
        }
    }

    public async Task<bool> UpdateAsync(TModel domainModel)
    {
        LogMethodStart();

        // Make sure the domain model is valid
        if (domainModel.Id == Guid.Empty)
        {
            LogCustomWarning($"{typeof(TModel).Name} {domainModel.DocumentNumber} cannot be updated because it has a null or missing Id.");
            return false;
        }

        if (domainModel.ExternalSystemCode == null)
        {
            LogCustomWarning($"{typeof(TModel).Name} {domainModel.DocumentNumber} cannot be updated because it has a null or missing ExternalSystemCode.");
            return false;
        }

        // Get existing domain model to check for changes
        var existingDomainModel = await GetAsync(
            externalSystemCode: domainModel.ExternalSystemCode,
            id: domainModel.Id);

        // This shouldn't happen, but if it does, we insert instead of update
        if (existingDomainModel == null)
        {
            return await InsertAsync(domainModel);
        }

        // If there are no changes, we don't need to update
        if (!domainModel.HasChanges(existingDomainModel))
        {
            LogCustomWarning($"{typeof(TModel).Name} {domainModel.DocumentNumber} has no changes to update.");
            return true;
        }

        using var context = _dbContextFactory.CreateDbContext();
        var dbEntity = _mapper.ToEntity(domainModel);

        // Retrieve the existing db entity
        IQueryable<TEntity> query = context.Set<TEntity>();
        query = IncludeNavigationProperties(query);
        var existingDbEntity = await query.FirstOrDefaultAsync(x => x.Id == domainModel.Id);

        if (existingDbEntity == null)
        {
            LogCustomWarning($"{typeof(TModel).Name} with Id {domainModel.Id} not found to update.", dbEntity);
            return false;
        }

        // Update db entity fields
        context.Entry(existingDbEntity).CurrentValues.SetValues(dbEntity);
        foreach (var line in domainModel.Lines)
        {
            var existingLine = existingDomainModel.Lines.Where(x => x.Sequence == line.Sequence)
                .FirstOrDefault();
            var existingDbLine = existingDbEntity.Lines.Where(x => x.Sequence == line.Sequence)
                .FirstOrDefault();

            // New line - add it
            if (existingDbLine == null)
            {
                var newDbLine = dbEntity.Lines.FirstOrDefault(x => x.Sequence == line.Sequence);
                if (newDbLine != null)
                {
                    existingDbEntity.Lines.Add(newDbLine);
                }
            }
            else
            {
                if (line.HasChanges(existingLine))
                {
                    var newDbLine = dbEntity.Lines.FirstOrDefault(x => x.Sequence == line.Sequence);
                    if (newDbLine != null && existingDbLine != null)
                    {
                        // Update existing line
                        context.Entry(existingDbLine).CurrentValues.SetValues(newDbLine);
                    }
                }
            }
        }

        try
        {
            LogCustomInformation($"Before updating {typeof(TEntity).Name} record.", dbEntity);
            await context.SaveChangesAsync();
            LogCustomInformation($"Successfully updated {typeof(TEntity).Name} record.", dbEntity);
            return true;
        }
        catch (DbUpdateException ex)
        {
            LogCustomError($"A database error while updating {typeof(TModel).Name} record: {ex.Message}", dbEntity);
            return false;
        }
        catch (Exception ex)
        {
            LogCustomError(ex, dbEntity);
            return false;
        }
    }

    protected virtual IQueryable<TEntity> IncludeNavigationProperties(IQueryable<TEntity> query)
    {
        return query;
    }
}
