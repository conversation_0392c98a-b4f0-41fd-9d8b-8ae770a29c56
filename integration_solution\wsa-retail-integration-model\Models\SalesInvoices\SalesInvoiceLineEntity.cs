﻿using WSA.Retail.Integration.Models.Products;

namespace WSA.Retail.Integration.Models.SalesInvoices
{
    public class SalesInvoiceLineEntity : ISalesInvoiceLineEntity
    {
        public Guid Id { get; set; }
        public Guid SalesInvoiceId { get; set; }
        public int? Sequence { get; set; }
        public Guid? SalesOrderLineId { get; set; }
        public Guid? ProductId { get; set; }
        public string? Description { get; set; }
        public decimal? Quantity { get; set; }
        public string? SerialNumber { get; set; }
        public decimal? UnitPrice { get; set; }
        public decimal? GrossAmount { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal? AmountExclTax { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal? AmountInclTax { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }

        public required SalesInvoiceEntity SalesInvoiceEntity { get; set; }
        public ProductEntity? ProductEntity { get; set; }
    }
}