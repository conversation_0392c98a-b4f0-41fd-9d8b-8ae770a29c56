﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Patients;

public class PatientEntityConfiguration : IEntityTypeConfiguration<PatientEntity>
{
    public void Configure(EntityTypeBuilder<PatientEntity> builder)
    {
        builder.ToTable("Patient");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureMasterDataFields();
        builder.ConfigureAddressFields();
        builder.ConfigureContactFields();
        builder.ConfigureAuditInfoFields();

        builder.Property(x => x.IdentificationNumber)
            .HasColumnName("IdentificationNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(false);
    }
}