﻿CREATE TABLE dbo.SalesReconciliation (
    Id UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    ExternalSystemId UNIQUEIDENTIFIER NOT NULL,
    DocumentNumber NVARCHAR(20) NOT NULL,
    AlternateNumber NVARCHAR(20) NULL,
    DocumentDate DATE NULL,
    ClinicCode NVARCHAR(20) NULL,
    PatientCode NVARCHAR(20) NULL,
    AmountExclTax DECIMAL(18, 4) NULL,
    TaxAmount DECIMAL(18, 4) NULL,
    AmountInclTax DECIMAL(18, 4) NULL,
    CreatedOn DATETIME2 CONSTRAINT [DF_SalesReconciliation_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    ModifiedOn DATETIME2 CONSTRAINT [DF_SalesReconciliation_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT PK_SalesReconciliation PRIMARY KEY (Id)
);
GO

CREATE NONCLUSTERED INDEX IX_SalesReconciliation_ExternalSystemId_DocumentDate_DocumentNumber
ON dbo.SalesReconciliation (ExternalSystemId, DocumentDate, DocumentNumber);
GO

CREATE NONCLUSTERED INDEX IX_SalesReconciliation_ExternalSystemId_ModifiedOn
ON dbo.SalesReconciliation (ExternalSystemId, ModifiedOn);
GO