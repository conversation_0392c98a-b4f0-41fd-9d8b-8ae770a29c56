using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Models.SalesCredits;
using WSA.Retail.Integration.Models.Claims;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplySalesProcessor> logger,
    ISimplySalesHeaderRepository simplySalesHeaderRepository,
    ISalesInvoiceService salesInvoiceService,
    ISalesCreditService salesCreditService,
    IClaimService claimService)
    : ISimplySalesProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplySalesProcessor> _logger = logger;
    private readonly ISimplySalesHeaderRepository _simplySalesHeaderRepository = simplySalesHeaderRepository;
    private readonly ISalesInvoiceService _salesInvoiceService = salesInvoiceService;
    private readonly ISalesCreditService _salesCreditService = salesCreditService;
    private readonly IClaimService _claimService = claimService;

    public enum SalesType
    {
        SI,
        CI
    }

    public async Task ProcessSimplySalesTransactionsAsync()
    {
        _logger.LogMethodStart();

        var records = await _simplySalesHeaderRepository.GetIntegrationRecordsAsync();
        if (records == null || records.Count == 0)
        {
            _logger.LogCustomInformation("No sales records found for integration");
            return;
        }

        _logger.LogCustomInformation($"Found {records.Count} sales records for integration");
        await Parallel.ForEachAsync(
            records,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (record, cancellationToken) =>
            {
                await ProcessSimplySalesTransactionAsync(record);
            });

    }

    public async Task<bool> ProcessSimplySalesTransactionAsync(SimplySalesHeaderEntity record)
    {
        _logger.LogMethodStart();
        if (record.SalesType == SalesType.SI.ToString())
        {
            if (record.Details != null)
            {
                bool negQuantities = record.Details.All(x => x.Quantity < 0);
                if (negQuantities)
                {
                    _logger.LogCustomError($"Sales invoice {record.InvoiceNumber} has negative quantities, tread as credit");
                    return await ProcessSimplySalesCreditAsync(record);
                }
            }    

            return await ProcessSimplySalesInvoiceAsync(record);
        }

        if (record.SalesType == SalesType.CI.ToString())
        {
            return await ProcessSimplySalesCreditAsync(record);
        }

        return false;
    }

    public async Task<bool> ProcessSimplySalesInvoiceAsync(SimplySalesHeaderEntity record)
    {
        _logger.LogMethodStart();
        var salesInvoice = record.ToSalesInvoice();
        salesInvoice.ExternalSystemCode = _appSettings.ExternalSystemCode;

        // Set document number
        salesInvoice.DocumentNumber = GetDocumentNumber(SalesType.SI, record.InvoiceNumber);

        // Add detail lines
        decimal ohsBenefitAmount = 0;
        if (record.Details != null)
        {
            foreach (var detail in record.Details)
            {
                var line = detail.ToSalesInvoiceLine();

                if ((detail.OHSBenefitAmountInclGST ?? 0) != 0)
                {
                    ohsBenefitAmount += detail.OHSBenefitAmountInclGST ?? 0;
                }
                salesInvoice.SalesInvoiceLines.Add(line);
            }
        }

        // Resolve external references
        await _salesInvoiceService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, salesInvoice);

        // Verify required data
        bool isValid = VerifyRequiredData(salesInvoice);
        if (!isValid)
        {
            _logger.LogCustomError($"Sales invoice {salesInvoice.DocumentNumber} has missing required data");
            return false;
        }

        int claimCount = 0;
        if (ohsBenefitAmount != 0)
        {
            var claim = new Claim
            {
                ExternalSystemCode = _appSettings.ExternalSystemCode,
                DocumentNumber = salesInvoice.DocumentNumber,
                DocumentDate = salesInvoice.DocumentDate,
                ExternalReference = salesInvoice.ExternalReference,
                Patient = salesInvoice.Patient,
                Clinic = salesInvoice.Clinic,
                AppliesTo = salesInvoice.AlternateNumber,
                Amount = ohsBenefitAmount,
                Payor = new()
                {
                    ExternalCode = Environment.GetEnvironmentVariable("OHSPayorNo")
                }
            };
            salesInvoice.Claims ??= [];

            claimCount++;
            claim.DocumentNumber = $"{claim.DocumentNumber}-{claimCount}";
            salesInvoice.Claims.Add(claim);
        }

        if (record.Funders?.Count > 0)
        {
            salesInvoice.Claims ??= [];
            foreach (var item in record.Funders)
            {
                var payorNo = item.FunderID.ToString();

                var existingClaim = salesInvoice.Claims.Where(x => x?.Payor?.Code == payorNo).FirstOrDefault();
                if (existingClaim != null)
                {
                    existingClaim.Amount += item.Amount;
                }
                else
                {
                    var claim = new Claim
                    {
                        ExternalSystemCode = salesInvoice.ExternalSystemCode,
                        DocumentNumber = salesInvoice.DocumentNumber,
                        DocumentDate = salesInvoice.DocumentDate,
                        ExternalReference = salesInvoice.ExternalReference,
                        Patient = salesInvoice.Patient,
                        Clinic = salesInvoice.Clinic,
                        AppliesTo = salesInvoice.AlternateNumber,
                        Amount = item.Amount,
                        Payor = new()
                        {
                            ExternalCode = item.FunderID.ToString()
                        }
                    };

                    claimCount++;
                    claim.DocumentNumber = $"{claim.DocumentNumber}-{claimCount}";
                    salesInvoice.Claims.Add(claim);
                }
            }
        }

        if (salesInvoice.Claims.Count > 0)
        {
            foreach (var claim in salesInvoice.Claims)
            {
                await _claimService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, claim);
            }
        }

        // Upsert the sales invoice
        var result = await _salesInvoiceService.UpsertAsync(salesInvoice);
        if (result != null)
        {
            // Update integration status
            await _simplySalesHeaderRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed sales invoice {salesInvoice.DocumentNumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process sales invoice {salesInvoice.DocumentNumber}");
            return false;
        }
    }

    public async Task<bool> ProcessSimplySalesCreditAsync(SimplySalesHeaderEntity record)
    {
        _logger.LogMethodStart();
        var salesCredit = record.ToSalesCredit();
        salesCredit.ExternalSystemCode = _appSettings.ExternalSystemCode;

        // Set document number
        salesCredit.DocumentNumber = GetDocumentNumber(SalesType.CI, record.InvoiceNumber);

        // Add detail lines
        decimal ohsBenefitAmount = 0;
        if (record.Details != null)
        {
            foreach (var detail in record.Details)
            {
                var line = detail.ToSalesCreditLine();

                if ((detail.OHSBenefitAmountInclGST ?? 0) != 0)
                {
                    ohsBenefitAmount += detail.OHSBenefitAmountInclGST ?? 0;
                }
                salesCredit.SalesCreditLines.Add(line);
            }
        }

        // Resolve external references
        await _salesCreditService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, salesCredit);

        // Verify required data
        bool isValid = VerifyRequiredData(salesCredit);
        if (!isValid)
        {
            _logger.LogCustomError($"Sales credit {salesCredit.DocumentNumber} has missing required data");
            return false;
        }

        int claimCount = 0;
        if (ohsBenefitAmount != 0)
        {
            var claim = new Claim
            {
                ExternalSystemCode = _appSettings.ExternalSystemCode,
                DocumentNumber = salesCredit.DocumentNumber,
                DocumentDate = salesCredit.DocumentDate,
                ExternalReference = salesCredit.ExternalReference,
                Patient = salesCredit.Patient,
                Clinic = salesCredit.Clinic,
                AppliesTo = salesCredit.AlternateNumber,
                Amount = -ohsBenefitAmount,
                Payor = new()
                {
                    ExternalCode = Environment.GetEnvironmentVariable("OHSPayorNo")
                }
            };
            salesCredit.Claims ??= [];

            claimCount++;
            claim.DocumentNumber = $"{claim.DocumentNumber}-{claimCount}";
            salesCredit.Claims.Add(claim);
        }

        if (record.Funders?.Count > 0)
        {
            salesCredit.Claims ??= [];
            foreach (var item in record.Funders)
            {
                var payorNo = item.FunderID.ToString();

                var existingClaim = salesCredit.Claims.Where(x => x?.Payor?.Code == payorNo).FirstOrDefault();
                if (existingClaim != null)
                {
                    existingClaim.Amount += item.Amount;
                }
                else
                {
                    var claim = new Claim
                    {
                        ExternalSystemCode = salesCredit.ExternalSystemCode,
                        DocumentNumber = salesCredit.DocumentNumber,
                        DocumentDate = salesCredit.DocumentDate,
                        ExternalReference = salesCredit.ExternalReference,
                        Patient = salesCredit.Patient,
                        Clinic = salesCredit.Clinic,
                        AppliesTo = salesCredit.AlternateNumber,
                        Amount = -item.Amount,
                        Payor = new()
                        {
                            ExternalCode = item.FunderID.ToString()
                        }
                    };

                    claimCount++;
                    claim.DocumentNumber = $"{claim.DocumentNumber}-{claimCount}";
                    salesCredit.Claims.Add(claim);
                }
            }
        }

        if (salesCredit.Claims.Count > 0)
        {
            foreach (var claim in salesCredit.Claims)
            {
                await _claimService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, claim);
            }
        }

        // Upsert the sales invoice
        var result = await _salesCreditService.UpsertAsync(salesCredit);
        if (result != null)
        {
            // Update integration status
            await _simplySalesHeaderRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed sales credit {salesCredit.DocumentNumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process sales credit {salesCredit.DocumentNumber}");
            return false;
        }
    }

    private static string GetDocumentNumber(SalesType type, string documentNumber)
    {
        string prefix = type == SalesType.SI ? "SINV-" : "SCM-";
        return $"{prefix}{documentNumber.ToString().PadLeft(10, '0')}";
    }

    private bool VerifyRequiredData(SalesInvoice salesInvoice)
    {
        if (string.IsNullOrEmpty(salesInvoice.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }

        if (salesInvoice.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }

        if (salesInvoice.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (salesInvoice.Patient?.Code == null)
        {
            _logger.LogCustomError("Patient.Code is required");
            return false;
        }

        if (salesInvoice.SalesInvoiceLines == null || salesInvoice.SalesInvoiceLines.Count == 0)
        {
            _logger.LogCustomError("At least one sales inovice line is required");
            return false;
        }

        foreach (var line in salesInvoice.SalesInvoiceLines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }

            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }

        return true;
    }

    private bool VerifyRequiredData(SalesCredit salesCredit)
    {
        if (string.IsNullOrEmpty(salesCredit.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }

        if (salesCredit.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }

        if (salesCredit.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (salesCredit.Patient?.Code == null)
        {
            _logger.LogCustomError("Patient.Code is required");
            return false;
        }

        if (salesCredit.SalesCreditLines == null || salesCredit.SalesCreditLines.Count == 0)
        {
            _logger.LogCustomError("At least one sales credit line is required");
            return false;
        }

        foreach (var line in salesCredit.SalesCreditLines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }

            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }

        return true;
    }

}