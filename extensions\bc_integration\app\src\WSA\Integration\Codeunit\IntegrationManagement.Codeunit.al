namespace WSA.Integration;

using System.Azure.Identity;
using System.Environment;
using Microsoft.Purchases.Document;
using System.Reflection;
using WSA.Integration.API.V1;
using Microsoft.Finance.Dimension;
using Microsoft.Inventory.Item;
using Microsoft.Purchases.Vendor;
using Microsoft.Sales.Customer;
using System.IO;


codeunit 50134 "Integration Management"
{
    trigger OnRun()
    begin

    end;


    [EventSubscriber(ObjectType::Table, Database::"WSA Integration Request Log", 'OnAfterInsertEvent', '', false, false)]
    local procedure OnAfterInsert(var Rec: Record "WSA Integration Request Log")
    var
        Handler: Interface "WSA Integration Request";

    begin
        if Rec.IsTemporary() then
            exit;

        if Rec."Type".AsInteger() = 0 then
            exit;

        Rec.Status := Rec.Status::ready;
        Rec.Modify(false);
        Commit();

        Handler := Rec."Type";
        Handler.HandleRequest(Rec);
    end;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Config. Template Management", 'OnInsertTemplateCaseElse', '', false, false)]
    local procedure ApplyDefaultDimentionsOnInsertTemplateCaseElse(
        var ConfigTemplateLine: Record "Config. Template Line";
        var ConfigTemplateHeader: Record "Config. Template Header";
        FldRef: FieldRef;
        var RecRef: RecordRef;
        SkipFields: Boolean;
        var TempSkipField: record Field;
        OldRecRef: RecordRef)

    var
        RelatedTemplate: Record "Config. Template Header";
        RelatedTemplateLine: Record "Config. Template Line";
        DefaultDimension: Record "Default Dimension";
        DefaultDimension2: Record "Default Dimension";
        KeyRef: KeyRef;
        FieldRef: FieldRef;

    begin
        if not (ConfigTemplateLine.Type = ConfigTemplateLine.Type::"Related Template") then
            exit;

        if not RelatedTemplate.Get(ConfigTemplateLine."Template Code") then
            exit;

        if not (RelatedTemplate."Table ID" = Database::"Default Dimension") then
            exit;

        if OldRecRef.Number = 0 then
            exit;

        KeyRef := OldRecRef.KeyIndex(1);
        FieldRef := KeyRef.FieldIndex(1);
        if Format(FieldRef.Value) = '' then
            exit;

        RelatedTemplateLine.SetRange("Data Template Code", RelatedTemplate.Code);
        RelatedTemplateLine.SetRange("Table ID", Database::"Default Dimension");

        DefaultDimension.Init();
        DefaultDimension."Table ID" := OldRecRef.Number;
        DefaultDimension."No." := FieldRef.Value;

        RelatedTemplateLine.SetRange("Field ID", DefaultDimension.FieldNo("Dimension Code"));
        if RelatedTemplateLine.FindFirst() then
            DefaultDimension."Dimension Code" := RelatedTemplateLine."Default Value";

        if DefaultDimension."Dimension Code" = '' then
            exit;

        RelatedTemplateLine.SetRange("Field ID", DefaultDimension.FieldNo("Dimension Value Code"));
        if RelatedTemplateLine.FindFirst() then
            DefaultDimension."Dimension Value Code" := RelatedTemplateLine."Default Value";

        DefaultDimension2.SetRange("Table ID", DefaultDimension."Table ID");
        DefaultDimension2.SetRange("No.", DefaultDimension."No.");
        DefaultDimension2.SetRange("Dimension Code", DefaultDimension."Dimension Code");
        if DefaultDimension2.IsEmpty() then
            DefaultDimension.Insert();
    end;

    procedure GetBaseUrl(): Text
    var
        EnvironmentInfo: Codeunit "Environment Information";
        AzureADTentat: Codeunit "Azure AD Tenant";
        BaseUrlTxt: Label 'https://api.businesscentral.dynamics.com/v2.0/%1/%2/api';

    begin
        exit(StrSubstNo(BaseUrlTxt, AzureADTentat.GetAadTenantId(), EnvironmentInfo.GetEnvironmentName()));
    end;


    procedure GetExternalSystemCode(): Text
    var
        EnvironmentInfo: Codeunit "Environment Information";
        AzureADTentat: Codeunit "Azure AD Tenant";
        BaseUrlTxt: Label 'https://businesscentral.dynamics.com/%1/companies(%2)';

    begin
        exit(StrSubstNo(
                BaseUrlTxt,
                EnvironmentInfo.GetEnvironmentName().ToLower(),
                Format(CompanyProperty.ID()).TrimStart('{').TrimEnd('}').ToLower()
                ));
    end;

    [TryFunction]
    procedure TryRaiseEvent(var JObject: JsonObject; Topic: Text; SubTopic: Text)
    begin
        RaiseEvent(JObject, Topic, SubTopic);
    end;

    local procedure RaiseEvent(var JObject: JsonObject; Topic: Text; SubTopic: Text)
    var
        Client: HttpClient;
        Content: HttpContent;
        ContentHeaders: HttpHeaders;
        ResponseMessage: HttpResponseMessage;
        HeaderJson: JsonObject;
        JsonText: Text;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        RetailIntegrationSetup.TestField("External System Code");
        RetailIntegrationSetup.TestField("Event Grid Topic Endpoint");
        RetailIntegrationSetup.TestField("Event Grid Access Key");

        Client.SetBaseAddress(RetailIntegrationSetup."Event Grid Topic Endpoint".TrimEnd('/') + '/');
        Client.DefaultRequestHeaders.Add('aeg-sas-key', RetailIntegrationSetup."Event Grid Access Key");

        HeaderJson.Add('id', Format(CreateGuid()).TrimStart('{').TrimEnd('}'));
        HeaderJson.Add('source', RetailIntegrationSetup."External System Code");
        HeaderJson.Add('specversion', '1.0');
        HeaderJson.Add('type', Topic);
        HeaderJson.add('subject', SubTopic);
        HeaderJson.Add('time', Format(CurrentDateTime(), 0, 9));
        HeaderJson.Add('data', JObject);

        HeaderJson.WriteTo(JsonText);
        Content.WriteFrom(JsonText);
        Content.GetHeaders(ContentHeaders);
        ContentHeaders.Clear();
        ContentHeaders.Add('Content-Type', 'application/cloudevents+json');

        Client.Post('', Content, ResponseMessage);
        if not ResponseMessage.IsSuccessStatusCode() then
            exit;
    end;


    [TryFunction]
    procedure TrySendToApi(var JObject: JsonObject; Path: Text)
    begin
        SendToAPI(JObject, Path);
    end;


    local procedure SendToAPI(var JObject: JsonObject; Path: Text)
    var
        Client: HttpClient;
        Content: HttpContent;
        ContentHeaders: HttpHeaders;
        ResponseMessage: HttpResponseMessage;
        JObject2: JsonObject;
        JsonText: Text;
        BaseUrl: Text;


    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        BaseUrl := GetBaseUrl();
        Client.SetBaseAddress(RetailIntegrationSetup."API Base URL".TrimEnd('/') + '/');
        Client.DefaultRequestHeaders.Add('X-ExternalSystemCode', GetExternalSystemCode());
        if RetailIntegrationSetup."API Key" <> '' then
            Client.DefaultRequestHeaders.Add('Ocp-Apim-Subscription-Key', RetailIntegrationSetup."API Key");

        JObject.WriteTo(JsonText);
        Content.WriteFrom(JsonText);
        Content.GetHeaders(ContentHeaders);
        ContentHeaders.Clear();
        ContentHeaders.Add('Content-Type', 'application/json');

        Client.Post(Path.TrimStart('/'), Content, ResponseMessage);
        if not ResponseMessage.IsSuccessStatusCode() then
            exit;
    end;


    local procedure GetFromAPI(var ResponseBody: Text; Path: Text): Boolean
    var
        Client: HttpClient;
        ResponseMessage: HttpResponseMessage;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        Client.SetBaseAddress(RetailIntegrationSetup."API Base URL".TrimEnd('/') + '/');
        Client.DefaultRequestHeaders.Add('X-ExternalSystemCode', RetailIntegrationSetup."External System Code");
        if RetailIntegrationSetup."API Key" <> '' then
            Client.DefaultRequestHeaders.Add('Ocp-Apim-Subscription-Key', RetailIntegrationSetup."API Key");

        if not Client.Get(Path.TrimStart('/'), ResponseMessage) then
            exit(false);

        if not ResponseMessage.IsSuccessStatusCode() then
            exit(false);

        ResponseMessage.Content.ReadAs(ResponseBody);
        if ResponseBody = '' then
            exit(false);

        exit(true);
    end;


    procedure GetPatient(Code: Code[20]; var Customer: Record Customer): Boolean
    var
        Request: Record "WSA Integration Request Log";
        responseBody: Text;
        jObject: JsonObject;
        jToken: JsonToken;
        jArray: JsonArray;

    begin
        if not GetFromAPI(responseBody, StrSubstNo('patients?code=%1', Code)) then
            exit(false);

        jArray.ReadFrom(responseBody);
        jArray.Get(0, jToken);
        jToken.WriteTo(responseBody);

        Request := InitRequestLog(Request.Type::patients, Request.Method::post, responseBody);
        Request.Insert(true);
        Request.Get(Request."Entry No.");
        if Request.Status = Request.Status::created then
            exit(Customer.Get(Code));
    end;


    procedure GetVendor(Code: Code[20]; var Vendor: Record Vendor): Boolean
    var
        Request: Record "WSA Integration Request Log";
        responseBody: Text;
        jObject: JsonObject;
        jToken: JsonToken;
        jArray: JsonArray;

    begin
        if not GetFromAPI(responseBody, StrSubstNo('vendors?code=%1', Code)) then
            exit(false);

        jArray.ReadFrom(responseBody);
        jArray.Get(0, jToken);
        jToken.WriteTo(responseBody);

        Request := InitRequestLog(Request.Type::vendors, Request.Method::post, responseBody);
        Request.Insert(true);
        Request.Get(Request."Entry No.");
        if Request.Status = Request.Status::created then
            exit(Vendor.Get(Code));
    end;


    procedure GetProduct(Code: Code[20]; var Item: Record Item): Boolean
    var
        Request: Record "WSA Integration Request Log";
        responseBody: Text;
        jObject: JsonObject;
        jToken: JsonToken;
        jArray: JsonArray;

    begin
        if not GetFromAPI(responseBody, StrSubstNo('products?code=%1', Code)) then
            exit(false);

        jArray.ReadFrom(responseBody);
        jArray.Get(0, jToken);
        jToken.WriteTo(responseBody);

        Request := InitRequestLog(Request.Type::products, Request.Method::post, responseBody);
        Request.Insert(true);
        Request.Get(Request."Entry No.");
        if Request.Status = Request.Status::created then
            exit(Item.Get(Code));
    end;


    procedure GetPurchaseOrder(Code: Code[20]; var PurchaseOrder: Record "Purchase Header"): Boolean
    var
        Request: Record "WSA Integration Request Log";
        responseBody: Text;
        jObject: JsonObject;
        jToken: JsonToken;
        jArray: JsonArray;

    begin
        if not GetFromAPI(responseBody, StrSubstNo('purchaseOrders?documentNumber=%1', Code)) then
            exit(false);

        jArray.ReadFrom(responseBody);
        jArray.Get(0, jToken);
        jToken.WriteTo(responseBody);

        Request := InitRequestLog(Request.Type::purchaseOrders, Request.Method::post, responseBody);
        Request.Insert(true);
        Request.Get(Request."Entry No.");
        if Request.Status = Request.Status::created then
            exit(PurchaseOrder.Get(PurchaseOrder."Document Type"::Order, Code));
    end;


    local procedure InitRequestLog(
        RequestType: Enum "WSA Request Type";
        RequestMethod: Enum "WSA Request Method";
        responseBody: Text): Record "WSA Integration Request Log"

    var
        Request: Record "WSA Integration Request Log";
        requestStream: OutStream;

    begin
        Request.Init();
        Request.Type := RequestType;
        Request.Method := RequestMethod;
        Request."Request Content".CreateOutStream(requestStream);
        requestStream.Write(responseBody);
        Request.Status := Request.Status::ready;
        exit(Request);
    end;

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

}
