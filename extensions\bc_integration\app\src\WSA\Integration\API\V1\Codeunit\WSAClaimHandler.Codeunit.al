namespace WSA.Integration.API.V1;

using Microsoft.Finance.GeneralLedger.Journal;
using Microsoft.Finance.GeneralLedger.Posting;
using Microsoft.Sales.Document;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Receivables;
using Microsoft.Inventory.Tracking;
using System.Utilities;
using WSA.Integration;


codeunit 50127 "WSA Claim Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Claim Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        salesInvoice: Record "Sales Header";

    begin
        if not TryHandlePost(Request, salesInvoice) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var SalesHeader: Record "Sales Header")

    var
        //salesInvoiceHeader: Record "Sales Invoice Header";
        genJournalLine: Record "Gen. Journal Line";
        customer: Record "Customer";
        custLedgEntry: Record "Cust. Ledger Entry";
        ApiCommon: Codeunit "WSA API Common";
        json: JsonObject;
        claimAmount: Decimal;
        firstLineNo: Integer;
        lastLineNo: Integer;
        postingDescription: Text[100];

    begin
        json := Common.GetJsonFromBlob(Request);
        UpdateRequestLog(Request, json);

        TempErrorMessage.ClearLog();
        ValidateExternalReferences(json);
        if TempErrorMessage.HasErrors(false) then begin
            TempErrorMessage.ThrowError();
        end;

        if IsDuplicate(json) then begin
            Common.SetSkippedResponse(Request, 'Duplicate claim');
            exit
        end;

        claimAmount := Common.GetJsonValue(json, '$.amount').AsDecimal();
        if claimAmount = 0 then begin
            Common.SetNoContentResponse(Request);
            exit;
        end;

        TempErrorMessage.ClearLog();
        if not InitGenJournalLine(json, genJournalLine) then begin
            if TempErrorMessage.HasErrors(false) then
                TempErrorMessage.ThrowError();

            Common.SetErrorResponse(Request, GetLastErrorText());
            exit
        end;
        if TempErrorMessage.HasErrors(false) then begin
            TempErrorMessage.ThrowError();
        end;

        genJournalLine.Validate("Document No.", Common.GetJsonValue(json, '$.documentNumber').AsCode());
        SetDates(json, genJournalLine);
        genJournalLine.Validate("WSA Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
        SetPatient(json, genJournalLine, customer);
        postingDescription := StrSubstNo('Claim for %1', customer.Name);
        SetExternalDocumentNo(json, genJournalLine);
        genJournalLine.Validate(amount, -claimAmount);
        genJournalLine.Validate("Description", postingDescription);

        genJournalLine.Insert();
        firstLineNo := genJournalLine."Line No.";

        genJournalLine."Line No." += 10000;
        SetPayor(json, genJournalLine);
        genJournalLine.Validate(amount, -genJournalLine.Amount);
        genJournalLine.Validate("Description", postingDescription);
        genJournalLine.Insert();
        lastLineNo := genJournalLine."Line No.";

        genJournalLine.Reset();
        genJournalLine.SetRange("Journal Template Name", genJournalLine."Journal Template Name");
        genJournalLine.SetRange("Journal Batch Name", genJournalLine."Journal Batch Name");
        genJournalLine.SetFilter("Line No.", '%1..%2', firstLineNo, lastLineNo);

        if PostClaim(genJournalLine, custLedgEntry) then begin
            ApiCommon.ApplyCustLedgEntries(custLedgEntry."Customer No.", custLedgEntry."Document No.", custLedgEntry."External Document No.");
            Common.SetCreatedResponse(Request, Common.PaymentToJson(custLedgEntry))
        end else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure UpdateRequestLog(var Request: Record "WSA Integration Request Log"; json: JsonObject)
    var
        recRef: RecordRef;

    begin
        recRef.GetTable(Request);
        Common.ValidateFieldFromJson(Json, '$.documentNumber', recRef, Request.FieldNo("Document No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.documentDate', recRef, Request.FieldNo("Document Date"), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.clinic.code', recRef, Request.FieldNo("Clinic No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.patient.code', recRef, Request.FieldNo("Patient No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.payor.code', recRef, Request.FieldNo("Payor No."), TempFieldSet);
        recRef.SetTable(Request);

        Request."Claim No." := Request."Document No.";

        if Request.Modify() then
            Commit();
    end;


    local procedure InitGenJournalLine(
        json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line"): Boolean

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        GenJouranlTemplate: Record "Gen. Journal Template";

    begin
        RetailIntegrationSetup.SafeGet();
        if RetailIntegrationSetup."Claim Journal Template" = '' then
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, 'Claim Journal Template is not defined in Retail Integration Setup.');

        if RetailIntegrationSetup."Claim Journal Batch" = '' then
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, 'Claim Journal Batch is not defined in Retail Integration Setup.');

        if TempErrorMessage.HasErrors(false) then
            exit(false);

        GenJouranlTemplate.Get(RetailIntegrationSetup."Claim Journal Template");

        GenJournalLine.Init();
        GenJournalLine.Validate("Journal Template Name", RetailIntegrationSetup."Claim Journal Template");
        GenJournalLine.Validate("Journal Batch Name", RetailIntegrationSetup."Claim Journal Batch");
        GenJournalLine.Validate("Line No.", GetLastLineNo(
            RetailIntegrationSetup."Claim Journal Template",
            RetailIntegrationSetup."Claim Journal Batch") + 10000);
        GenJournalLine.Validate("Source Code", GenJouranlTemplate."Source Code");
        exit(true);
    end;


    local procedure GetLastLineNo(
        TemplateCode: Code[10];
        BatchCode: Code[10]): Integer

    var
        genJournalLine: Record "Gen. Journal Line";

    begin
        genJournalLine.SetRange("Journal Template Name", TemplateCode);
        genJournalLine.SetRange("Journal Batch Name", BatchCode);
        if genJournalLine.FindLast() then
            exit(genJournalLine."Line No.")
        else
            exit(0);
    end;


    local procedure SetDates(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if newDate <> 0D then begin
            GenJournalLine.Validate("Posting Date", newDate);
            GenJournalLine.Validate("Document Date", newDate);
        end;
    end;


    local procedure SetPayor(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    begin
        GenJournalLine.Validate("Account Type", GenJournalLine."Account Type"::Customer);
        GenJournalLine.Validate("Account No.", Common.GetJsonValue(json, '$.payor.code').AsCode());
    end;


    local procedure SetPatient(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line";
        var Customer: Record "Customer")

    begin
        GenJournalLine.Validate("Account Type", GenJournalLine."Account Type"::Customer);
        GenJournalLine.Validate("Account No.", Common.GetJsonValue(json, '$.patient.code').AsCode());

        Customer.Get(GenJournalLine."Account No.");
    end;


    local procedure SetExternalDocumentNo(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.appliesTo');
        if not jValue.IsNull then begin
            GenJournalLine.Validate("External Document No.", jValue.AsCode());
            exit;
        end;

        Clear(jValue);
        jValue := Common.GetJsonValue(json, '$.referenceNumber');
        if not jValue.IsNull then begin
            GenJournalLine.Validate("External Document No.", jValue.AsCode());
            exit;
        end;
    end;


    local procedure SetAppliesTo(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        CustLedgEntry: Record "Cust. Ledger Entry";
        appliesToValue: JsonValue;
        amountValue: JsonValue;
        documentType: Enum "Gen. Journal Document Type";

    begin
        appliesToValue := Common.GetJsonValue(json, '$.appliesTo');
        if appliesToValue.IsNull then
            exit;

        if appliesToValue.AsCode() = '' then
            exit;

        amountValue := Common.GetJsonValue(json, '$.amount');
        if amountValue.IsNull then
            exit;

        if amountValue.AsDecimal() = 0 then
            exit;

        if amountValue.AsDecimal() > 0 then
            documentType := GenJournalLine."Document Type"::Invoice
        else
            documentType := GenJournalLine."Document Type"::"Credit Memo";

        if documentType = "Gen. Journal Document Type"::Invoice then begin
            CustLedgEntry.SetRange("Customer No.", GenJournalLine."Account No.");
            CustLedgEntry.SetRange("Document Type", CustLedgEntry."Document Type"::Invoice);
            CustLedgEntry.SetRange("Document No.", appliesToValue.AsCode());
            CustLedgEntry.SetRange(Positive, true);
            CustLedgEntry.SetRange(Open, true);
            if CustLedgEntry.FindFirst() then begin
                CustLedgEntry.CalcFields("Remaining Amount");
                if CustLedgEntry."Remaining Amount" - amountValue.AsDecimal() < 0 then
                    exit;
                GenJournalLine.Validate("Applies-to Doc. No.", CustLedgEntry."Document No.");
                GenJournalLine.Validate("Applies-to Doc. Type", CustLedgEntry."Document Type");
                exit;
            end;
        end;

        if documentType = "Gen. Journal Document Type"::"Credit Memo" then begin
            CustLedgEntry.SetRange("Customer No.", GenJournalLine."Account No.");
            CustLedgEntry.SetRange("Document Type", CustLedgEntry."Document Type"::"Credit Memo");
            CustLedgEntry.SetRange("Document No.", appliesToValue.AsCode());
            CustLedgEntry.SetRange(Positive, false);
            CustLedgEntry.SetRange(Open, true);
            if CustLedgEntry.FindFirst() then begin
                CustLedgEntry.CalcFields("Remaining Amount");
                if CustLedgEntry."Remaining Amount" - amountValue.AsDecimal() > 0 then
                    exit;
                GenJournalLine.Validate("Applies-to Doc. No.", CustLedgEntry."Document No.");
                GenJournalLine.Validate("Applies-to Doc. Type", CustLedgEntry."Document Type");
                exit;
            end;
        end;
    end;

    local procedure PostClaim(
        var GenJournalLine: Record "Gen. Journal Line";
        var CustLedgEntry: Record "Cust. Ledger Entry") Ok: Boolean

    var
        GenJournalLine2: Record "Gen. Journal Line";

    begin
        ClearLastError();
        GenJournalLine2 := GenJournalLine;

        GenJournalLine.SendToPosting(Codeunit::"Gen. Jnl.-Post Batch");
        CustLedgEntry.SetRange("Document Type", GenJournalLine2."Document Type");
        CustLedgEntry.SetRange("Document No.", GenJournalLine2."Document No.");
        exit(CustLedgEntry.FindLast());
        exit(false);
    end;


    local procedure ValidateExternalReferences(json: JsonObject)
    begin
        ValidatePatient(json);
        ValidatePayor(json);
        ValidateClinic(json);
    end;


    local procedure ValidatePatient(json: JsonObject): Boolean
    var
        PatientHandler: Codeunit "WSA Patient Handler";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.patient.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.patient.code is missing from request json.');
            exit(false);
        end;

        exit(PatientHandler.ValidatePatient(jValue.AsCode(), TempErrorMessage));
    end;


    local procedure ValidatePayor(json: JsonObject): Boolean
    var
        PayorHandler: Codeunit "WSA Payor Handler";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.payor.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.payor.code is missing from request json.');
            exit(false);
        end;

        exit(PayorHandler.ValidatePayor(jValue.AsCode(), TempErrorMessage));
    end;


    local procedure ValidateClinic(json: JsonObject): Boolean
    var
        ClinicHandler: Codeunit "WSA Clinic Handler";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.clinic.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.clinic.code is missing from request json.');
            exit(false);
        end;

        exit(ClinicHandler.ValidateClinic(jValue.AsCode(), TempErrorMessage));
    end;


    local procedure IsDuplicate(json: JsonObject): Boolean
    var
        CustLedgEntry: Record "Cust. Ledger Entry";
        DocumentNo: Code[20];
        PayorNo: Code[20];

    begin
        DocumentNo := Common.GetJsonValue(json, '$.documentNumber').AsCode();
        PayorNo := Common.GetJsonValue(json, '$.payor.code').AsCode();

        CustLedgEntry.SetRange("Customer No.", PayorNo);
        CustLedgEntry.SetRange("Document No.", DocumentNo);
        CustLedgEntry.SetRange(Reversed, false);
        exit(not CustLedgEntry.IsEmpty());
    end;


    local procedure CleanupAfterFailure(DocumentNo: Code[20])
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        GenJournalLine: Record "Gen. Journal Line";
        ReservationEntry: Record "Reservation Entry";
        SalesLine: Record "Sales Line";

    begin
        RetailIntegrationSetup.SafeGet();
        if RetailIntegrationSetup."Claim Journal Template" = '' then
            exit;

        if RetailIntegrationSetup."Claim Journal Batch" = '' then
            exit;

        GenJournalLine.SetRange("Journal Template Name", RetailIntegrationSetup."Claim Journal Template");
        GenJournalLine.SetRange("Journal Batch Name", RetailIntegrationSetup."Claim Journal Batch");
        GenJournalLine.SetRange("Document No.", DocumentNo);
        if not GenJournalLine.IsEmpty() then
            GenJournalLine.DeleteAll();

        Commit();
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        TempErrorMessage: Record "Error Message" temporary;
        Common: Codeunit "WSA Common";
}
