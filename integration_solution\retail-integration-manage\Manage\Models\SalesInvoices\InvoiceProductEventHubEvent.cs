﻿namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class InvoiceProductEventHubEvent
{
    public Guid Id { get; set; }
    public Guid? StockProductId { get; set; }
    public Guid? StockProductItemId { get; set; }
    public string? SerialNumber { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal DiscountAmount { get; set; }
    public string? Name { get; set; }
    public bool IsSerialized { get; set; }
    public string? Category { get; set; }
    public List<InvoiceProductTaxEventHubEvent>? Taxes { get; set; }
}
