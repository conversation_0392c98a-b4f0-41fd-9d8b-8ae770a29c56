using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductAttributeConfiguration : IEntityTypeConfiguration<PimProductAttributeEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductAttributeEntity> builder)
        {
            builder.ToTable("ProductAttribute", "pim");

            builder.Has<PERSON>ey(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.AttributeId)
                .HasColumnName("AttributeId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.Value)
                .HasColumnName("Value")
                .HasColumnType("nvarchar(100)");

            builder.HasOne(x => x.PimAttributeEntity)
                .WithMany()
                .HasForeignKey(x => x.AttributeId);

            builder.HasOne(x => x.PimProductEntity)
                .WithMany(x => x.Attributes)
                .HasForeignKey(x => x.ProductId);
        }
    }
}