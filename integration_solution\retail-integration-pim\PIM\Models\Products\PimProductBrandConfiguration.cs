using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductBrandConfiguration : IEntityTypeConfiguration<PimProductBrandEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductBrandEntity> builder)
        {
            builder.ToTable("ProductBrand", "pim");

            builder.HasK<PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.BrandId)
                .HasColumnName("BrandId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.HasOne(x => x.ProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ProductId);

            builder.HasOne(x => x.BrandEntity)
                .WithMany()
                .HasForeignKey(x => x.BrandId);
        }
    }
}