﻿using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Core;

public interface IPatientAssociated
{
    public ExternalReference? Patient { get; set; }

    public bool HasChanges(IPatientAssociated? oldEntity)
    {
        if (oldEntity == null) return true;
        if (!Common.AreEqual(Patient, oldEntity.Patient)) return true;
        return false;
    }
}
