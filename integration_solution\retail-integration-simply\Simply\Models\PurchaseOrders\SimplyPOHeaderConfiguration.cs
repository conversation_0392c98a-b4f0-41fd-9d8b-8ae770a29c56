﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPOHeaderConfiguration : IEntityTypeConfiguration<SimplyPOHeaderEntity>
{
    public void Configure(EntityTypeBuilder<SimplyPOHeaderEntity> builder)
    {
        builder.ToTable("PO_Header");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.PONumber)
            .HasColumnName("PONumber")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.POType)
            .HasColumnName("POType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(true);

        builder.Property(x => x.PODate)
            .HasColumnName("PODate")
            .HasColumnType("datetime2(7)")
            .IsRequired(true);

        builder.Property(x => x.TotalItems)
            .HasColumnName("TotalItems")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.POAmount)
            .HasColumnName("POAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.DiscountAmount)
            .HasColumnName("DiscountAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.ClinicCode)
            .HasColumnName("ClinicCode")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.CreatedBy)
            .HasColumnName("CreatedBy")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);
            
        builder.Property(x => x.ApproverFirstName)
            .HasColumnName("ApproverFirstName")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);
            
        builder.Property(x => x.ApproverName)
            .HasColumnName("ApproverName")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);
            
        builder.Property(x => x.Area)
            .HasColumnName("Area")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);
            
        builder.Property(x => x.Region)
            .HasColumnName("Region")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);
            
        builder.Property(x => x.POSCode)
            .HasColumnName("POSCode")
            .HasColumnType("nvarchar(20)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true);

        builder.Property(x => x.IntegrationDate)
            .HasColumnName("IntegrationDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();
    }
}