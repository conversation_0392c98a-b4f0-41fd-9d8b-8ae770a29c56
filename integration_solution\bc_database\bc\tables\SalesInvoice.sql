﻿CREATE TABLE [bc].[SalesInvoice]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[CompanyId] UNIQUEIDENTIFIER,
	[ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
	[ExternalId] UNIQUEIDENTIFIER NOT NULL,
	[DocumentNumber] NVARCHAR(20),
	[ExternalDocumentNumber] NVARCHAR(35),
	[DocumentDate] DATETIME2(7),
	[PostingDate] DATETIME2(7),
	[CustomerId] UNIQUEIDENTIFIER,
	[ExternalCustomerId] UNIQUEIDENTIFIER, 
	[SellToCustomerId] UNIQUEIDENTIFIER,
	[ExternalSellToCustomerId] UNIQUEIDENTIFIER,
	[ResponsibilityCenterId] UNIQUEIDENTIFIER,
	[ExternalResponsibilityCenterId] UNIQUEIDENTIFIER,
	[CurrencyId] UNIQUEIDENTIFIER,
	[ExternalCurrencyId] UNIQUEIDENTIFIER,
	[PaymentTermsId] UNIQUEIDENTIFIER,
	[ExternalPaymentTermsId] UNIQUEIDENTIFIER,
	[DimensionSetId] UNIQUEIDENTIFIER,
	[ExternalDimensionSetNumber] INT,
	[AmountExcludingTax] DECIMAL(18, 4),
	[AmountIncludingTax] DECIMAL(18, 4),
	[ExternalCreatedOn] DATETIME2(7),
	[ExternalModifiedOn] DATETIME2(7),
	[CreatedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ModifiedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	CONSTRAINT [PK_SalesInvoice_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX [IX_SalesInvoice_CompanyId_ExternalId]
ON [bc].[SalesInvoice] ([CompanyId], [ExternalId])
GO

CREATE INDEX [IX_SalesInvoice_PostingDate_DocumentNumber]
ON [bc].[SalesInvoice] ([PostingDate], [DocumentNumber])
GO