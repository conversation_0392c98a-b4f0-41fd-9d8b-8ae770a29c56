﻿CREATE TABLE [cosium].[ts_typefacture] (
    [id]                     NVARCHAR (50) NOT NULL,
    [code]                   NVARCHAR (50) NULL,
    [achat_vente]            NVARCHAR (50) NULL,
    [CreatedOn]              DATETIME2 CONSTRAINT [DF_ts_typefacture_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]             DATETIME2 CONSTRAINT [DF_ts_typefacture_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT               [PK_ts_typefacture] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].[ts_typefacture_UpdateModified]
ON [cosium].[ts_typefacture]
AFTER UPDATE 
AS
   UPDATE [cosium].[ts_typefacture]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[ts_typefacture].[id] = i.[id]
GO