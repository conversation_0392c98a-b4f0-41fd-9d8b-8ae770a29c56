﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Manage.Models.Countries;

public class InventoryCountryConfiguration : IEntityTypeConfiguration<InventoryCountryEntity>
{
    public void Configure(EntityTypeBuilder<InventoryCountryEntity> builder)
    {
        builder.ToTable("InventoryCountry", "manage");

        builder.<PERSON><PERSON>ey(e => e.Id)
            .HasName("PK_InventoryCountry_Id");

        builder.Property(e => e.Id)
            .HasDefaultValueSql("NEWID()");

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.CreatedOn)
            .HasDefaultValueSql("sysutcdatetime()");

        builder.Property(e => e.ModifiedOn)
            .HasDefaultValueSql("sysutcdatetime()");

        builder.HasIndex(e => e.Name)
            .IsUnique()
            .HasDatabaseName("IX_InventoryCountry_Name");
    }
}
