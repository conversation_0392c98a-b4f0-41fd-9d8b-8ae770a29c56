﻿using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Manage.Models.PurchaseReceipts;

public static class PurchaseReceiptMapping
{
    /// <summary>
    /// Maps an OrderLineItemAcceptedEventHubEvent to a PurchaseReceipt
    /// This creates a receipt based on the accepted line item
    /// </summary>
    public static PurchaseReceipt ToPurchaseReceipt(this OrderLineItemAcceptedEventHubEvent orderLineItemAcceptedEvent, string externalSystemCode)
    {
        return new PurchaseReceipt
        {
            ExternalSystemCode = externalSystemCode,
            ExternalReference = orderLineItemAcceptedEvent.Id.ToString(),
            DocumentDate = orderLineItemAcceptedEvent.ShippedDate ?? DateTime.UtcNow,
            PurchaseOrder = new ExternalDocumentReference
            {
                ExternalReference = orderLineItemAcceptedEvent.Order.Id.ToString()
            },
            PurchaseReceiptLines = new List<PurchaseReceiptLine>()
        };
    }

}