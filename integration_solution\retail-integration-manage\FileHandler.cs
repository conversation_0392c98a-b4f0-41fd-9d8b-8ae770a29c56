﻿//using System;
//using System.IO;
//using Microsoft.Azure.WebJobs;
//using Microsoft.Azure.WebJobs.Host;
//using Microsoft.Extensions.Logging;

//namespace retail_integration_manage
//{
//    public class BlobHandler
//    {
//        [FunctionName("ListenForFiles")]
//        public void Run([BlobTrigger("we-dev/{name}", Connection = "BlobStorageConnectionString")] Stream myBlob, string name, ILogger log)
//        {
//            log.LogInformation($"C# Blob trigger function Processed blob\n Name:{name} \n Size: {myBlob.Length} Bytes");
//        }
//    }
//}