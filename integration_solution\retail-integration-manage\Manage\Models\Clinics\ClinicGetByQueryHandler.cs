﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Clinics;

namespace WSA.Retail.Integration.Manage.Models.Clinics;

public class ClinicGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IClinicService entityService)
    : BaseApiGetByQuery<
        Clinic,
        IClinicService>(appSettings, entityService)
{
}