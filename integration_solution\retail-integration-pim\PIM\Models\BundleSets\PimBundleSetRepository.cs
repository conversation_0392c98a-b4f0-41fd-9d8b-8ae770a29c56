﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.BundleSets;

public class PimBundleSetRepository(
    ILogger<PimBundleSetRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
        PimProductRelatedEntityRepositoryBase<PimBundleSetUpsertTableType>(
            logger,
            dbContextFactory),
    IPimBundleSetRepository
{
    protected override string StoredProcedureName => "pim.BundleSetUpsert";
    protected override string TableTypeName => "pim.BundleSetUpsertTableType";

    protected override List<PimBundleSetUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .SelectMany(p => p.BundleSets
            .SelectMany(b => b.ChildProducts
            .Select(c => new PimBundleSetUpsertTableType
            {
                BundleId = b.BundleSetId,
                BundleName = b.BundleSetName,
                BundleType = b.BundleSetType.ToString(),
                BundleIsDefault = b.IsDefault,
                BundleIsMandatory = b.IsMandatory,
                BundleIsMasterData = b.IsMasterData,
                BundleRanking = b.Ranking,
                BundleState = b.State.ToString(),
                BundleCreatedAt = b.CreatedAt,
                BundleUpdatedAt = b.UpdatedAt,
                ProductId = c.ProductId,
                ProductIsAvailable = c.IsAvailable,
                ProductIsPhasedOut = c.IsPhasedOut,
                ProductIsDefault = c.IsDefault,
                ProductRanking = c.Ranking,
                ProductSku = c.Sku,
                ProductState = c.State.ToString(),
                ProductCreatedAt = c.CreatedAt,
                ProductUpdatedAt = c.UpdatedAt
            }))).ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("BundleId", typeof(Guid));
        dataTable.Columns.Add("BundleName", typeof(string));
        dataTable.Columns.Add("BundleType", typeof(string));
        dataTable.Columns.Add("BundleIsDefault", typeof(bool));
        dataTable.Columns.Add("BundleIsMandatory", typeof(bool));
        dataTable.Columns.Add("BundleIsMasterData", typeof(bool));
        dataTable.Columns.Add("BundleRanking", typeof(int));
        dataTable.Columns.Add("BundleState", typeof(string));
        dataTable.Columns.Add("BundleCreatedAt", typeof(DateTime));
        dataTable.Columns.Add("BundleUpdatedAt", typeof(DateTime));
        dataTable.Columns.Add("ProductId", typeof(Guid));
        dataTable.Columns.Add("ProductIsAvailable", typeof(bool));
        dataTable.Columns.Add("ProductIsPhasedOut", typeof(bool));
        dataTable.Columns.Add("ProductIsDefault", typeof(bool));
        dataTable.Columns.Add("ProductRanking", typeof(int));
        dataTable.Columns.Add("ProductSku", typeof(string));
        dataTable.Columns.Add("ProductState", typeof(string));
        dataTable.Columns.Add("ProductCreatedAt", typeof(DateTime));
        dataTable.Columns.Add("ProductUpdatedAt", typeof(DateTime));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimBundleSetUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["BundleId"] = entity.BundleId;
            dataRow["BundleName"] = entity.BundleName ?? "n/a";
            dataRow["BundleType"] = entity.BundleType;
            dataRow["BundleIsDefault"] = entity.BundleIsDefault;
            dataRow["BundleIsMandatory"] = entity.BundleIsMandatory;
            dataRow["BundleIsMasterData"] = entity.BundleIsMasterData;
            dataRow["BundleRanking"] = entity.BundleRanking;
            dataRow["BundleState"] = entity.BundleState;
            dataRow["BundleCreatedAt"] = entity.BundleCreatedAt;
            dataRow["BundleUpdatedAt"] = entity.BundleUpdatedAt;
            dataRow["ProductId"] = entity.ProductId;
            dataRow["ProductIsAvailable"] = entity.ProductIsAvailable;
            dataRow["ProductIsPhasedOut"] = entity.ProductIsPhasedOut;
            dataRow["ProductIsDefault"] = entity.ProductIsDefault;
            dataRow["ProductRanking"] = entity.ProductRanking;
            dataRow["ProductSku"] = entity.ProductSku;
            dataRow["ProductState"] = entity.ProductState;
            dataRow["ProductCreatedAt"] = entity.ProductCreatedAt;
            dataRow["ProductUpdatedAt"] = entity.ProductUpdatedAt;
        }
        return dataRow;
    }
}