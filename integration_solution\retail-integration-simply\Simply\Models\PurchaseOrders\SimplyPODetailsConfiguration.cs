using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPODetailsConfiguration : IEntityTypeConfiguration<SimplyPODetailsEntity>
{
    public void Configure(EntityTypeBuilder<SimplyPODetailsEntity> builder)
    {
        builder.ToTable("PO_Detail");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.PurchaseOrderId)
            .HasColumnName("PurchaseOrderId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        builder.Property(x => x.PONumber)
            .HasColumnName("PONumber")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.POType)
            .HasColumnName("POType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(true);

        builder.Property(x => x.LineNumber)
            .HasColumnName("LineNumber")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.ProductCode)
            .HasColumnName("ProductCode")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.UnitOfMeasure)
            .HasColumnName("UnitOfMeasure")
            .HasColumnType("nvarchar(30)")
            .IsRequired(false);

        builder.Property(x => x.UnitPrice)
            .HasColumnName("UnitPrice")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.LineAmount)
            .HasColumnName("LineAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        // Set up foreign key relationship
        builder.HasOne(x => x.Header)
            .WithMany(h => h.Details)
            .HasForeignKey(x => x.PurchaseOrderId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}