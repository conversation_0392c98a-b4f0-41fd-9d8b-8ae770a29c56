using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductCategoryConfiguration : IEntityTypeConfiguration<PimProductCategoryEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductCategoryEntity> builder)
        {
            builder.ToTable("ProductCategory", "pim");

            builder.Has<PERSON>ey(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.CategoryId)
                .HasColumnName("CategoryId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.HasOne(x => x.ProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ProductId);

            builder.HasOne(x => x.CategoryEntity)
                .WithMany()
                .HasForeignKey(x => x.CategoryId);
        }
    }
}