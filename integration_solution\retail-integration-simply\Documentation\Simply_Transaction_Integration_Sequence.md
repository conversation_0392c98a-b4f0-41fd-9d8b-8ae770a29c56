# Transaction Integration Sequence Diagram

This sequence diagram illustrates the process of integrating transaction data from the Simply system.

```mermaid
sequenceDiagram
    participant Timer
    participant EventProcessor as FromSimplyEventProcessor
    participant SimplyDB as Simply Database
    participant TransProcessor as Transaction Processor (SalesInvoice/Payment/PurchaseOrder)
    participant ExternalService as External Service
    participant RefService as Reference Service
    
    Timer->>EventProcessor: FromSimplyTimerEventAsync(myTimer)
    EventProcessor->>EventProcessor: MasterDataPostAsync()
    EventProcessor->>EventProcessor: TransactionPost()
    
    EventProcessor->>TransProcessor: ProcessNewRecords()
    
    TransProcessor->>TransProcessor: GetFromSimply()
    TransProcessor->>SimplyDB: Query IntegrationRequired=true
    SimplyDB-->>TransProcessor: Return Transaction records
    
    alt Has records to process
        TransProcessor->>TransProcessor: PostList(records)
        TransProcessor->>ExternalService: Get EntitySubscriber
        ExternalService-->>TransProcessor: Return EntitySubscriber
        
        alt FromExternalSystem is true
            loop For each record
                TransProcessor->>TransProcessor: Prepare Transaction object
                
                TransProcessor->>TransProcessor: ValidateExternalReferences()
                TransProcessor->>RefService: Get Patient reference
                RefService-->>TransProcessor: Return Patient reference
                TransProcessor->>RefService: Get Clinic reference
                RefService-->>TransProcessor: Return Clinic reference
                
                alt Has line items
                    loop For each line
                        TransProcessor->>RefService: Get Product reference
                        RefService-->>TransProcessor: Return Product reference
                    end
                end
                
                TransProcessor->>ExternalService: Upsert() transaction
                ExternalService-->>TransProcessor: Return result
                
                alt Upsert successful
                    TransProcessor->>SimplyDB: UpdateIsIntegrated()
                    SimplyDB-->>TransProcessor: Return success
                else Upsert failed
                    TransProcessor->>TransProcessor: Log error
                end
            end
        end
    else No records to process
        TransProcessor->>TransProcessor: Log "No new transactions" and return
    end
    
    TransProcessor-->>EventProcessor: Processing complete
    EventProcessor->>EventProcessor: Log completion
```

This sequence diagram shows the transaction integration process:

1. A timer triggers the `FromSimplyEventProcessor`
2. After processing master data, it calls `TransactionPost()` which invokes the appropriate transaction processors
3. Each processor (SalesInvoice, Payment, PurchaseOrder):
   - Fetches transaction records from the Simply database
   - For each transaction, it:
     - Creates a transaction model
     - Validates and resolves external references (patients, clinics, products)
     - Sends the transaction to the external system
     - Updates the integration status on success
4. The process completes and logs the results

This implementation ensures that all related references are resolved before sending transactions to the external system, maintaining data integrity and consistency.