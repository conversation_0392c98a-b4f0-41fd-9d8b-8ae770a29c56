﻿namespace WSA.Retail.Integration.Core;

public interface IDocumentRepositoryBase<TModel, TLineModel, TEntity, TLineEntity>
    where TModel : class, IDocument<TLineModel>
    where TLineModel : class, IDocumentLine
    where TEntity : class, IDocumentEntity<TLineEntity>
    where TLineEntity : class, IDocumentLineEntity
{
    public Task<TModel?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null);

    public Task<List<TModel>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null);

    public Task<bool> InsertAsync(TModel domainEntity);

    public Task<bool> UpdateAsync(TModel domainModel);
}
