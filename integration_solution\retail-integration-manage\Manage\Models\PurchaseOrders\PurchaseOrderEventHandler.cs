﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Data;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.PurchaseOrders;

public class PurchaseOrderEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PurchaseOrderEventHandler> logger,
    BlobServiceClient blobServiceClient,
    Dictionary<string, QueueClient> queueClients,
    IManageAPI manageApi,
    IDbContextFactory<ManageDbContext> contextFactory,
    IPurchaseOrderService purchaseOrderService,
    IClinicService clinicService,
    IVendorService vendorService,
    IProductService productService) : 
        EventHandlerBase<PurchaseOrderEventHubEvent, PurchaseOrder, OrderEventEntity>(
            appSettings, 
            logger, 
            blobServiceClient,
            queueClients), 
        IPurchaseOrderEventHandler
{
    private readonly IManageAPI _manageApi = manageApi;
    private readonly IDbContextFactory<ManageDbContext> _contextFactory = contextFactory;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IClinicService _clinicService = clinicService;
    private readonly IVendorService _vendorService = vendorService;
    private readonly IProductService _productService = productService;

    protected override async Task<PurchaseOrder?> GetFromApiAsync(PurchaseOrderEventHubEvent? eventData)
    {
        LogMethodStart();

        if (eventData == null)
        {
            LogCustomWarning($"Event data was null for {nameof(PurchaseOrderEventHubEvent)}.");
            return default;
        }

        PurchaseOrder? purchaseOrder;
        OrderResponse? response;
        try
        {
            response = await _manageApi.OrdersGET2Async(eventData.Id);
            if (response == null)
            {
                LogCustomError($"No response received for PurchaseOrder with ID: {eventData.Id}");
                return null;
            }
            purchaseOrder = response.ToPurchaseOrder(_appSettings.ExternalSystemCode);
            return purchaseOrder;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<PurchaseOrder?> GetFromRepositoryAsync(PurchaseOrder modelData)
    {
        LogMethodStart();
        try
        {
            PurchaseOrder? existingPurchaseOrder = null;
            if (existingPurchaseOrder == null && modelData.ExternalReference != null)
            {
                existingPurchaseOrder = await _purchaseOrderService.GetAsync(
                externalSystemCode: _appSettings.ExternalSystemCode,
                externalReference: modelData.ExternalReference);
            }

            if (existingPurchaseOrder == null && modelData.DocumentNumber != null)
            {
                existingPurchaseOrder = await _purchaseOrderService.GetAsync(
                    externalSystemCode: _appSettings.ExternalSystemCode,
                    documentNumber: modelData.DocumentNumber);
            }

            return existingPurchaseOrder;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<PurchaseOrder> OnAfterGetModelDataFromRepositoryAsync(
        PurchaseOrder? existingModelData, 
        PurchaseOrder newModelData)
    {
        LogMethodStart();

        if (existingModelData != null)
        {
            newModelData.Clinic = existingModelData.Clinic;
            newModelData.Vendor = existingModelData.Vendor;
            newModelData.DocumentNumber = existingModelData.DocumentNumber;
            newModelData.DocumentDate = existingModelData.DocumentDate;

            foreach (var line in newModelData.Lines)
            {
                var existingLine = existingModelData.Lines
                    .Where(x => x.ExternalReference == line.ExternalReference)
                    .FirstOrDefault();

                if (existingLine != null)
                {
                    line.Sequence = existingLine.Sequence;
                    line.Product = existingLine.Product;
                }
            }
        }

        newModelData.DocumentDate ??= DateTime.Now;

        return await Task.FromResult(newModelData);
    }

    protected override async Task<PurchaseOrder?> ValidateExternalReferencesAsync(PurchaseOrder modelData)
    {
        LogMethodStart();

        if (modelData.Clinic != null)
        {
            var clinic = await _clinicService.ValidateExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                modelData.Clinic);

            if (clinic != null)
            {
                modelData.Clinic = clinic;
            }

            if (clinic == null)
            {
                LogCustomError($"Clinic ExternalCode = '{modelData.Clinic.ExternalCode}' does not exist.");
            }
        }

        if (modelData.Vendor != null)
        {
            var vendor = await _vendorService.ValidateExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                modelData.Vendor);

            if (vendor != null)
            {
                modelData.Vendor = vendor;
            }

            if (vendor == null)
            {
                LogCustomError($"Vendor ExternalCode = '{modelData.Vendor.ExternalCode}' does not exist.");
            }
        }

        foreach (var line in modelData.Lines)
        {
            if (line.Product != null)
            {
                var product = await _productService.ValidateExternalReferenceAsync(
                    _appSettings.ExternalSystemCode,
                    line.Product);

                if (product != null)
                {
                    line.Product = product;
                }

                if (product == null)
                {
                    LogCustomError($"Product ExternalCode = '{line.Product.ExternalCode}' does not exist.");
                }
            }
        }

        return modelData;
    }

    protected override async Task<PurchaseOrder?> VerifyRequiredFieldsAsync(PurchaseOrder modelData)
    {
        LogMethodStart();

        try
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.DocumentNumber, nameof(modelData.DocumentNumber));
            ArgumentNullException.ThrowIfNull(modelData.DocumentDate, nameof(modelData.DocumentDate));
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Clinic?.Code, $"{nameof(modelData.Clinic)}.{nameof(modelData.Clinic.Code)}");
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Vendor?.Code, $"{nameof(modelData.Vendor)}.{nameof(modelData.Vendor.Code)}");
            foreach (var line in modelData.Lines)
            {
                ArgumentNullException.ThrowIfNull(line.Sequence, nameof(line.Sequence));
                ArgumentException.ThrowIfNullOrWhiteSpace(line.Product?.Code, $"{nameof(line.Product)}.{nameof(line.Product.Code)}");
                ArgumentNullException.ThrowIfNull(line.Quantity, nameof(line.Quantity));
            }

            return await Task.FromResult(modelData);
        }
        catch(Exception ex)
        {
            LogCustomError(ex.Message);
            return null;
        }
    }

    protected override async Task<PurchaseOrder?> UpsertAsync(PurchaseOrder modelData)
    {
        LogMethodStart();

        try
        {
            var purchaseOrder = await _purchaseOrderService.UpsertAsync(modelData);
            if (purchaseOrder == null)
            {
                LogCustomError($"Failed to upsert PurchaseOrder {modelData.DocumentNumber}");
                return null;
            }
            else
            {
                return purchaseOrder;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<OrderEventEntity?> LogMessageMetadataAsync(EventHubMessage message)
    {
        LogMethodStart();

        var msg = JsonSerializer.Serialize(message.Message, Common.GetJsonOptions());
        var purchaseOrderEvent = JsonSerializer.Deserialize<PurchaseOrderEventHubEvent>(msg!, Common.GetJsonOptions());
        if (purchaseOrderEvent == null)
        {
            LogCustomWarning("PurchaseOrderEventHubEvent deserialization failed.");
            return null;
        }

        var dbContext = await _contextFactory.CreateDbContextAsync();
        var orderEventEntity = await dbContext.OrderEventEntity.Where(x => x.MessageId == message.MessageId).FirstOrDefaultAsync();
        if (orderEventEntity != null)
        {
            bool isDirty = false;

            if (orderEventEntity.OrderId != purchaseOrderEvent.Id)
            {
                orderEventEntity.OrderId = purchaseOrderEvent.Id;
                isDirty = true;
            }

            if (orderEventEntity.ExternalNumber != purchaseOrderEvent.Number)
            {
                orderEventEntity.ExternalNumber = purchaseOrderEvent.Number;
                isDirty = true;
            }

            if (orderEventEntity.Timestamp != message.Timestamp)
            {
                orderEventEntity.Timestamp = message.Timestamp;
                isDirty = true;
            }

            var timeZoneId = _appSettings.LocalTimeZone;
            var localTimestamp = TimeZoneHelper.ConvertToTimeZone(message.Timestamp, timeZoneId);
            if (orderEventEntity.LocalTimestamp != localTimestamp)
            {
                orderEventEntity.LocalTimestamp = localTimestamp;
                isDirty = true;
            }

            if (!string.IsNullOrWhiteSpace(message.BlobPath) && orderEventEntity.BlobPath != message.BlobPath)
            {
                orderEventEntity.BlobPath = message.BlobPath;
                isDirty = true;
            }

            if (isDirty)
            {
                orderEventEntity.ModifiedOn = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();
                return orderEventEntity;
            }
        }
        else
        {
            orderEventEntity = new OrderEventEntity
            {
                Id = Guid.NewGuid(),
                EventType = message.Event,
                MessageId = message.MessageId,
                OrderId = purchaseOrderEvent.Id,
                ExternalNumber = purchaseOrderEvent.Number,
                Timestamp = message.Timestamp,
                LocalTimestamp = message.Timestamp,
                BlobPath = message.BlobPath ?? string.Empty,
                Status = EventProcessingStatus.New
            };
            dbContext.OrderEventEntity.Add(orderEventEntity);
            await dbContext.SaveChangesAsync();
            return orderEventEntity;
        }
        return null;
    }

    protected override async Task<OrderEventEntity?> UpdateMessageLogStatusAsync(OrderEventEntity log, EventProcessingStatus status)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var orderEventEntity = await dbContext.OrderEventEntity
            .FirstOrDefaultAsync(x => x.Id == log.Id);
        if (orderEventEntity != null)
        {
            orderEventEntity.Status = status;
            orderEventEntity.ProcessedOn = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
            return orderEventEntity;
        }

        return default;
    }

    protected override async Task<OrderEventEntity?> GetMetadataByMessageId(Guid messageId)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.OrderEventEntity
            .FirstOrDefaultAsync(x => x.Id == messageId);
        if (eventEntity != null)
        {
            return eventEntity;
        }

        return default;
    }


    [Function("RequeueOrderEvent")]
    public async Task<IActionResult> RequeueOrderEvent([HttpTrigger(AuthorizationLevel.Function, "post", Route = "requeueOrderEvent/{id}")] HttpRequest req,
        Guid id)
    {
        LogMethodStart();

        if (id == Guid.Empty)
        {
            LogCustomError("Invalid ID provided for requeueing order event.");
            return new BadRequestObjectResult("Invalid ID provided.");
        }

        try
        {
            await RetryFromLogAsync(id);
            return new OkObjectResult($"Order event with ID {id} has been requeued successfully.");
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return new BadRequestObjectResult($"Failed to requeue order event: {ex.Message}");
        }
    }
}