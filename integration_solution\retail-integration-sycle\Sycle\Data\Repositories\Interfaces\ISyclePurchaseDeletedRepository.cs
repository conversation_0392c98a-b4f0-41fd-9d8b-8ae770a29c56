using WSA.Retail.Integration.Sycle.Models.Purchases;

namespace WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;

public interface ISyclePurchaseDeletedRepository
{
    Task<List<string>> GetIntegrationRecordsAsync();

    Task<List<SyclePurchaseDeletedEntity>> GetLinesByTransactionNo(string transactionNo);

    Task<SyclePurchase?> GetAsync(int id);
    Task<bool> UpdateIntegrationStatusAsync(int id, bool isIntegrated = false);
}