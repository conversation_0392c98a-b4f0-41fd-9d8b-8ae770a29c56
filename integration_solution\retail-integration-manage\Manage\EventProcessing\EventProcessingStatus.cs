﻿namespace WSA.Retail.Integration.Manage.EventProcessing;

public enum EventProcessingStatus
{
    New = 0,
    Success = 1,
    Skipped = 2,
    SoftFail = 3,
    HardFail = 4,
    Retry = 5
}

public static class EventProcessingStatusExtensions
{
    public static bool ShouldSkip(this EventProcessingStatus status) =>
        status == EventProcessingStatus.HardFail ||
        status == EventProcessingStatus.Skipped ||
        status == EventProcessingStatus.Success;
}
