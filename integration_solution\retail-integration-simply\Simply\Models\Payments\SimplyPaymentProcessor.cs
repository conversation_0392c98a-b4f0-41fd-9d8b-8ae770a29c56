using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Payments;
using Microsoft.EntityFrameworkCore.Metadata;

namespace WSA.Retail.Integration.Simply.Models.Payments;

public class SimplyPaymentProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPaymentProcessor> logger,
    ISimplyPaymentRepository simplyPaymentRepository,
    IPaymentService paymentService)
    : ISimplyPaymentProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPaymentProcessor> _logger = logger;
    private readonly ISimplyPaymentRepository _simplyPaymentRepository = simplyPaymentRepository;
    private readonly IPaymentService _paymentService = paymentService;

    public async Task ProcessSimplyPaymentTransactionsAsync()
    {
        _logger.LogMethodStart();

        var records = await _simplyPaymentRepository.GetIntegrationRecordsAsync();
        if (records == null || records.Count == 0)
        {
            _logger.LogCustomInformation("No payment records found for integration");
            return ;
        }

        _logger.LogCustomInformation($"Found {records.Count} payment records for integration");
        await Parallel.ForEachAsync(
            records,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (record, cancellationToken) =>
            {
                await ProcessSimplyPaymentTransactionAsync(record);
            });

    }

    public async Task<bool> ProcessSimplyPaymentTransactionAsync(SimplyPaymentEntity record)
    {
        _logger.LogMethodStart();
        var payment = record.ToPayment();

        // Set document number
        payment.DocumentNumber = GetDocumentNumber(record.DocumentNumber);

        // Resolve external references
        await _paymentService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, payment);

        // Verify required data
        bool isValid = VerifyRequiredData(payment);
        if (!isValid)
        {
            _logger.LogCustomError($"Payment {payment.DocumentNumber} has missing required data");
            return false;
        }

        // Upsert the payment
        var result = await _paymentService.UpsertAsync(payment);
        if (result != null)
        {
            // Update integration status
            await _simplyPaymentRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed payment {payment.DocumentNumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process payment {payment.DocumentNumber}");
            return false;
        }
    }
   
    private string GetDocumentNumber(string documentNumber)
    {
        string prefix = "PMT-";
        return $"{prefix}{documentNumber.ToString().PadLeft(10, '0')}";
    }
    
    private bool VerifyRequiredData(Payment payment)
    {
        if (string.IsNullOrEmpty(payment.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }
        
        if (payment.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }
        
        if (payment.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (payment.PaymentMethod == null)
        {
            _logger.LogCustomError("PaymentMethod is required");
            return false;
        }

        return true;
    }
}