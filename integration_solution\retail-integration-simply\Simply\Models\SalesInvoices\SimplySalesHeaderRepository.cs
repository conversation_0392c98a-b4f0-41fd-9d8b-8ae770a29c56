using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesHeaderRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplySalesHeaderRepository> logger,
    SimplyContext simplyContext)
    : ISimplySalesHeaderRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplySalesHeaderRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<SimplySalesHeaderEntity>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplySalesHeaderEntity
                .Where(x => x.IntegrationRequired == true)
                //.Where(x => x.InvoiceNumber.Trim() == "2226228")
                .Include(x => x.Details)
                .Include(x => x.Funders)
                .ToListAsync();

            return list ?? [];
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplySalesHeaderEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}