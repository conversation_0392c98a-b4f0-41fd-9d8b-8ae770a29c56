# Event System Class Diagram

This diagram illustrates the event system classes in the WSA Retail Integration Model project.

```mermaid
classDiagram
    class Event {
        +string Id
        +string Source
        +string SpecVersion
        +string EventType
        +string Subject
        +string EventTime
        +object Data
        +Event(source, eventType, subject, data)
    }

    class EventGridEvent {
        <<static>>
        +Create(appSettings, eventType, subject, data) Event
    }

    class IEventGridPublisher {
        <<interface>>
        +RaiseEventAsync(settings, eventType, subject, data) Task
        +RaiseMockEventAsync(eventType, subject, data) Task
    }

    class EventGridPublisher {
        -HttpClient _httpClient
        -AppSettings _appSettings
        -Dictionary~string,QueueClient~ _queueClients
        +RaiseEventAsync(settings, eventType, subject, data) Task
        +RaiseMockEventAsync(eventType, subject, data) Task
    }

    class BaseEntityService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #IDbContextFactory~IntegrationContext~ _dbContextFactory
        #IEventGridPublisher _eventGridPublisher
    }

    EventGridEvent --> Event : creates
    IEventGridPublisher <|.. EventGridPublisher
    EventGridPublisher ..> Event : uses
    BaseEntityService --> IEventGridPublisher : uses
```

This diagram shows the event system classes. The `Event` class represents an event in the system. The `EventGridEvent` static class creates events with standard properties. The `IEventGridPublisher` interface defines methods for publishing events, and the `EventGridPublisher` class implements this interface. The `BaseEntityService` class uses the event publisher to raise events when entity operations occur.