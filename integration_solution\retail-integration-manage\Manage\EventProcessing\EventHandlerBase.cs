﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class EventHandlerBase<TEvent, TModel, TLogClass>(
    IOptions<AppSettings> appSettings,
    ILogger logger,
    BlobServiceClient blobServiceClient,
    Dictionary<string, QueueClient> queueClients)
    : LoggingBase<TEvent>(logger), IEventHandler
    where TEvent : class, IIdentifiable
    where TModel : class, IIdentifiable
    where TLogClass : class, IEventProcessingInfo
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly BlobServiceClient _blobServiceClient = blobServiceClient;
    protected readonly QueueClient? _queueClient = queueClients.GetValueOrDefault("from-manage");

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        LogMethodStart();

        var eventLog = await LogMessageMetadataAsync(message);
        if (eventLog == null)
        {
            LogCustomError("Event log is null after logging message metadata.");
            return false;
        }

        // Handle statuses that should not be processed.  Return true so the queue will be deleted, not just dequeued.
        if (eventLog.Status.ShouldSkip())
        {
            return true;
        }

        EventProcessingStatus status;
        try
        {
            status = await ProcessFromQueueInternalAsync(message);
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            status = EventProcessingStatus.HardFail;
        }

        eventLog = await UpdateMessageLogStatusAsync(eventLog, status);
        if (eventLog != null && eventLog.Status.ShouldSkip())
        {
            return true;
        }
        else
        {
            return false;
        }
    } 

    protected virtual async Task<EventProcessingStatus> ProcessFromQueueInternalAsync(EventHubMessage message)
    {
        LogMethodStart();

        var eventData = Deserialize(message);
        if (eventData == null)
        {
            LogCustomError("Event data is null after deserialization.");
            return EventProcessingStatus.HardFail;
        }

        var skipStatus = await OnAfterDeserialize(eventData);
        if (skipStatus == EventProcessingStatus.Skipped)
        {
            LogCustomError("Event data has been marked as skipped.");
            return EventProcessingStatus.Skipped;
        }
        if (skipStatus == EventProcessingStatus.SoftFail)
        {
            LogCustomError("Skip status was unable to be determined.");
            return EventProcessingStatus.SoftFail;
        }

        var modelData = await GetModelDataFromManageApiAsync(eventData);
        if (modelData == null)
        {
            LogCustomError("Model data is null after getting from Manage.");
            return EventProcessingStatus.HardFail;
        }

        var existingModelData = await GetModelDataFromRepositoryAsync(modelData);
        if (existingModelData != null)
        {
            modelData.Id = existingModelData.Id;
        }
        modelData = await OnAfterGetModelDataFromRepositoryAsync(existingModelData, modelData);
        if (modelData == null)
        {
            LogCustomError("Model data is null after getting from repository.");
            return EventProcessingStatus.HardFail;
        }

        modelData = await ValidateModelDataExternalReferencesAsync(modelData);

        if (modelData == null)
        {
            LogCustomError("Model data is null after validating external references.");
            return EventProcessingStatus.SoftFail;
        }

        modelData = await VerifyModelDataRequiredFieldsAsync(modelData);
        if (modelData == null)
        {
            LogCustomError("Model data is null after verifying required fields.");
            return EventProcessingStatus.SoftFail;
        }

        modelData = await UpsertModelDataAsync(modelData);
        if (modelData != null)
        {
            return EventProcessingStatus.Success;
        }
        else
        {
            return EventProcessingStatus.SoftFail;
        }
    }

    public async Task<bool> HandleToQueueAsync(Events.Event ev)
    {
        LogMethodStart();

        await Task.Yield();
        return true;
    }

    public async Task RetryFromLogAsync(Guid messageId)
    {
        LogMethodStart();
        var log = await GetMetadataByMessageId(messageId);
        if (log == null)
        {
            LogCustomError($"No log found for message ID {messageId}");
            return;
        }

        if (string.IsNullOrWhiteSpace(log.BlobPath))
        {
            LogCustomError($"Blob path is empty for message ID {messageId}");
            return;
        }

        var archiveContainerClient = _blobServiceClient.GetBlobContainerClient(_appSettings.EventArchiveContainerName);
        var blobClient = archiveContainerClient.GetBlobClient(log.BlobPath);

        string messageBody;
        try
        {
            var blobDownload = await blobClient.DownloadContentAsync();
            messageBody = blobDownload.Value.Content.ToString();
            var eventHubMessage = JsonSerializer.Deserialize<EventHubMessage>(messageBody, Utilities.Common.GetJsonOptions());
            if (eventHubMessage == null)
            {
                LogCustomError($"Failed to deserialize message body for message ID {messageId}");
                return;
            }
            eventHubMessage.Archive = false; // Set Archive to false for retry
            messageBody = JsonSerializer.Serialize(eventHubMessage, Utilities.Common.GetJsonOptions());
        }
        catch (RequestFailedException ex) when (ex.Status == 404)
        {
            LogCustomError($"Blob not found at path '{log.BlobPath}' for message ID {messageId}");
            return;
        }
        catch (Exception ex)
        {
            LogCustomError($"Failed to retry message {messageId}: {ex.Message}");
            return;
        }

        try
        {
            if (!string.IsNullOrWhiteSpace(messageBody))
            {
                byte[] utf8Bytes = Encoding.UTF8.GetBytes(messageBody);
                string base64String = Convert.ToBase64String(utf8Bytes);
                ArgumentNullException.ThrowIfNull(_queueClient);
                await _queueClient.SendMessageAsync(base64String);
            }
        }
        catch (Exception ex)
        {
            LogCustomError($"Failed to send message {messageId} to queue: {ex.Message}");
            return;
        }

        await UpdateMessageLogStatusAsync(log, EventProcessingStatus.Retry);
    }

    // ########## Log message metadata ##########
    protected virtual async Task<TLogClass?> LogMessageMetadataAsync(EventHubMessage message)
    {
        await Task.Yield();
        return default;
    }

    // ########## Update message log ##########
    protected virtual async Task<TLogClass?> UpdateMessageLogStatusAsync(TLogClass log, EventProcessingStatus status)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TLogClass?> GetMetadataByMessageId(Guid messageId)
    {
        await Task.Yield();
        return default;
    }

    // ########## Deserialize to Event Model ##########
    protected virtual TEvent? Deserialize(EventHubMessage message)
    {
        LogMethodStart();
        var msg = message.Message?.ToString();
        if (msg != null)
        {
            var eventData = JsonSerializer.Deserialize<TEvent>(msg, Utilities.Common.GetJsonOptions());
            return eventData;
        }
        return default;
    }

    protected virtual async Task<EventProcessingStatus> OnAfterDeserialize(TEvent eventData)
    {
        await Task.Yield();
        return EventProcessingStatus.New;
    }

    // ########## Get Model from Manage API ##########
    private async Task<TModel?> GetModelDataFromManageApiAsync(TEvent eventData)
    {
        var before = await OnBeforeGetModelDataFromManageApiAsync(eventData);
        var modelData = before.IsHandled ? before.Value : await GetFromApiAsync(eventData);
        modelData = await OnAfterGetModelDataFromManageApiAsync(eventData, modelData);
        return modelData;
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeGetModelDataFromManageApiAsync(TEvent? eventData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> GetFromApiAsync(TEvent? eventData)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TModel?> OnAfterGetModelDataFromManageApiAsync(TEvent? eventData, TModel? modelData)
    {
        await Task.Yield();
        return modelData;
    }

    // ########## Get Existing Model from Repository ##########
    private async Task<TModel?> GetModelDataFromRepositoryAsync(TModel modelData)
    {
        var before = await OnBeforeGetModelDataFromRepositoryAsync(modelData);
        var existingModelData = before.IsHandled ? before.Value : await GetFromRepositoryAsync(modelData);
        return await OnAfterGetFromRepositoryAsync(existingModelData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeGetModelDataFromRepositoryAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> GetFromRepositoryAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TModel?> OnAfterGetFromRepositoryAsync(TModel? existingModelData, TModel? newModelData)
    {
        await Task.Yield();
        return existingModelData;
    }

    protected virtual async Task<TModel> OnAfterGetModelDataFromRepositoryAsync(TModel? existingModelData, TModel newModelData)
    {
        await Task.Yield();
        return newModelData;
    }

    // ########## Validate External References ##########
    private async Task<TModel?> ValidateModelDataExternalReferencesAsync(TModel modelData)
    {
        var before = await OnBeforeValidateModelDataExternalReferencesAsync(modelData);
        var existingModelData = before.IsHandled ? before.Value : await ValidateExternalReferencesAsync(modelData);
        return await OnAfterValidateModelDataExternalReferencesAsync(existingModelData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeValidateModelDataExternalReferencesAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> ValidateExternalReferencesAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TModel?> OnAfterValidateModelDataExternalReferencesAsync(TModel? existingModelData, TModel? newModelData)
    {
        await Task.Yield();
        return newModelData;
    }


    // ########## Verify Required Fields ##########
    private async Task<TModel?> VerifyModelDataRequiredFieldsAsync(TModel? modelData)
    {
        if (modelData == null)
        {
            return null;
        }
        var before = await OnBeforeVerifyModelDataRequiredFieldsAsync(modelData);
        modelData = before.IsHandled ? before.Value : await VerifyRequiredFieldsAsync(modelData);
        return await OnAfterVerifyModelDataRequiredFieldsAsync(modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeVerifyModelDataRequiredFieldsAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> VerifyRequiredFieldsAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TModel?> OnAfterVerifyModelDataRequiredFieldsAsync(TModel? modelData)
    {
        await Task.Yield();
        return modelData;
    }

    // ########## Upsert Model ##########
    private async Task<TModel?> UpsertModelDataAsync(TModel modelData)
    {
        var before = await OnBeforeUpsertModelDataAsync(modelData);
        var upsertModelData = before.IsHandled ? before.Value : await UpsertAsync(modelData);
        return await OnAfterUpsertModelDataAsync(upsertModelData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeUpsertModelDataAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> UpsertAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }

    protected virtual async Task<TModel?> OnAfterUpsertModelDataAsync(TModel? existingModelData, TModel? newModelData)
    {
        await Task.Yield();
        return newModelData;
    }
}