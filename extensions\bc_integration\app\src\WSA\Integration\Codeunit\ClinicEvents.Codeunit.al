namespace WSA.Integration.API.V1;

using Microsoft.Inventory.Location;
using WSA.Integration;

codeunit 50140 "Clinic Events"
{
    [EventSubscriber(ObjectType::Table, Database::"Responsibility Center", OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record "Responsibility Center";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendClinicToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::"Responsibility Center", OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record "Responsibility Center";
        var xRec: Record "Responsibility Center";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendClinicToIntegrationAPI(Rec);
    end;



    local procedure SendClinicToIntegrationAPI(ResponsibilityCenter: Record "Responsibility Center")
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if ResponsibilityCenter.Code = '' then
            exit;

        if ResponsibilityCenter.Name = '' then
            exit;

        if IsNullGuid(ResponsibilityCenter.SystemId) then
            exit;

        JObject := Common.ClinicToJson(ResponsibilityCenter);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'clinics', Format(ResponsibilityCenter.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}