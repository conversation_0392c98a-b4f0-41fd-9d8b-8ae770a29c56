namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesFunderEntity
{
    public Guid Id { get; set; }
    public Guid SalesInvoiceId { get; set; }
    public required string SalesType { get; set; }
    public required string InvoiceNumber { get; set; }
    public int Line { get; set; }
    public int? FunderID { get; set; }
    public int? Quantity { get; set; }
    public decimal? Amount { get; set; }
    public string? FileName { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
    public bool IntegrationRequired { get; set; }
    public DateTime? IntegrationDate { get; set; }

    // Navigation property
    public SimplySalesHeaderEntity? Header { get; set; }
}