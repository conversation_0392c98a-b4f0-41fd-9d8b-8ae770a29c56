﻿CREATE TABLE [bc].[CustomerLedgerEntry_Staging](
	[Id] UNIQUEIDENTIFIER NOT NULL,
	[CompanyId] UNIQUEIDENTIFIER,
	[EntryNo] INT,
	[CustomerId] UNIQUEIDENTIFIER,
	[CustomerPostingGroupId] UNIQUEIDENTIFIER,
	[PostingDate] DATETIME2(7),
	[DocumentDate] DATETIME2(7),
	[DueDate] DATETIME2(7),
	[DocumentType] NVARCHAR(20),
	[DocumentNo] NVARCHAR(20),
	[ExternalDocumentNo] NVARCHAR(50),
	[Description] NVARCHAR(100),
	[Open] BIT,
	[Amount] DECIMAL(18,2),
	[DimensionSetId] INT,
	[SystemCreatedAt] DATETIME2(7),
	[SystemModifiedAt] DATETIME2(7),
	[ETL_LoadId] NVARCHAR(40),
	[ETL_CreatedAt] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ETL_Status] NVARCHAR(40)
	)
GO

CREATE INDEX [IX_CustomerLedgerEntryStaging_ETLLoadId_CompanyId_Id]
ON [bc].[CustomerLedgerEntry_Staging] ([ETL_LoadId], [CompanyId], [Id])
GO