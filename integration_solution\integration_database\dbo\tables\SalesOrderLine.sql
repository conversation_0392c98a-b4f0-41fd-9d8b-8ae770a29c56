﻿CREATE TABLE [dbo].[SalesOrderLine]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_SalesOrderLine_Id] DEFAULT NEWID() NOT NULL,
    [SalesOrderId]          UNIQUEIDENTIFIER NOT NULL,
    [Sequence]              INT NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NULL,
    [Description]           NVARCHAR(100) NULL,
    [Quantity]              MONEY NULL,
    [SerialNumber]          NVARCHAR(30) NULL,
    [UnitPrice]             MONEY NULL,
    [GrossAmount]           MONEY NULL,
    [DiscountAmount]        MONEY NULL,
    [AmountExclTax]         MONEY NULL,
    [TaxAmount]             MONEY NULL,
    [AmountInclTax]         MONEY NULL,
    [IsActive]              BIT CONSTRAINT [DF_SalesOrderLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_SalesOrderLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_SalesOrderLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT              [PK_SalesOrderLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_SalesOrderLine_SalesOrder] FOREIGN KEY (SalesOrderId) REFERENCES [dbo].[SalesOrder](Id),
    CONSTRAINT              [FK_SalesOrderLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesOrderLine_SalesOrderId_LineNo
ON [dbo].[SalesOrderLine] ([SalesOrderId], [Sequence])
GO

CREATE TRIGGER SalesOrderLine_UpdateModified
ON [dbo].[SalesOrderLine]
AFTER UPDATE 
AS
   UPDATE [dbo].[SalesOrderLine]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [dbo].[SalesOrderLine].[Id] = i.[Id]
GO