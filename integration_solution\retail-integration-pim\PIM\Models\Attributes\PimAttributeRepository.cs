﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.Attributes;

public class PimAttributeRepository(
    ILogger<PimAttributeRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) : 
        PimProductRelatedEntityRepositoryBase<PimAttributeUpsertTableType> (
            logger,
            dbContextFactory),
    IPimAttributeRepository
{
    protected override string StoredProcedureName => "pim.AttributeUpsert";
    protected override string TableTypeName => "pim.AttributeUpsertTableType";

    protected override List<PimAttributeUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .SelectMany(p => p.Attributes.Select(a => new PimAttributeUpsertTableType
            {
                ProductId = p.ProductId,
                Code = a.AttributeCode,
                Name = a.Name,
                DataType = a.DataType,
                Value = a.Value != null && a.Value.Length > 100 ? a.Value.Substring(0, 100) : a.Value
            })).ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("ProductId", typeof(Guid));
        dataTable.Columns.Add("Code", typeof(string));
        dataTable.Columns.Add("Name", typeof(string));
        dataTable.Columns.Add("DataType", typeof(string));
        dataTable.Columns.Add("Value", typeof(string));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimAttributeUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["ProductId"] = entity.ProductId;
            dataRow["Code"] = entity.Code;
            dataRow["Name"] = entity.Name;
            dataRow["DataType"] = entity.DataType;
            dataRow["Value"] = entity.Value;
        }
        return dataRow;
    }
}