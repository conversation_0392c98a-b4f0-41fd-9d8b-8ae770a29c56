﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Models.Claims;

public class Claim : IClaim
{
    [JsonPropertyName("id")] public Guid Id { get; set; } = Guid.Empty;
    [JsonPropertyName("externalSystemCode")] public string? ExternalSystemCode { get; set; }
    [JsonPropertyName("documentNumber")] public required string DocumentNumber { get; set; }
    [JsonPropertyName("externalReference")] public string? ExternalReference { get; set; }
    [JsonPropertyName("salesInvoice")] public ExternalDocumentReference? SalesInvoice { get; set; }
    [JsonPropertyName("salesCredit")] public ExternalDocumentReference? SalesCredit { get; set; }
    [JsonPropertyName("patient")] public References.ExternalReference? Patient { get; set; }
    [JsonPropertyName("payor")] public References.ExternalReference? Payor { get; set; }
    [JsonPropertyName("clinic")] public References.ExternalReference? Clinic { get; set; }
    [JsonPropertyName("appliesTo")] public string? AppliesTo { get; set; } = string.Empty;
    [JsonPropertyName("referenceNumber")] public string? ReferenceNumber { get; set; } = string.Empty;
    [JsonPropertyName("documentDate"), JsonConverter(typeof(DateOnlyJsonConverter))] public DateTime? DocumentDate { get; set; }
    [JsonPropertyName("amount")] public decimal? Amount { get; set; }

    public bool HasChanges(Claim? oldClaim)
    {
        if (oldClaim == null) return true;
        if (!Common.AreEqual(DocumentNumber, oldClaim.DocumentNumber)) return true;
        if (!Common.AreEqual(SalesInvoice, oldClaim.SalesInvoice)) return true;
        if (!Common.AreEqual(SalesCredit, oldClaim.SalesCredit)) return true;
        if (!Common.AreEqual(Patient, oldClaim.Patient)) return true;
        if (!Common.AreEqual(Payor, oldClaim.Payor)) return true;
        if (!Common.AreEqual(Clinic, oldClaim.Clinic)) return true;
        if (!Common.AreEqual(AppliesTo, oldClaim.AppliesTo)) return true;
        if (!Common.AreEqual(ReferenceNumber, oldClaim.ReferenceNumber)) return true;
        if (!Common.AreEqual(DocumentDate, oldClaim.DocumentDate)) return true;
        if (!Common.AreEqual(Amount, oldClaim.Amount)) return true;
        return false;
    }

    public static ExternalDocumentReference GetExternalDocumentReferenceFromJson(JsonNode json)
    {
        return new ExternalDocumentReference()
        {
            Id = (Guid?)json.AsObject()["claim"]?["id"],
            DocumentNumber = (string?)json.AsObject()["claim"]?["documentNumber"],
            ExternalReference = (string?)json.AsObject()["claim"]?["externalReference"]
        };
    }

    public void AddTelemetryProperties(MetricTelemetry metricTelemetry)
    {
        metricTelemetry.Properties.Add("ClaimId", Id.ToString());
        metricTelemetry.Properties.Add("ClaimNumber", DocumentNumber);
        if (DocumentDate != null)
            metricTelemetry.Properties.Add("ClaimDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            metricTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            metricTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Patient?.Id != null)
            metricTelemetry.Properties.Add("PatientId", Patient.Id.ToString());
        if (Patient?.Code != null)
            metricTelemetry.Properties.Add("PatientCode", Patient.Code);
        if (Payor?.Id != null)
            metricTelemetry.Properties.Add("PayorId", Payor.Id.ToString());
        if (Payor?.Code != null)
            metricTelemetry.Properties.Add("PayorCode", Payor.Code);
    }

    public void AddEventProperties(EventTelemetry eventTelemetry)
    {
        eventTelemetry.Properties.Add("ClaimId", Id.ToString());
        eventTelemetry.Properties.Add("ClaimNumber", DocumentNumber);
        if (DocumentDate != null)
            eventTelemetry.Properties.Add("ClaimDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            eventTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            eventTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Patient?.Id != null)
            eventTelemetry.Properties.Add("PatientId", Patient.Id.ToString());
        if (Patient?.Code != null)
            eventTelemetry.Properties.Add("PatientCode", Patient.Code);
        if (Payor?.Id != null)
            eventTelemetry.Properties.Add("PayorId", Payor.Id.ToString());
        if (Payor?.Code != null)
            eventTelemetry.Properties.Add("PayorCode", Payor.Code);
    }
}