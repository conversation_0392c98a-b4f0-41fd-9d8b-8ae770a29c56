﻿CREATE VIEW cosium.Payments AS
SELECT Source.id,
       CONCAT('PMT-', FORMAT(CAST(Source.id AS INT), '**********')) AS DocumentNumber,
       CAST(Source.dateencaissee AS DATE) AS DocumentDate,
       CAST(Source.[montant] AS MONEY) * IIF(Source.[typeemetteur] = 1, -1, 1) AS 'Amount',
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.ReglementFacture AS Source

       OUTER APPLY (SELECT (SELECT CONCAT('PMT-', FORMAT(CAST(Source.id AS INT), '**********')) AS documentNumber,
                                  ReglementFacture.[id] AS 'externalReference',
                                  Patients.[Code] AS 'patient.code',
                                  Patients.[ExternalCode] AS 'patient.externalCode',
                                  Patients.[Name] AS 'patient.name',
                                  Clinics.[Code] AS 'clinic.code',
                                  Clinics.[ExternalCode] AS 'clinic.externalCode',
                                  Clinics.[Name] AS 'clinic.name',
                                  CAST(ReglementFacture.dateencaissee AS DATE) AS 'documentDate',
                                  CASE ReglementFacture.typereglement
                                       WHEN 7 THEN 'CARTE'
                                       WHEN 8 THEN 'CHEQUE'
                                       WHEN 9 THEN 'TRESORERIE'
                                       WHEN 40 THEN 'BAPARR'
                                       WHEN 41 THEN 'SOFINCO'
                                       WHEN 66 THEN 'CIC'
                                       WHEN 68 THEN 'CAPAV'
                                    --   WHEN 70 THEN 'COFIDIS'
                                       WHEN 72 THEN 'CM2'
                                       WHEN 74 THEN 'CM1'
                                       WHEN 76 THEN 'LCLN'
                                       WHEN 78 THEN 'LCLL'
                                       WHEN 80 THEN 'BNPCLA'
                                       WHEN 82 THEN 'LCLBAL'
                                       WHEN 84 THEN 'LCLLIB'
                                       WHEN 86 THEN 'PAYBOX'
                                       ELSE 'BANQUE'
                                  END AS 'paymentMethod',
                                  CAST(ReglementFacture.[montant] AS MONEY) * IIF(ReglementFacture.[typeemetteur] = 1, -1, 1) AS 'amount'

                             FROM cosium.[ReglementFacture]
       
                                   LEFT JOIN cosium.Patients
                                          ON ReglementFacture.refclient = Patients.ExternalCode

                                   LEFT JOIN cosium.Clinics
                                          ON ReglementFacture.centre = Clinics.id

                                   LEFT JOIN cosium.typereglement
                                          ON ReglementFacture.typereglement = typereglement.id

                            WHERE ReglementFacture.id = Source.id

                              FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                          ) AS JSON
                   ) AS t1

 WHERE Source.typereglement IN (7,8,9,41,40,66,68,72,74,76,78,80,82,84)
   AND CAST(Source.dateencaissee AS DATE) BETWEEN '1900-01-02' AND GETDATE()