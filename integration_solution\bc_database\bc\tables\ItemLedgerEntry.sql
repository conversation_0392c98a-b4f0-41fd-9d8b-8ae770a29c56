﻿CREATE TABLE [bc].[ItemLedgerEntry](
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[CompanyId] UNIQUEIDENTIFIER,
	[ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
	[ExternalId] UNIQUEIDENTIFIER NOT NULL,
	[EntryNumber] INT,
	[JournalBatchName] NVARCHAR(20),
	[EntryType] NVARCHAR(50),
	[ItemId] UNIQUEIDENTIFIER,
	[ExternalItemId] UNIQUEIDENTIFIER,
	[ItemNumber] NVARCHAR(20),
	[LocationId] UNIQUEIDENTIFIER,
	[ExternalLocationId] UNIQUEIDENTIFIER,
	[LocationCode] NVARCHAR(20),
	[PostingDate] DATETIME2(7),
	[DocumentDate] DATETIME2(7),
	[DocumentType] NVARCHAR(50),
	[DocumentNumber] NVARCHAR(20),
	[ExternalDocumentNumber] NVARCHAR(50),
	[Description] NVARCHAR(100),
	[Open] BIT,
	[Quantity] DECIMAL(18,5),
	[SerialNumber] NVARCHAR(50),
	[DimensionSetId] UNIQUEIDENTIFIER,
	[ExternalDimensionSetNumber] INT,
	[ExternalCreatedOn] DATETIME2(7),
	[ExternalModifiedOn] DATETIME2(7),
	[CreatedOn] [datetime2](7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ModifiedOn] [datetime2](7) NOT NULL DEFAULT SYSUTCDATETIME(),
       CONSTRAINT [PK_ItemLedgerEntry_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
	)
GO

CREATE UNIQUE INDEX [IX_ItemLedgerEntry_CompanyId_ExternalId]
ON [bc].[ItemLedgerEntry] ([CompanyId], [ExternalId])
GO

CREATE INDEX [IX_ItemLedgerEntry_DocumentType_PostingDate_DocumentNumber]
ON [bc].[ItemLedgerEntry] ([DocumentType], [PostingDate], [DocumentNumber])
GO

CREATE INDEX [IX_ItemLedgerEntry_ItemId_PostingDate_EntryType]
ON [bc].[ItemLedgerEntry] ([ItemId], [PostingDate], [EntryType])
GO

CREATE INDEX [IX_ItemLedgerEntry_ItemId_ExternalCreatedOn_JournalBatchName]
ON [bc].[ItemLedgerEntry] ([ExternalCreatedOn], [JournalBatchName], [EntryNumber])
GO