using System;

namespace WSA.Retail.Integration.Simply.Models.Payments
{
    public class SimplyPaymentEntity
    {
        public Guid Id { get; set; }
        public required string DocumentNumber { get; set; }
        public DateTime? DocumentDate { get; set; }
        public string? PaymentMethod { get; set; }
        public decimal? Amount { get; set; }
        public string? ClinicCode { get; set; }
        public string? PatientNumber { get; set; }
        public string? PayerNumber { get; set; }
        public string? AppliesToDocNumber { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }
        public bool IntegrationRequired { get; set; }
        public DateTime? IntegrationDate { get; set; }
    }
}