﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Manage.Core;

namespace WSA.Retail.Integration.Manage.Models.PurchaseOrders;

public class OrderEventConfiguration : IEntityTypeConfiguration<OrderEventEntity>
{
    public void Configure(EntityTypeBuilder<OrderEventEntity> builder)
    {
        builder.ToTable("OrderEvent");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureManageIdField();
        builder.ConfigureManageEventMetadataFields();
        builder.ConfigureManageOrderIdentifersFields();
        builder.ConfigureManageLocationScopedField();
        builder.ConfigureManageSupplierScopedField();
        builder.ConfigureManageEventProcessingInfoFields();
        builder.ConfigureAuditInfoFields();
    }
}
