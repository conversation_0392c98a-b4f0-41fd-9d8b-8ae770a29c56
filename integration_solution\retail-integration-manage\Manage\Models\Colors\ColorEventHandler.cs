﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.Colors;

public class ColorEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ColorEventHandler> logger,
    IColorManageIntegrator colorHandler,
    ColorFromEventHandler fromEventHandler,
    ColorGetByQueryHandler queryHandler) : IEventHandler
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<ColorEventHandler> _logger = logger;
    private readonly IColorManageIntegrator _colorHandler = colorHandler;
    private readonly ColorFromEventHandler _fromEventHandler = fromEventHandler;
    private readonly ColorGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (ev.Data != null)
        {
            var msg = JsonSerializer.Serialize(ev.Data);
            var color = JsonSerializer.Deserialize<Color>(msg, Common.GetJsonOptions());
            if (color == null) return false;
            if (color.Id != Guid.Empty)
            {
                if ((ev.Source ?? "") == _appSettings.ExternalSystemCode)
                {
                    return true;
                }
                var responseObject = await _colorHandler.UpdateManageAsync(color);
                if (responseObject != null)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        return false;
    }

    [Function("ColorGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "colors")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}