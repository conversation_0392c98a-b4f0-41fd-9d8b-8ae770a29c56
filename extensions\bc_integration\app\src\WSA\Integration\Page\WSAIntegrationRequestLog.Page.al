namespace WSA.Integration;

using WSA.Integration.API.V1;
using System.IO;
using System.Security.AccessControl;
using System.Utilities;

page 50185 "WSA Integration Request Log"
{
    ApplicationArea = All;
    Caption = 'Integration Requests';
    PageType = List;
    SourceTable = "WSA Integration Request Log";
    SourceTableView = order(descending);
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                    ToolTip = 'Specifies the value of the Entry No. field.', Comment = '%';
                    Editable = false;
                }

                field("Type"; Rec."Type")
                {
                    ToolTip = 'Specifies the value of the Type field.', Comment = '%';
                }

                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                }

                field(SystemCreatedBy; CreatedUser)
                {
                    Caption = 'Created By';
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.', Comment = '%';
                    Editable = false;
                }

                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.', Comment = '%';
                    Editable = false;
                }

                field(SystemModifiedBy; ModifiedUser)
                {
                    Caption = 'Modified By';
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.', Comment = '%';
                    Visible = false;
                    Editable = false;
                }

                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.', Comment = '%';
                    Visible = false;
                    Enabled = false;
                }

                field(Notes; Rec.Notes)
                {
                    ToolTip = 'Specifies the value of the Notes field.', Comment = '%';
                }

                field("Document No."; Rec."Document No.")
                {
                    ToolTip = 'Specifies the value of the Document No. field.', Comment = '%';
                    Visible = false;
                }

                field("Document Date"; Rec."Document Date")
                {
                    ToolTip = 'Specifies the value of the Document Date field.', Comment = '%';
                    Visible = false;
                }

                field("Clinic No."; Rec."Clinic No.")
                {
                    ToolTip = 'Specifies the value of the Clinic No. field.', Comment = '%';
                    Visible = false;
                }

                field("Patient No."; Rec."Patient No.")
                {
                    ToolTip = 'Specifies the value of the Patient No. field.', Comment = '%';
                    Visible = false;
                }

                field("Payor No."; Rec."Payor No.")
                {
                    ToolTip = 'Specifies the value of the Payor No. field.', Comment = '%';
                    Visible = false;
                }

                field("Vendor No."; Rec."Vendor No.")
                {
                    ToolTip = 'Specifies the value of the Vendor No. field.', Comment = '%';
                    Visible = false;
                }

                field("Color Code"; Rec."Color Code")
                {
                    ToolTip = 'Specifies the value of the Color Code field.', Comment = '%';
                    Visible = false;
                }

                field("Product No."; Rec."Product No.")
                {
                    ToolTip = 'Specifies the value of the Product No. field.', Comment = '%';
                    Visible = false;
                }

                field("Battery Code"; Rec."Battery Code")
                {
                    ToolTip = 'Specifies the value of the Battery Code field.', Comment = '%';
                    Visible = false;
                }

                field("Manufacturer Code"; Rec."Manufacturer Code")
                {
                    ToolTip = 'Specifies the value of the Manufacturer Code field.', Comment = '%';
                    Visible = false;
                }

                field("Product Model Code"; Rec."Product Model Code")
                {
                    ToolTip = 'Specifies the value of the Product Model Code field.', Comment = '%';
                    Visible = false;
                }

                field("Purchase Order No."; Rec."Purchase Order No.")
                {
                    ToolTip = 'Specifies the value of the Purchase Order No. field.', Comment = '%';
                    Visible = false;
                }
            }
        }

        area(FactBoxes)
        {
            part(JsonRequestViewer; "API Log Json Viewer")
            {
                ApplicationArea = All;
            }
        }
    }


    actions
    {
        area(Processing)
        {
            action(processRequest)
            {
                ApplicationArea = All;
                Caption = 'Process Request';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Process;
                ToolTip = 'Process Request';

                trigger OnAction()
                var
                    Handler: Interface "WSA Integration Request";

                begin
                    if Rec.status <> Rec.status::ready then
                        exit;

                    Handler := Rec."Type";
                    Handler.HandleRequest(Rec);
                    CurrPage.Update();
                end;
            }

            action(processAllHeaders)
            {
                ApplicationArea = All;
                Caption = 'Process All Requests';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Process;
                ToolTip = 'ToolTip: ';

                trigger OnAction()
                begin
                    Rec.ProcessAllHeaders();
                    CurrPage.Update();
                end;
            }

            action(setStatusReady)
            {
                ApplicationArea = All;
                Caption = 'Set Status Ready';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Process;
                ToolTip = 'Set Status Ready';

                trigger OnAction()
                var
                    Request: Record "WSA Integration Request Log";

                begin
                    Request.CopyFilters(Rec);
                    Request.SetRange(Status, Request.Status::badRequest);
                    Request.ModifyAll(Status, Request.Status::ready);
                    Commit();

                    CurrPage.Update();
                end;
            }

            action(AttachFile)
            {
                Caption = 'Upload Request Content';
                Image = Attach;
                Promoted = false;
                ToolTip = 'Upload a new json file to the request content.';

                trigger OnAction()
                begin
                    ImportRequestContent();
                    CurrPage.Update(true);
                end;
            }
        }
    }


    views
    {
        view(Batteries)
        {
            Caption = 'Batteries';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(batteries));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Battery Code") { Visible = true; }
                moveafter(Type; "Battery Code")
            }
        }

        view(Clinics)
        {
            Caption = 'Clinics';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(clinics));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Clinic No.") { Visible = true; }
                moveafter(Type; "Clinic No.")
            }
        }

        view(Colors)
        {
            Caption = 'Colors';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(colors));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Color Code") { Visible = true; }
                moveafter(Type; "Color Code")
            }
        }

        view(Manufacturers)
        {
            Caption = 'Manufacturers';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(manufacturers));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Manufacturer Code") { Visible = true; }
                moveafter(Type; "Manufacturer Code")
            }
        }

        view(Patients)
        {
            Caption = 'Patients';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(patients));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Patient No.") { Visible = true; }
                moveafter(Type; "Patient No.")
            }
        }

        view(Payments)
        {
            Caption = 'Payments';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(payments));
            OrderBy = descending("Entry No.");
        }

        view(Payors)
        {
            Caption = 'Payors';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(payors));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Payor No.") { Visible = true; }
                moveafter(Type; "Payor No.")
            }
        }

        view(ProductModels)
        {
            Caption = 'ProductModels';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(models));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Product Model Code") { Visible = true; }
                moveafter(Type; "Product Model Code")
            }
        }

        view(Products)
        {
            Caption = 'Products';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(products));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Product No.") { Visible = true; }
                moveafter(Type; "Product No.")
            }
        }

        view(PurchaseOrders)
        {
            Caption = 'Purchase Orders';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(purchaseOrders));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Purchase Order No.") { Visible = true; }
                modify("Document Date") { Visible = true; }
                modify("Clinic No.") { Visible = true; }
                modify("Vendor No.") { Visible = true; }
                modify(SystemCreatedBy) { Visible = false; }

                moveafter(Status; "Purchase Order No.")
                moveafter("Purchase Order No."; "Document Date")
                moveafter("Document Date"; "Clinic No.")
                moveafter("Clinic No."; "Vendor No.")
                //modify()
            }
        }

        view(PurchaseReceipts)
        {
            Caption = 'Purchase Receipts';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(purchaseReceipts));
            OrderBy = descending("Entry No.");
        }

        view(SalesCredits)
        {
            Caption = 'Sales Credits';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(salesCredits));
            OrderBy = descending("Entry No.");
        }

        view(SalesInvoices)
        {
            Caption = 'Sales Invoices';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(salesInvoices));
            OrderBy = descending("Entry No.");
        }

        view(Vendors)
        {
            Caption = 'Vendors';
            SharedLayout = false;
            Filters = where(status = filter(ready | badRequest | hold), Type = filter(vendors));
            OrderBy = descending("Entry No.");

            layout
            {
                modify("Vendor No.") { Visible = true; }
                moveafter(Type; "Vendor No.")
            }
        }
    }


    trigger OnAfterGetRecord()
    begin
        SetCalculatedFields();
    end;


    trigger OnAfterGetCurrRecord()
    var
        requestStream: InStream;
        responseStream: InStream;

    begin
        Rec.CalcFields("Request Content", "Response Content");
        Rec."Request Content".CreateInStream(requestStream);
        Rec."Response Content".CreateInStream(responseStream);
        CurrPage.JsonRequestViewer.Page.SetContent(requestStream, responseStream);
    end;


    local procedure SetCalculatedFields()
    begin
        CreatedUser := GetUser(Rec.SystemCreatedBy);
        ModifiedUser := GetUser(Rec.SystemModifiedBy);
    end;


    local procedure GetUser(UserId: Guid): Text
    var
        User: Record User;

    begin
        User.SetRange("User Security ID", UserId);
        if User.FindFirst() then
            exit(User."User Name")
        else
            exit('');
    end;


    local procedure ImportRequestContent()
    var
        fileMgt: Codeunit "File Management";
        tempBlob: Codeunit "Temp Blob";
        requestIStream: InStream;
        requestOStream: OutStream;

    begin
        if Rec."Request Content".HasValue() then begin
            Clear(Rec."Request Content");
            Rec.Modify(true);
        end;

        fileMgt.BLOBImport(tempBlob, '');
        tempBlob.CreateInStream(requestIStream);
        Rec."Request Content".CreateOutStream(requestOStream);
        CopyStream(requestOStream, requestIStream);
        Rec.Modify(true);
    end;


    var
        CreatedUser: Text;
        ModifiedUser: Text;
}
