﻿CREATE TABLE [dbo].[PurchaseReturn]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseReturn_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NULL,
    [AlternateNumber]   NVARCHAR(50) NULL,
    [VendorId]          UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NULL,
    [DocumentDate]      DATE NULL,
    [IsActive]          BIT CONSTRAINT [DF_PurchaseReturn_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_PurchaseReturn_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_PurchaseReturn_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT [PK_PurchaseReturn_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_PurchaseReturn_Vendor] FOREIGN KEY (VendorId) REFERENCES [dbo].[Vendor](Id),
    CONSTRAINT [FK_PurchaseReturn_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseReturn_DocumentNumber
ON [dbo].[PurchaseReturn] ([DocumentNumber])
GO