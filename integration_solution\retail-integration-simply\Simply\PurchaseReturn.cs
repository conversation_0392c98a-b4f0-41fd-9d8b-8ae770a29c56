﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;


namespace WSA.Retail.Integration.Simply
{
    /*
    internal class PurchaseReturn : Model.PurchaseReturn
    {
        internal PurchaseReturn() { }

        internal PurchaseReturn(string requestBody) : base(requestBody) { }

        internal PurchaseReturn(Model.PurchaseReturn? baseObject)
        {
            if (baseObject != null)
            {
                this.Id = baseObject.Id;
                this.ExternalSystemCode = baseObject.ExternalSystemCode;
                this.DocumentNumber = baseObject.DocumentNumber;
                this.ExternalReference = baseObject.ExternalReference;
                this.AlternateNumber = baseObject.AlternateNumber;
                this.Vendor = baseObject.Vendor;
                this.Clinic = baseObject.Clinic;
                this.DocumentDate = baseObject.DocumentDate;
                this.PurchaseReturnLines = baseObject.PurchaseReturnLines;
            }
        }

        internal PurchaseReturn(Simply.POHeader dbRecord)
        {
            this.ExternalSystemCode = Common.ExternalSystemCode();
            this.DocumentNumber = "PRO-" + (dbRecord.PONumber?.ToString() ?? "").PadLeft(10, '0');
            this.ExternalReference = dbRecord.PONumber.ToString();
            if (dbRecord.PODate != null)
            {
                this.DocumentDate = DateOnly.FromDateTime((DateTime)dbRecord.PODate);
            }
            if (dbRecord.POSCode != null)
            {
                this.Vendor = new Model.ExternalReference()
                {
                    ExternalCode = dbRecord.POSCode
                };
            }
            if (dbRecord.ClinicCode != null)
            {
                this.Clinic = new Model.ExternalReference()
                {
                    Code = dbRecord.ClinicCode
                };
            }

            if (dbRecord.PODetails?.Count > 0)
            {
                this.PurchaseReturnLines = [];
                foreach (var item in dbRecord.PODetails)
                {
                    var thisLine = new Model.PurchaseReturnLine()
                    {
                        Sequence = item.LineNumber,
                        Quantity = item.Quantity
                    };
                    if (item.ProductCode != null)
                    {
                        thisLine.Product = new Model.ExternalReference()
                        {
                            Code = item.ProductCode
                        };
                    }
                    this.PurchaseReturnLines.Add(thisLine);
                }
            }
        }


        public static void ProcessNewRecords()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ProcessNewRecords));

            List<PurchaseReturn?> purchaseReturns = GetFromSimply();

            log.LogInformation("Fetched {recordCount} PurchaseReturn from simply database", purchaseReturns?.Count);
            if (purchaseReturns?.Count > 0)
            {
                PostList(purchaseReturns);
            }
            else
            {
                log.LogInformation("[{className}].[{procedureName}] No new purchase returns are avalable in the Simply database.",
                    ClassName(), nameof(ProcessNewRecords));
            }
        }

        private static List<PurchaseReturn?> GetFromSimply()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromSimply));

            List<PurchaseReturn?> objectList = [];

            var context = Common.Context();
            try
            {
                var dbRecordList = context.POHeader
                                          .Where(x => x.IntegrationRequired == true && x.POType == "RO")
                                          .Include(y => y.PODetails)
                                          .ToList();
                if (dbRecordList != null)
                {
                    if (dbRecordList.Count > 0)
                    {
                        log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows.",
                                        ClassName(), nameof(GetFromSimply), dbRecordList.Count);

                        foreach (Simply.POHeader dbRecord in dbRecordList)
                        {
                            if (dbRecord != null)
                            {
                                var record = new PurchaseReturn(dbRecord);
                                objectList.Add(record);
                            }
                        }
                        return objectList;
                    }
                    else
                    {
                        log.LogInformation("[{className}].[{procedureName}] No new purchase returns were retrieved from the database.",
                            ClassName(), nameof(GetFromSimply));
                    }
                }
            }
                catch (Exception ex)
                {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch purchase returns from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromSimply), ex.Message);
            }
            return objectList;
        }


        public static void PostList(List<PurchaseReturn?> records)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostList));

            var entitySubscriber = Model.EntitySubscriber.Get(null, Common.ExternalSystemCode(), EntityCode);
            ArgumentNullException.ThrowIfNull(entitySubscriber);
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    foreach (var record in records)
                    {
                        record?.Post(entitySubscriber);
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostList), ex.Message);
                }
            }
        }

        public PurchaseReturn? Post(Model.EntitySubscriber? entitySubscriber = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(Post));

            if (entitySubscriber == null)
            {
                entitySubscriber = Model.EntitySubscriber.Get(null, Common.ExternalSystemCode(), EntityCode);
                ArgumentNullException.ThrowIfNull(entitySubscriber);
            }
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    if (this != null)
                    {
                        ValidateExternalReferences();
                        
                        log.LogInformation("[{className}].[{procedureName}] Sending purchase order: {code} to API\r\n{requestBody}",
                            ClassName(), nameof(Post), this.DocumentNumber, JsonSerializer.Serialize(this, Common.GetJsonOptions()));
                       
                        //var responseObject = this.Upsert();
                        //if (responseObject != null)
                        //{
                        //    log.LogInformation("[{className}].[{procedureName}] Successfully updated patient: {code}", ClassName(), nameof(Post), this.DocumentNumber);
                        //    UpdateIsIntegrated();
                        //    return new(responseObject);
                        //}
                        //else
                        //{
                        //    log.LogError("[{className}].[{procedureName}] Attempt to update purchase return: {code} failed",
                        //        ClassName(), nameof(Post), this.DocumentNumber);
                        //}
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(Post), ex.Message);
                }
            }
            return null;
        }

        private void ValidateExternalReferences()
        {
            ArgumentNullException.ThrowIfNull(this.ExternalSystemCode);
            if (this.Vendor?.Id == null && this.Vendor?.ExternalCode != null)
            {
                this.Vendor = Model.Vendor.GetExternalReference(externalSystemCode: this.ExternalSystemCode, 
                                                                externalCode: this.Vendor.ExternalCode);
                ArgumentNullException.ThrowIfNull(this.Vendor?.Id);
            }

            if (this.Clinic?.Id == null && this.Clinic?.Code != null)
            {
                this.Clinic = Model.Clinic.GetExternalReference(externalSystemCode: this.ExternalSystemCode,
                                                                code: this.Clinic.Code);
                ArgumentNullException.ThrowIfNull(this.Clinic?.Id);
            }

            if (this.Clinic?.Id == null && this.Clinic?.ExternalCode != null)
            {
                this.Clinic = Model.Clinic.GetExternalReference(externalSystemCode: this.ExternalSystemCode,
                                                                externalCode: this.Clinic.ExternalCode);
                ArgumentNullException.ThrowIfNull(this.Clinic?.Id);
            }

            if (this.PurchaseReturnLines?.Count > 0) 
            { 
                foreach(var thisLine in this.PurchaseReturnLines) 
                {
                    if (thisLine != null)
                    {
                        if (thisLine.Product?.Id == null && thisLine.Product?.Code != null)
                        {
                            thisLine.Product = Model.Product.GetExternalReference(externalSystemCode: this.ExternalSystemCode,
                                                                                  code: thisLine.Product.Code);
                            ArgumentNullException.ThrowIfNull(thisLine.Product?.Id);
                        }

                        if (thisLine.Product?.Id == null && thisLine.Product?.ExternalCode != null)
                        {
                            thisLine.Product = Model.Product.GetExternalReference(externalSystemCode: this.ExternalSystemCode,
                                                                                 externalCode: thisLine.Product.ExternalCode);
                            ArgumentNullException.ThrowIfNull(thisLine.Product?.Id);
                        }
                    }
                }
            }
        }

        private bool UpdateIsIntegrated()
        {
            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE simply.PO_Header SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE PONumber = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalReference);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        private static string ClassName()
        {
            return nameof(PurchaseReturn);
        }
    }
        */
}