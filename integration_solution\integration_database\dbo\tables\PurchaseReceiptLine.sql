﻿CREATE TABLE [dbo].[PurchaseReceiptLine]
(
    [Id]                        UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseReceiptLine_Id] DEFAULT NEWID() NOT NULL,
    [PurchaseReceiptId]         UNIQUEIDENTIFIER NOT NULL,
    [Sequence]                  INT NOT NULL,
    [PurchaseOrderLineId]       UNIQUEIDENTIFIER NULL,
    [ProductId]                 UNIQUEIDENTIFIER NULL,
    [Quantity]                  MONEY NULL,
    [SerialNumber]              NVARCHAR(30) NULL,
    [IsActive]                  BIT CONSTRAINT [DF_PurchaseReceiptLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]                 DATETIME2 CONSTRAINT [DF_PurchaseReceiptLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]                DATETIME2 CONSTRAINT [DF_PurchaseReceiptLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT                  [PK_PurchaseReceiptLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT                  [FK_PurchaseReceiptLine_PurchaseReceipt] FOREIGN KEY (PurchaseReceiptId) REFERENCES [dbo].[PurchaseReceipt](Id) ON DELETE CASCADE,
    CONSTRAINT                  [FK_PurchaseReceiptLine_PurchaseOrderLine] FOREIGN KEY (PurchaseOrderLineId) REFERENCES [dbo].[PurchaseOrderLine](Id),
    CONSTRAINT                  [FK_PurchaseReceiptLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseReceiptLine_PurchaseReceiptId_Sequence
ON [dbo].[PurchaseReceiptLine] ([PurchaseReceiptId], [Sequence])
GO