# Simply Integration Flow Diagram

This diagram illustrates the flow of data in the retail-integration-simply project.

```mermaid
classDiagram
    class FromSimplyEventProcessor {
        -AppSettings _appSettings
        -ILogger~FromSimplyEventProcessor~ _logger
        -IPatientHandlerService _patientHandler
        +FromSimplyTimerEventAsync(myTimer) Task
        +MasterDataPostAsync() Task
        +TransactionPost() void
    }
    
    class PatientHandler {
        -ISimplyCustomerRepository _simplyCustomerRepository
        -IPatientService _patientService
        -IEntitySubscriberService _subscriberService
        +ProcessNewRecordsAsync() Task~bool~
        +ProcessPatientListAsync(list) Task~bool~
        +ProcessPatientAsync(id, patient, entitySubscriber) Task~bool~
    }
    
    class SimplyCustomerRepository {
        +GetListAsync() Task~List~SimplyCustomerEntity~~
        +UpdateIsIntegratedAsync(Id) Task~bool~
    }
    
    class SalesInvoice {
        -Static GetFromSimply() List~SalesInvoice~
        -Static PostList(records) void
        -Post(entitySubscriber) SalesInvoice
        -ValidateExternalReferences() void
        -UpdateIsIntegrated() bool
    }
    
    class PurchaseOrder {
        -Static GetFromSimply() List~PurchaseOrder~
        -Static PostList(records) void
        -Post(entitySubscriber) PurchaseOrder
        -ValidateExternalReferences() void
        -UpdateIsIntegrated() bool
    }
    
    class Payment {
        -Static GetFromSimply() List~Payment~
        -Static PostList(records) void
        -Post(entitySubscriber) Payment
        -ValidateExternalReferences() void
        -UpdateIsIntegrated() bool
    }
    
    class BaseService {
        <<external>>
        +UpsertAsync(dto) Task~Model~
    }
    
    FromSimplyEventProcessor --> PatientHandler : MasterDataPost
    FromSimplyEventProcessor --> SalesInvoice : TransactionPost
    FromSimplyEventProcessor --> PurchaseOrder : TransactionPost
    FromSimplyEventProcessor --> Payment : TransactionPost
    
    PatientHandler --> SimplyCustomerRepository : GetListAsync
    PatientHandler --> BaseService : UpsertAsync
    PatientHandler --> SimplyCustomerRepository : UpdateIsIntegratedAsync
    
    SalesInvoice --> BaseService : UpsertAsync
    PurchaseOrder --> BaseService : UpsertAsync
    Payment --> BaseService : UpsertAsync
```

This diagram shows the integration flow in the Simply Integration project:

1. The **FromSimplyEventProcessor** is triggered by a timer and coordinates both master data processing (patients) and transaction processing (sales, purchases, payments)

2. For master data:
   - **PatientHandler** processes customer records from Simply
   - It fetches records using the repository, validates and maps them, then sends them to the central system
   - After successful integration, it updates the integration status in the source system

3. For transactions:
   - Transaction classes (SalesInvoice, PurchaseOrder, Payment) process their respective transactions
   - They follow a similar pattern: get records from Simply, validate references, post to the central system, update integration status

The integration flow follows a unidirectional pattern from Simply to the central system, ensuring that data is synchronized efficiently.