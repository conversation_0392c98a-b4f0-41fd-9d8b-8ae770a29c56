﻿using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Simply.Models.Patients;

public static class PatientExtensions
{
    public static Patient ToPatient(
        this SimplyCustomerEntity entity)
    {
        var patient = new Patient()
        {
            Id = Guid.Empty,
            Code = "",
            ExternalCode = entity.CustomerNumber.ToString(),
            AlternateCode = entity.CustomerNumber.ToString(),
            Name = entity.CustomerNumber.ToString(),
            Address = null,
            Address2 = null,
            City = null,
            Region = null,
            Country = null,
            PostalCode = null,
            Phone = null,
            Email = null,
            IdentificationNumber = null
        };
        patient.Code = Common.GetCode(patient.ExternalCode, "PAT") ?? patient.Code;
        return patient;
    }
}