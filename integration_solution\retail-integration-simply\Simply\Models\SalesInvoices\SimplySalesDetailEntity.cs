namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesDetailEntity
{
    public Guid Id { get; set; }
    public Guid SalesInvoiceId { get; set; }
    public required string SalesType { get; set; }
    public required string InvoiceNumber { get; set; }
    public int Line { get; set; }
    public string? ProductNumber { get; set; }
    public int? Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? Amount { get; set; }
    public decimal? GST { get; set; }
    public decimal? OHSBenefitAmount { get; set; }
    public decimal? OHSTopUpAmount { get; set; }
    public decimal? OHSBenefitAmountInclGST { get; set; }
    public decimal? OHSTopUpAmountInclGST { get; set; }
    public bool? NonStock { get; set; }
    public decimal? Gross { get; set; }
    public decimal? DiscountAmount { get; set; }
    public string? SerialNumber { get; set; }
    public DateTime? WarrantyExpireDate { get; set; }
    public string? FileName { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }

    // Navigation property
    public SimplySalesHeaderEntity? Header { get; set; }
}