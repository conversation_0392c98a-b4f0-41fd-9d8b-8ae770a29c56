﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Models.PurchaseOrders;

public class PurchaseOrderService(
    IOptions<AppSettings> options,
    ILogger<PurchaseOrderService> logger,
    IEventGridPublisher eventGridPublisher,
    IPurchaseOrderRepository repository,
    ICouplingService couplingService,
    IEntitySubscriberService entitySubscriberService,
    IPurchaseReceiptRepository purchaseReceiptService,
    IClinicService clinicService,
    IVendorService vendorService,
    IProductService productService) : IPurchaseOrderService
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<PurchaseOrderService> _logger = logger;
    private readonly IEventGridPublisher _eventGridPublisher = eventGridPublisher;
    private readonly IPurchaseOrderRepository _repository = repository;
    private readonly ICouplingService _couplingService = couplingService;
    private readonly IEntitySubscriberService _entitySubscriberService = entitySubscriberService;
    private readonly IPurchaseReceiptRepository _purchaseReceptRepository = purchaseReceiptService;
    private readonly IClinicService _clinicService = clinicService;
    private readonly IVendorService _vendorService = vendorService;
    private readonly IProductService _productService = productService;

    public async Task<PurchaseOrder?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        var purchaseOrder = await _repository.GetAsync(externalSystemCode, id, documentNumber, externalReference);
        return purchaseOrder;
    }

    public async Task<List<PurchaseOrder>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        var purchaseOrder = await _repository.GetListAsync(externalSystemCode, id, documentNumber, externalReference, documentDate);
        return purchaseOrder;
    }

    public async Task<ExternalDocumentReference?> GetExternalDocumentReferenceAsync(
        string externalSystemCode,
        string externalReference)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        var purchaseOrder = await _repository.GetAsync(externalSystemCode, externalReference: externalReference);
        return purchaseOrder?.ToExternalDocumentReference();
    }

    public async Task<PurchaseOrder?> UpsertAsync(PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        // Validate required properties
        ArgumentException.ThrowIfNullOrEmpty(purchaseOrder.ExternalSystemCode);
        ArgumentException.ThrowIfNullOrEmpty(purchaseOrder.DocumentNumber);

        // Fetch Entity Subscriber
        var subscriber = await _entitySubscriberService.GetAsync(
            purchaseOrder.ExternalSystemCode,
            EntityType.PurchaseOrder.GetEntityCode());

        ArgumentNullException.ThrowIfNull(subscriber);
        if (subscriber.FromExternalSystem ?? false)
        {
            // Retrieve existing PurchaseOrder from repository
            PurchaseOrder? oldPurchaseOrder = null;
            if (!string.IsNullOrWhiteSpace(purchaseOrder.DocumentNumber))
            {
                oldPurchaseOrder = await _repository.GetAsync(
                    purchaseOrder.ExternalSystemCode,
                    documentNumber: purchaseOrder.DocumentNumber);
            }
            else if (!string.IsNullOrWhiteSpace(purchaseOrder.ExternalReference))
            {
                oldPurchaseOrder = await _repository.GetAsync(
                    purchaseOrder.ExternalSystemCode,
                    externalReference: purchaseOrder.ExternalReference);
            }

            if (oldPurchaseOrder == null)
            {
                _logger.LogCustomInformation(_appSettings.AppName,
                    $"PurchaseOrder {purchaseOrder.DocumentNumber} does not exist in the database and must be inserted.");
                var isSuccess = await _repository.InsertAsync(purchaseOrder);
                if (isSuccess)
                {
                    _logger.LogCustomInformation(_appSettings.AppName,
                        $"PurchaseOrder {purchaseOrder.DocumentNumber} was successfully inserted in the database.");
                    await RaiseEventAsync(purchaseOrder);
                }
                else
                {
                    _logger.LogCustomWarning(_appSettings.AppName,
                        $"PurchaseOrder {purchaseOrder.DocumentNumber} failed to insert.");
                }
            }
            else
            {
                purchaseOrder.Id = oldPurchaseOrder.Id;
                if (purchaseOrder.HasChanges(oldPurchaseOrder))
                {
                    // Update existing PurchaseOrder
                    _logger.LogCustomInformation(_appSettings.AppName,
                        $"PurchaseOrder {purchaseOrder.DocumentNumber} has changed from database and must be updated.");
                    var isSuccess = await _repository.UpdateAsync(purchaseOrder);
                    if (isSuccess)
                    {
                        _logger.LogCustomInformation(_appSettings.AppName,
                            $"PurchaseOrder {purchaseOrder.DocumentNumber} was successfully updated in the database.");
                        await RaiseEventAsync(purchaseOrder);
                    }
                    else
                    {
                        _logger.LogCustomWarning(_appSettings.AppName,
                            $"PurchaseOrder {purchaseOrder.DocumentNumber} failed to update.");
                    }
                }
                else
                {
                    _logger.LogCustomInformation(_appSettings.AppName,
                        $"PurchaseOrder {purchaseOrder.DocumentNumber} has not changed.  Do nothing.");
                }
            }
        }

        // Upsert coupling record
        if (purchaseOrder.Id != Guid.Empty)
        {
            List<Task> couplingTasks = [];

            var coupling = new Coupling
            {
                ExternalSystem = new() { Code = purchaseOrder.ExternalSystemCode },
                Entity = new() { Code = EntityType.PurchaseOrder.GetEntityCode() },
                RecordId = purchaseOrder.Id,
                ExternalCode = purchaseOrder.ExternalReference
            };
            couplingTasks.Add(_couplingService.UpsertAsync(coupling));

            foreach (var line in purchaseOrder.Lines)
            {
                if (line.Id != Guid.Empty && !String.IsNullOrWhiteSpace(line.ExternalReference))
                {
                    var lineCoupling = new Coupling
                    {
                        ExternalSystem = new() { Code = purchaseOrder.ExternalSystemCode },
                        Entity = new() { Code = EntityType.PurchaseOrder.GetEntityCode() },
                        RecordId = line.Id,
                        ExternalCode = line.ExternalReference
                    };
                    couplingTasks.Add(_couplingService.UpsertAsync(lineCoupling));
                }
            }

            await Task.WhenAll(couplingTasks);
        }
        return purchaseOrder;
    }

    public async Task RaiseEventAsync(PurchaseOrder purchaseOrder)
    {
        ArgumentNullException.ThrowIfNull(purchaseOrder.ExternalSystemCode);
        ArgumentNullException.ThrowIfNull(purchaseOrder.Id);
        await _eventGridPublisher.RaiseEventAsync(
            _appSettings, 
            EntityType.PurchaseOrder.GetEventName(), 
            purchaseOrder.Id.ToString() ?? "", 
            purchaseOrder);
    }

    public async Task RaiseMockEventAsync(PurchaseOrder purchaseOrder)
    {
        ArgumentNullException.ThrowIfNull(purchaseOrder.ExternalSystemCode);
        ArgumentNullException.ThrowIfNull(purchaseOrder.Id);
        await _eventGridPublisher.RaiseMockEventAsync(
            EntityType.PurchaseOrder.GetEventName(),
            purchaseOrder.Id.ToString() ?? "",
            purchaseOrder);
    }

    public async Task<List<PurchaseReceiptLine>> GetReceiptLinesAsync(PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (purchaseOrder.Id == Guid.Empty)
        {
            _logger.LogCustomWarning(_appSettings.AppName,
                "Cannot get receipt lines for an unidentified purchase order.");
            return [];
        }

        var receiptLines = await _purchaseReceptRepository
            .GetReceiptLinesByPurchaseOrderAsync(_appSettings.ExternalSystemCode, purchaseOrder.Id);

        return receiptLines;
    }

    public async Task ValidateExternalReferencesAsync(string externalSystemCode, PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        purchaseOrder.Clinic = await _clinicService.ValidateExternalReferenceAsync(externalSystemCode, purchaseOrder.Clinic);
        purchaseOrder.Vendor = await _vendorService.ValidateExternalReferenceAsync(externalSystemCode, purchaseOrder.Vendor);
        foreach(var purchaseOrderLine in purchaseOrder.Lines)
        {
            purchaseOrderLine.Product = await _productService.ValidateExternalReferenceAsync(externalSystemCode, purchaseOrderLine.Product);
        }
    }
}