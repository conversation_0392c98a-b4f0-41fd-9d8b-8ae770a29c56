﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Models.Clinics;

public class Clinic : IClinic
{
    // IMasterData
    [JsonPropertyName("id")] public Guid Id { get; set; } = Guid.Empty;
    [JsonPropertyName("externalSystemCode")] public string? ExternalSystemCode { get; set; }
    [JsonPropertyName("code")] public required string Code { get; set; }
    [JsonPropertyName("externalCode")] public string? ExternalCode { get; set; }
    [JsonPropertyName("name")] public string? Name { get; set; }
    [JsonPropertyName("alternateCode")] public string? AlternateCode { get; set; }

    [JsonPropertyName("company")] public ExternalReference? Company { get; set; }

    // IAddress
    [JsonPropertyName("address")] public string? Address { get; set; }
    [JsonPropertyName("address2")] public string? Address2 { get; set; }
    [JsonPropertyName("city")] public string? City { get; set; }
    [JsonPropertyName("region")] public string? Region { get; set; }
    [JsonPropertyName("country")] public string? Country { get; set; }
    [JsonPropertyName("postalCode")] public string? PostalCode { get; set; }

    // IContact
    [JsonPropertyName("phone")] public string? Phone { get; set; }
    [JsonPropertyName("email")] public string? Email { get; set; }

    // IAuditInfo
    [JsonPropertyName("createdOn")] public DateTime CreatedOn { get; set; }
    [JsonPropertyName("modifiedOn")] public DateTime ModifiedOn { get; set; }

    public bool HasChanges(IMasterData? oldEntity)
    {
        if (oldEntity is not IClinic oldClinic) return true;
        return HasChanges(oldClinic);
    }

    public bool HasChanges(IClinic? oldEntity)
    {

        if (oldEntity == null) return true;
        if (((IMasterData)this).MasterDataHasChanges((IMasterData)oldEntity)) return true;
        if (((IAddress)this).AddressHasChanges((IAddress)oldEntity)) return true;

        if (!Common.AreEqual(Company, oldEntity.Company)) return true;
        return false;
    }

    public Dictionary<string, string>? GetLoggingProperties()
    {
        return this.GetLoggingProperties();
    }

    public void AddTelemetryProperties(MetricTelemetry metricTelemetry)
    {
        this.AddTelemetryProperties(metricTelemetry);
    }

    public void AddEventProperties(EventTelemetry eventTelemetry)
    {
        this.AddEventProperties(eventTelemetry);
    }
}