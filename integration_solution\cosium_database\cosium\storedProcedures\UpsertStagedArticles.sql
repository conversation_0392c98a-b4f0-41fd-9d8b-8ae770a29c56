﻿CREATE PROCEDURE [cosium].[UpsertStagedArticles]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.articles
          SET [numserie] = Source.[numserie],
              [type] = Source.[type],
              [statut] = Source.[statut],
              [quantite] = Source.[quantite],
              [prixvente] = Source.[prixvente],
              [refhistoappareil] = Source.[refhistoappareil],
              [centre] = Source.[centre],
              [commentaire] = Source.[commentaire],
              [savencours] = Source.[savencours],
              [cache] = Source.[cache],
              [remise] = Source.[remise],
              [typeremise] = Source.[typeremise],
              [libelleremiseart] = Source.[libelleremiseart],
              [appcouleur] = Source.[appcouleur],
              [appmatiere] = Source.[appmatiere],
              [depot] = Source.[depot],
              [seuilarticle] = Source.[seuilarticle],
              [datecreation] = Source.[datecreation],
              [prixprestation] = Source.[prixprestation],
              [datemodif] = Source.[datemodif],
              [prixachat] = Source.[prixachat]

         FROM cosium.articles

              INNER JOIN cosium.staging_articles AS Source
                      ON articles.id = Source.id

        WHERE articles.datemodif <> Source.datemodif;

       INSERT INTO cosium.articles (
              [id],
              [numserie],
              [type],
              [statut],
              [quantite],
              [prixvente],
              [refhistoappareil],
              [centre],
              [commentaire],
              [savencours],
              [cache],
              [remise],
              [typeremise],
              [libelleremiseart],
              [appcouleur],
              [appmatiere],
              [depot],
              [seuilarticle],
              [datecreation],
              [prixprestation],
              [datemodif],
              [prixachat])

       SELECT Source.[id],
              Source.[numserie],
              Source.[type],
              Source.[statut],
              Source.[quantite],
              Source.[prixvente],
              Source.[refhistoappareil],
              Source.[centre],
              Source.[commentaire],
              Source.[savencours],
              Source.[cache],
              Source.[remise],
              Source.[typeremise],
              Source.[libelleremiseart],
              Source.[appcouleur],
              Source.[appmatiere],
              Source.[depot],
              Source.[seuilarticle],
              Source.[datecreation],
              Source.[prixprestation],
              Source.[datemodif],
              Source.[prixachat]

         FROM cosium.staging_articles AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.articles WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_articles
       
       COMMIT TRANSACTION;