﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Payors;

namespace WSA.Retail.Integration.Manage.Models.Payors;

public class PayorFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PayorEventHandler> logger,
    IPayorService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<PayorEventHubEvent, Payor> eventHubEntityAdapter)
    : GenericFromEventHandler<
        PayorEventHubEvent,
        IPayorService,
        Payor,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Payor)

{
    private readonly ILogger<PayorEventHandler> _logger = logger;


    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }



    protected override Guid GetEventEntityId(PayorEventHubEvent entity)
    {
        return entity.Id ?? Guid.Empty;
    }

    protected override string? GetEventEntityName(PayorEventHubEvent entity)
    {
        return entity.Name;
    }
}