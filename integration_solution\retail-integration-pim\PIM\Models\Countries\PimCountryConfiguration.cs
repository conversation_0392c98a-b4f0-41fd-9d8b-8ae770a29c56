using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Countries
{
    public class PimCountryConfiguration : IEntityTypeConfiguration<PimCountryEntity>
    {
        public void Configure(EntityTypeBuilder<PimCountryEntity> builder)
        {
            builder.ToTable("Country", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureCodeIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();

            builder.<PERSON><PERSON>any(x => x.<PERSON>)
                .WithMany(x => x.Countries)
                .UsingEntity<PimCountryBrandEntity>(
                    j => j
                        .<PERSON>(cb => cb.BrandEntity)
                        .WithMany()
                        .HasForeign<PERSON>ey(pc => pc.BrandId),
                    j => j
                        .<PERSON>(cb => cb.CountryEntity)
                        .WithMany()
                        .<PERSON><PERSON>(cb => cb.CountryId),
                    j =>
                    {
                        j.<PERSON>(cb => cb.Id);
                        j.ToTable("CountryBrand", "pim");
                    }
                );
        }
    }
}