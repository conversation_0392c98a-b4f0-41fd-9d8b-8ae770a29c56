﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>bc_database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{a6fdeeea-7bc7-4871-85af-f9531b09d381}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>bc_database</RootNamespace>
    <AssemblyName>bc_database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="bc" />
    <Folder Include="bc\schemas" />
    <Folder Include="bc\tables" />
    <Folder Include="bc\storedprocedures" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="bc\schemas\bc.sql" />
    <Build Include="bc\tables\CustomerLedgerEntry.sql" />
    <Build Include="bc\tables\CustomerLedgerEntry_Staging.sql" />
    <Build Include="bc\storedprocedures\CustomerLedgerEntryUpsert.sql" />
    <Build Include="bc\tables\PurchaseOrder_Staging.sql" />
    <Build Include="bc\storedprocedures\PurchaseOrderUpsert.sql" />
    <Build Include="bc\tables\PurchaseOrder.sql" />
    <Build Include="bc\tables\SalesInvoice_Staging.sql" />
    <Build Include="bc\tables\SalesInvoice.sql" />
    <Build Include="bc\tables\SalesCredit_Staging.sql" />
    <Build Include="bc\tables\SalesCredit.sql" />
    <Build Include="bc\storedprocedures\SalesInvoiceUpsert.sql" />
    <Build Include="bc\storedprocedures\SalesCreditUpsert.sql" />
    <Build Include="bc\tables\Customer.sql" />
    <Build Include="bc\tables\Customer_Staging.sql" />
    <Build Include="bc\storedprocedures\CustomerUpsert.sql" />
    <Build Include="bc\tables\ItemLedgerEntry_Staging.sql" />
    <Build Include="bc\tables\ItemLedgerEntry.sql" />
    <Build Include="bc\storedprocedures\ItemLedgerEntryUpsert.sql" />
  </ItemGroup>
</Project>