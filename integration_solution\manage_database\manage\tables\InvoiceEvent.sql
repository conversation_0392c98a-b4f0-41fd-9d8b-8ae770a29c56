﻿CREATE TABLE [manage].[InvoiceEvent]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_InvoiceEvent_Id] DEFAULT NEWID() NOT NULL,
    [EventType]         NVARCHAR(100),
    [MessageId]         UNIQUEIDENTIFIER,
    [SaleId]            UNIQUEIDENTIFIER,
    [ExternalNumber]    NVARCHAR(50),
    [LocationId]        UNIQUEIDENTIFIER,
    [PatientId]         UNIQUEIDENTIFIER,
    [FunderId]          UNIQUEIDENTIFIER,
    [Timestamp]         DATETIME2,
    [LocalTimestamp]    DATETIME2,
    [BlobPath]          NVARCHAR(255),
    [Status]            INT CONSTRAINT [DF_InvoiceEvent_Status] DEFAULT 0 NOT NULL,
    [ProcessedOn]       DATETIME2,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_InvoiceEvent_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_InvoiceEvent_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_InvoiceEvent_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_InvoiceEvent_MessageId
ON [manage].[InvoiceEvent] ([MessageId])
GO

CREATE INDEX IX_InvoiceEvent_Status
ON [manage].[InvoiceEvent] ([Status], [MessageId])
GO

CREATE INDEX IX_InvoiceEvent_SaleId
ON [manage].[InvoiceEvent] ([SaleId])
GO