﻿using GraphQL;
using GraphQL.Client.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.PIM.Queries;


namespace WSA.Retail.Integration.PIM;

public class TokenService(
    IOptions<AppSettings> appSettings,
    ILogger<TokenService> logger,
    GraphQLHttpClient graphQLClient
    ) : LoggingBase<GraphQLRequest>(logger), ITokenService
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly GraphQLHttpClient _graphQLClient = graphQLClient;

    public async Task<string?> GetTokenAsync()
    {
        LogMethodStart();

        var loginRequest = new GraphQLRequest
        {
            Query = @"
                query ($clientAppInput: ClientAppInput!) {
                    auth {
                        getJwtToken(clientAppInput: $clientAppInput) {
                            accessToken
                        }
                    }
                }",
            Variables = new
            {
                clientAppInput = new
                {
                    clientId = _appSettings.PimClientId,
                    clientSecret = _appSettings.PimClientSecret
                }
            }
        };

        try
        {
            var response = await _graphQLClient.SendQueryAsync<GraphQLResponse>(loginRequest);
            return response.Data.Auth.GetJwtToken.AccessToken;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }
}

public class GraphQLResponse
{
    [JsonPropertyName("auth")] public required AuthData Auth { get; set; }
}

public class GraphQLData
{
    
}

public class AuthData
{
    [JsonPropertyName("getJwtToken")] public required JwtTokenResult GetJwtToken { get; set; }
}

public class JwtTokenResult
{
    [JsonPropertyName("accessToken")] public required string AccessToken { get; set; }
}