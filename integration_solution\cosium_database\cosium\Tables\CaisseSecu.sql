﻿CREATE TABLE [cosium].[CaisseSecu] (
    [id]                    NVARCHAR (50)  NOT NULL,
    [cache]                 NVARCHAR (50)  NULL,
    [caissegest]            NVARCHAR (50)  NULL,
    [centregest]            NVARCHAR (50)  NULL,
    [codeinformatique]      NVARCHAR (50)  NULL,
    [coderegime]            NVARCHAR (50)  NULL,
    [addressline1]          NVARCHAR (100) NULL,
    [addressline2]          NVARCHAR (100) NULL,
    [cscode]                NVARCHAR (100) NULL,
    [cscodecomplementaire]  NVARCHAR (50)  NULL,
    [cscommentaire]         NVARCHAR (300) NULL,
    [postcode]              NVARCHAR (300) NULL,
    [fax]                   NVARCHAR (100) NULL,
    [csnom]                 NVARCHAR (300) NULL,
    [country]               NVARCHAR (100) NULL,
    [phone]                 NVARCHAR (100) NULL,
    [town]                  NVARCHAR (100) NULL,
    [organismedestinataire] NVARCHAR (50)  NULL,
    [teletransfse]          NVARCHAR (50)  NULL,
    [version]               NVARCHAR (50)  NULL,
    [datemodif]             NVARCHAR (100) NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_CaisseSecu_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_CaisseSecu_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_CaisseSecu] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].[CaisseSecu_UpdateModified]
ON [cosium].[CaisseSecu]
AFTER UPDATE 
AS
   UPDATE [cosium].[CaisseSecu]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[CaisseSecu].[id] = i.[id]
GO