namespace WSA.Integration;

codeunit 50139 "Color Events"
{
    TableNo = "Color";

    trigger OnRun()
    begin
        RaiseEvent(Rec);
    end;

    [EventSubscriber(ObjectType::Table, Database::Color, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Color;
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;


    [EventSubscriber(ObjectType::Table, Database::Color, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Color;
        var xRec: Record Color;
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;

    local procedure RaiseEventAsync(Color: Record Color)
    var
        SessionId: Integer;

    begin
        Session.StartSession(SessionId, Codeunit::"Color Events", CompanyName, Color);
    end;


    local procedure RaiseEvent(Color: Record Color)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Color."Code" = '' then
            exit;

        if Color.Description = '' then
            exit;

        if IsNullGuid(Color.SystemId) then
            exit;

        JObject := Common.ColorToJson(Color);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'colors', Format(Color.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}