# Repository Layer Class Diagram

This diagram illustrates the repository layer classes in the WSA Retail Integration Model project.

```mermaid
classDiagram
    class IClinicRepository {
        <<interface>>
        +GetAsync(id) Task~ClinicEntity~
        +GetAsync(code) Task~ClinicEntity~
        +GetListAsync() Task~List~ClinicEntity~~
        +GetList<PERSON>ync(codes) Task~List~ClinicEntity~~
        +UpsertAsync(entity) Task~ClinicEntity~
    }

    class ClinicRepository {
        -IDbContextFactory~IntegrationContext~ _dbContextFactory
        -ILogger~ClinicRepository~ _logger
        -AppSettings _appSettings
        +GetAsync(id) Task~ClinicEntity~
        +GetAsync(code) Task~ClinicEntity~
        +GetListAsync() Task~List~ClinicEntity~~
        +GetListAsync(codes) Task~List~ClinicEntity~~
        +UpsertAsync(entity) Task~ClinicEntity~
    }

    class ISalesOrderRepository {
        <<interface>>
        +GetAsync(id) Task~SalesOrderEntity~
        +GetAsync(documentNumber) Task~SalesOrderEntity~
        +GetListAsync() Task~List~SalesOrderEntity~~
        +GetListAsync(documentNumbers) Task~List~SalesOrderEntity~~
        +UpsertAsync(entity) Task~SalesOrderEntity~
    }

    class SalesOrderRepository {
        -IDbContextFactory~IntegrationContext~ _dbContextFactory
        -ILogger~SalesOrderRepository~ _logger
        -AppSettings _appSettings
        +GetAsync(id) Task~SalesOrderEntity~
        +GetAsync(documentNumber) Task~SalesOrderEntity~
        +GetListAsync() Task~List~SalesOrderEntity~~
        +GetListAsync(documentNumbers) Task~List~SalesOrderEntity~~
        +UpsertAsync(entity) Task~SalesOrderEntity~
    }

    class BaseRepositoryService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #IDbContextFactory~IntegrationContext~ _dbContextFactory
    }

    class IntegrationContext {
        +DbSet~ClinicEntity~ ClinicEntity
        +DbSet~PatientEntity~ PatientEntity
        +DbSet~ProductEntity~ ProductEntity
        +DbSet~SalesOrderEntity~ SalesOrderEntity
        +DbSet~SalesOrderLineEntity~ SalesOrderLineEntity
        ... (other entities)
        +OnModelCreating(modelBuilder) void
        +OnConfiguring(optionsBuilder) void
    }

    IClinicRepository <|.. ClinicRepository
    BaseRepositoryService <|-- ClinicRepository
    
    ISalesOrderRepository <|.. SalesOrderRepository
    BaseRepositoryService <|-- SalesOrderRepository
    
    ClinicRepository --> IntegrationContext : uses
    SalesOrderRepository --> IntegrationContext : uses
```

This diagram shows the repository layer with interfaces and implementations. Each entity type has its own repository interface (e.g., `IClinicRepository`, `ISalesOrderRepository`) and implementation (e.g., `ClinicRepository`, `SalesOrderRepository`). Repositories inherit from the `BaseRepositoryService` class, which provides common functionality. All repositories use the `IntegrationContext` to interact with the database.