﻿namespace WSA.Retail.Integration.Models.SalesInvoices;

public static class SalesInvoiceLineMapper
{
    public static SalesInvoiceLineEntity ToEntity(
        this SalesInvoiceLine salesInvoiceLine,
        Guid salesInvoiceId)
    {
        return new SalesInvoiceLineEntity()
        {
            Id = salesInvoiceLine.Id == Guid.Empty ? Guid.NewGuid() : salesInvoiceLine.Id,
            SalesInvoiceId = salesInvoiceId,
            Sequence = salesInvoiceLine.Sequence,
            SalesOrderLineId = salesInvoiceLine.SalesOrderLine?.Id,
            ProductId = salesInvoiceLine.Product?.Id,
            Description = salesInvoiceLine.Description,
            Quantity = salesInvoiceLine.Quantity,
            SerialNumber = salesInvoiceLine.SerialNumber,
            UnitPrice = salesInvoiceLine.UnitPrice,
            GrossAmount = salesInvoiceLine.GrossAmount,
            DiscountAmount = salesInvoiceLine.DiscountAmount,
            AmountExclTax = salesInvoiceLine.AmountExclTax,
            TaxAmount = salesInvoiceLine.TaxAmount,
            AmountInclTax = salesInvoiceLine.AmountInclTax,
            CreatedOn = salesInvoiceLine.CreatedOn,
            ModifiedOn = salesInvoiceLine.ModifiedOn,
            SalesInvoiceEntity = new SalesInvoiceEntity { Id = salesInvoiceId }
        };
    }

    public static References.ExternalDocumentLineReference ToExternalDocumentLineReference(
        this SalesInvoiceLine salesInvoiceLine)
    {
        return new References.ExternalDocumentLineReference()
        {
            Id = salesInvoiceLine.Id,
            Sequence = salesInvoiceLine.Sequence
        };
    }
}