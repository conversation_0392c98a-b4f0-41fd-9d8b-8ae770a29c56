using Microsoft.Identity.Client;
using System;
using WSA.Retail.Integration.Events;

namespace WSA.Retail.Integration.Simply.Models.Payments
{
    public class SimplyCashReceiptsEntity
    {
        public Guid Id { get; set; }
        public string? PaymentMethodCode { get; set; }
        public DateTime PaymentDate { get; set; }
        public string? DocumentNumber { get; set; }
        public string? ClinicCode { get; set; }
        public decimal? Amount { get; set; }
        public string? Description { get; set; }
        public string? BankName { get; set; }
        public string? BalanceAccountNumber { get; set; }
        public string? AppliesToDocNumber { get; set; }
        public string? FileName { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }
        public bool IntegrationRequired { get; set; }
        public DateTime? IntegrationDate { get; set; }
    }
}