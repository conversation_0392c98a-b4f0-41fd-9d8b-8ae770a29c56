﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Data;

public class ModifiedOnInterceptor : SaveChangesInterceptor
{
    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        UpdateAdded(eventData);
        UpdateModifiedOn(eventData);
        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        UpdateAdded(eventData);
        UpdateModifiedOn(eventData);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private static void UpdateAdded(DbContextEventData eventData)
    {
        var context = eventData.Context;
        if (context == null) return;

        foreach (var entry in context.ChangeTracker.Entries().Where(e => e.State == EntityState.Added))
        {
            var idProperty = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "Id" && p.Metadata.ClrType == typeof(Guid));
            if (idProperty != null && idProperty.CurrentValue is Guid currentId && currentId == Guid.Empty)
            {
                idProperty.CurrentValue = Guid.NewGuid();
            }

            // Optional: Set CreatedOn for new entities
            var createdOnProp = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "CreatedOn");
            if (createdOnProp != null)
            {
                createdOnProp.CurrentValue = DateTime.UtcNow;
            }

            // Optional: Also set ModifiedOn initially
            var modifiedOnProp = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "ModifiedOn");
            if (modifiedOnProp != null)
            {
                modifiedOnProp.CurrentValue = DateTime.UtcNow;
            }
        }
    }

    private static void UpdateModifiedOn(DbContextEventData eventData)
    {
        var context = eventData.Context;
        if (context == null) return;

        foreach (var entry in context.ChangeTracker.Entries().Where(e => e.State == EntityState.Modified))
        {
            var entityType = entry.Entity.GetType().Name;

            var property = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "ModifiedOn");
            if (property != null)
            {
                var oldValue = property.OriginalValue;
                var newValue = DateTime.UtcNow;
                property.CurrentValue = newValue;
                property.IsModified = true;

            }
            // Prevent CreatedOn from being updated
            var createdOnProp = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "CreatedOn");
            if (createdOnProp != null)
            {
                createdOnProp.IsModified = false;
            }
        }
    }
}