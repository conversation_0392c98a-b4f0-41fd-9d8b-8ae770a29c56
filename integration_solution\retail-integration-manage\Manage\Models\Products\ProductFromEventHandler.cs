﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Products;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ProductFromEventHandler> logger,
    IProductService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<ProductEventHubEvent, Product> eventHubEntityAdapter)
    : GenericFromEventHandler<
        ProductEventHubEvent,
        IProductService,
        Product,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Product)

{
    private readonly ILogger<ProductFromEventHandler> _logger = logger;

    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }



    protected override Guid GetEventEntityId(ProductEventHubEvent entity)
    {
        return entity.Id;
    }

    protected override string? GetEventEntityName(ProductEventHubEvent entity)
    {
        return entity.Name;
    }
}