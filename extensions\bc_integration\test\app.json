{"id": "c81fae45-793a-45b3-aed6-656001058711", "name": "Retail Integration Connector - Test", "publisher": "WSA", "version": "********", "brief": "Customization required to test the Retail Integration Suite.", "description": "This app provides the customization required to test the Retail Integration Suite.", "privacyStatement": "http://www.wsa.com", "EULA": "http://www.wsa.com", "help": "http://www.wsa.com", "url": "http://www.wsa.com", "logo": "../logo/wsa_Logo.jpg", "dependencies": [{"id": "d62f6908-2517-11eb-adc1-0242ac120002", "publisher": "WSA", "name": "WSA Global Template", "version": "*******"}, {"id": "bd339f32-f9e0-455c-a8e1-252f149a41e0", "name": "Retail Integration Connector", "publisher": "WSA", "version": "*******"}, {"id": "5d86850b-0d76-4eca-bd7b-951ad998e997", "name": "Tests-TestLibraries", "publisher": "Microsoft", "version": "********"}, {"id": "9856ae4f-d1a7-46ef-89bb-6ef056398228", "name": "System Application Test Library", "publisher": "Microsoft", "version": "********"}, {"id": "5095f467-0a01-4b99-99d1-9ff1237d286f", "publisher": "Microsoft", "name": "Library Variable Storage", "version": "********"}], "screenshots": [], "platform": "*******", "application": "********", "idRanges": [{"from": 50100, "to": 59999}], "resourceExposurePolicy": {"allowDebugging": true, "allowDownloadingSource": true, "includeSourceInSymbolFile": true}, "runtime": "12.0", "features": ["NoImplicitWith", "TranslationFile", "GenerateCaptions"], "target": "OnPrem", "applicationInsightsConnectionString": "InstrumentationKey=56e07e20-92ee-44e2-8f32-5f105db71078;IngestionEndpoint=https://westeurope-5.in.applicationinsights.azure.com/;LiveEndpoint=https://westeurope.livediagnostics.monitor.azure.com/"}