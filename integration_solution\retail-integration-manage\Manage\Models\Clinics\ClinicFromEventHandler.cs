﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Manage.Models.Countries;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Clinics;

namespace WSA.Retail.Integration.Manage.Models.Clinics;

public class ClinicFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ClinicFromEventHandler> logger,
    IClinicService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<ClinicEventHubEvent, Clinic> eventHubEntityAdapter,
    IInventoryCountryService inventoryCountryService)
    : GenericFromEventHandler<
        ClinicEventHubEvent,
        IClinicService,
        Clinic,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Clinic)

{
    private readonly ILogger<ClinicFromEventHandler> _logger = logger;
    private readonly IInventoryCountryService _inventoryCountryService = inventoryCountryService;

    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }

    protected override Guid GetEventEntityId(ClinicEventHubEvent entity)
    {
        return entity.LocationID ?? Guid.Empty;
    }

    protected override string? GetEventEntityName(ClinicEventHubEvent entity)
    {
        return entity.Name;
    }

    protected override async Task AfterConvertToEntity(Clinic entity)
    {
        if (entity.Country != null && entity.Country.Length > 2)
        {
            var inventoryCountry = await _inventoryCountryService.GetCountryByNameAsync(entity.Country);
            if (inventoryCountry != null)
            {
                entity.Country = inventoryCountry.Code;
            }
        }
    }
}