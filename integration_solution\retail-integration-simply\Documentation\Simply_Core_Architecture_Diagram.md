# Simply Integration Core Architecture Diagram

This diagram shows the core architecture of the retail-integration-simply project.

```mermaid
classDiagram
    class IMasterDataEntityService {
        <<interface>>
        +ProcessNewRecordsAsync() Task~bool~
    }
    
    class BaseEntityService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #SimplyContext _simplyContext
        +BaseEntityService(appSettings, logger, simplyContext)
    }
    
    class BaseRepositoryService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #SimplyContext _simplyContext
        +BaseRepositoryService(appSettings, logger, simplyContext)
    }
    
    class IPatientHandlerService {
        <<interface>>
    }
    
    class PatientHandler {
        -ISimplyCustomerRepository _simplyCustomerRepository
        -IPatientService _patientService
        -IEntitySubscriberService _subscriberService
        +ProcessNewRecordsAsync() Task~bool~
        +ProcessPatientListAsync(list) Task~bool~
        +ProcessPatientAsync(id, patient, entitySubscriber) Task~bool~
    }
    
    class ISimplyCustomerRepository {
        <<interface>>
        +GetListAsync() Task~List~SimplyCustomerEntity~~
        +UpdateIsIntegratedAsync(Id) Task~bool~
    }
    
    class SimplyCustomerRepository {
        +GetListAsync() Task~List~SimplyCustomerEntity~~
        +UpdateIsIntegratedAsync(Id) Task~bool~
    }
    
    class SimplyCustomerEntity {
        +Guid Id
        +int CustomerNumber
        +string Name
        +string? Address
        +string? AddressLine2
        +string? City
        +string? StateRegion
        +string? Phone
        +string? Email
        +string? ContactName
        +string? ClinicCode
        +string? FileName
        +bool? IntegrationRequired
        +DateTime? IntegrationDate
        +DateTime? CreatedOn
        +DateTime? ModifiedOn
    }
    
    class SimplyContext {
        +DbSet~SimplyCustomerEntity~ SimplyCustomerEntity
        +OnModelCreating(modelBuilder) void
    }
    
    class FromSimplyEventProcessor {
        -AppSettings _appSettings
        -ILogger~FromSimplyEventProcessor~ _logger
        -IPatientHandlerService _patientHandler
        +FromSimplyTimerEventAsync(myTimer) Task
        +MasterDataPostAsync() Task
        +TransactionPost() void
    }
    
    class AppSettings {
        +string AppName
        +string SqlConnectionString
        +string EventGridEndpoint
        +string EventGridAccessKey
        +string StorageQueueConnectionString
        +string FromStorageQueueName
        +string ToStorageQueueName
        +string ExternalSystemCode
    }
    
    class Program {
        <<function>>
        +Main() void
    }
    
    IMasterDataEntityService <|.. IPatientHandlerService
    IPatientHandlerService <|.. PatientHandler
    BaseEntityService <|-- PatientHandler
    ISimplyCustomerRepository <|.. SimplyCustomerRepository
    BaseRepositoryService <|-- SimplyCustomerRepository
    SimplyContext --o SimplyCustomerEntity : contains
    PatientHandler --> ISimplyCustomerRepository : uses
    SimplyCustomerRepository --> SimplyContext : uses
    FromSimplyEventProcessor --> IPatientHandlerService : uses
    Program --> FromSimplyEventProcessor : configures
```

This diagram shows the core architecture of the Simply Integration project. The key components are:

1. **Entity Services**: Implementations of `IMasterDataEntityService` like `PatientHandler` that process master data entities
2. **Repositories**: Implementations of repository interfaces like `ISimplyCustomerRepository` that provide data access
3. **Database**: The `SimplyContext` provides access to the Simply database entities
4. **Event Processor**: The `FromSimplyEventProcessor` orchestrates data processing from Simply to other systems
5. **Base Classes**: Common functionality is provided through `BaseEntityService` and `BaseRepositoryService`