﻿using Microsoft.Extensions.Options;
using Moq;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.PurchaseOrders;

public class PurchaseOrderTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly PurchaseOrderService _purchaseOrderService;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;

    private readonly IEnumerable<ClinicEntity> _clinics;
    private readonly IEnumerable<VendorEntity> _vendors;
    private readonly IEnumerable<ProductEntity> _products;

    public PurchaseOrderTests()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());

        _serviceFactory = new ServiceFactory();
        _purchaseOrderService = _serviceFactory.CreatePurchaseOrderService();

        _clinics = TestDataLoader.Clinics();
        _vendors = TestDataLoader.Vendors();
        _products = TestDataLoader.Products();
    }

    [Fact]
    public async Task UpsertAsync_InsertShouldReturnPurchaseOrder()
    {
        // Arange
        var purchaseOrder = new PurchaseOrder()
        {
            ExternalSystemCode = _optionsMock.Object.Value.ExternalSystemCode,
            DocumentNumber = "TESTPO1",
            ExternalReference = "EXT-TESTPO1",
            Vendor = new() { Code = _vendors.First().Code },
            Clinic = new() { Code = _clinics.First().Code },
            DocumentDate = DateTime.Now.Date
        };
        purchaseOrder.Lines.Add(new PurchaseOrderLine()
        {
            Sequence = 1,
            ExternalReference = "EXT-TESTPO1-1",
            Product = new() { Code = _products.First().Code },
            Quantity = 1
        });

        // Act
        var result = await _purchaseOrderService.UpsertAsync(purchaseOrder);

        // Assert result exists
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);

        // Assert the PO really exists in the db
        var getResult = await _purchaseOrderService.GetAsync(
            externalSystemCode: _optionsMock.Object.Value.ExternalSystemCode,
            documentNumber: "TESTPO1");

        Assert.NotNull(getResult);
        Assert.Equal(result.Id, getResult.Id);

        // Assert event was raised
        _serviceFactory.EventGridPublisherMock.Verify(p => p.RaiseEventAsync(
            It.IsAny<AppSettings>(),
            It.Is<string>(s => s == EntityType.PurchaseOrder.GetEventName()),
            It.IsAny<string>(),
            It.IsAny<PurchaseOrder>()),
            Times.Once);
    }
}

