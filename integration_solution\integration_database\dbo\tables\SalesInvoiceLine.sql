﻿CREATE TABLE [dbo].[SalesInvoiceLine]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_SalesInvoiceLine_Id] DEFAULT NEWID() NOT NULL,
    [SalesInvoiceId]        UNIQUEIDENTIFIER NOT NULL,
    [Sequence]              INT NOT NULL,
    [SalesOrderLineId]      UNIQUEIDENTIFIER NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [Description]           NVARCHAR(100) NULL,
    [Quantity]              MONEY NULL,
    [SerialNumber]          NVARCHAR(30) NULL,
    [UnitPrice]             MONEY NULL,
    [GrossAmount]           MONEY NULL,
    [DiscountAmount]        MONEY NULL,
    [AmountExclTax]         MONEY NULL,
    [TaxAmount]             MONEY NULL,
    [AmountInclTax]         MONEY NULL,
    [IsActive]              BIT CONSTRAINT [DF_SalesInvoiceLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_SalesInvoiceLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_SalesInvoiceLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT              [PK_SalesInvoiceLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_SalesInvoiceLine_SalesInvoice] FOREIGN KEY (SalesInvoiceId) REFERENCES [dbo].[SalesInvoice](Id),
    CONSTRAINT              [FK_SalesInvoiceLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesInvoiceLine_SalesInvoiceId_LineNo
ON [dbo].[SalesInvoiceLine] ([SalesInvoiceId], [Sequence])
GO