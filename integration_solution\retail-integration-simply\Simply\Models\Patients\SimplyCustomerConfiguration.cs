﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.Patients;

public class SimplyCustomerConfiguration : IEntityTypeConfiguration<SimplyCustomerEntity>
{
    public void Configure(EntityTypeBuilder<SimplyCustomerEntity> builder)
    {
        builder.ToTable("Customer");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.CustomerNumber)
            .HasColumnName("CustomerNumber")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(100)")
            .IsRequired(true);

        builder.Property(x => x.Address)
            .HasColumnName("Address")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.AddressLine2)
            .HasColumnName("AddressLine2")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.City)
            .HasColumnName("City")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.StateRegion)
            .HasColumnName("StateRegion")
            .HasColumnType("nvarchar(20)")
            .IsRequired(false);

        builder.Property(x => x.Phone)
            .HasColumnName("Phone")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.Email)
            .HasColumnName("Email")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.Phone)
            .HasColumnName("Phone")
            .HasColumnType("nvarchar(30)")
            .IsRequired(false);

        builder.Property(x => x.ContactName)
            .HasColumnName("ContactName")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.ClinicCode)
            .HasColumnName("ClinicCode")
            .HasColumnType("nvarchar(20)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true);

        builder.Property(x => x.IntegrationDate)
            .HasColumnName("IntegrationDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();
    }
}