# Master Data Operation Sequence Diagram

This diagram illustrates the sequence of operations for a master data entity (like Clinic, Patient, etc.) in the WSA Retail Integration Model project.

```mermaid
sequenceDiagram
    participant Client
    participant EntityService as Entity Service
    participant Repository as Entity Repository
    participant DB as Integration Context
    participant EventGrid as Event Grid Publisher
    
    %% Get operation
    Client->>EntityService: GetAsync(externalSystemCode, id, code, externalCode)
    EntityService->>Repository: GetAsync(id) or GetAsync(code)
    Repository->>DB: Query Entity
    DB-->>Repository: Return Entity
    Repository-->>EntityService: Return Entity
    EntityService-->>Client: Return Mapped Model
    
    %% Upsert operation
    Client->>EntityService: UpsertAsync(dto)
    EntityService->>Repository: GetAsync(code) or GetAsync(id)
    Repository->>DB: Query Entity
    DB-->>Repository: Return Entity (or null)
    
    alt Entity exists
        EntityService->>EntityService: Map DTO to existing Entity
    else Entity doesn't exist
        EntityService->>EntityService: Create new Entity from DTO
    end
    
    EntityService->>Repository: UpsertAsync(entity)
    Repository->>DB: SaveChanges()
    DB-->>Repository: Return Updated Entity
    Repository-->>EntityService: Return Updated Entity
    
    EntityService->>EventGrid: RaiseEventAsync(settings, "EntityUpdated", subject, entity)
    EventGrid-->>EntityService: Event Published
    
    EntityService-->>Client: Return Mapped Model
```

This sequence diagram shows the flow of operations for a master data entity (like Clinic, Patient, etc.):

1. **Get Operation**: The client requests an entity using the EntityService's GetAsync method. The service delegates to the repository, which queries the database through the DbContext. The entity is then mapped to a model and returned to the client.

2. **Upsert Operation**: The client sends a DTO to create or update an entity. The service checks if the entity exists, then either updates the existing entity or creates a new one. The updated entity is saved to the database, an event is published to Event Grid, and the mapped model is returned to the client.