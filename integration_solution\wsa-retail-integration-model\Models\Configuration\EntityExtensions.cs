﻿namespace WSA.Retail.Integration.Models.Configuration;

public static class EntityExtensions
{
    public static string GetEntityCode(this EntityType entityType)
    {
        return entityType switch
        {
            EntityType.Adjustment => "ADJUSTMENT",
            EntityType.Battery => "BATTERY",
            EntityType.Category => "CATEGORY",
            EntityType.Claim => "CLAIM",
            EntityType.Clinic => "CLINIC",
            EntityType.Color => "COLOR",
            EntityType.Company => "COMPANY",
            EntityType.Manufacturer => "MANUFACTURER",
            EntityType.Model => "MODEL",
            EntityType.Patient => "PATIENT",
            EntityType.Payment => "PAYMENT",
            EntityType.PaymentMethod => "PAYMENTMETHOD",
            EntityType.Payor => "PAYOR",
            EntityType.Product => "PRODUCT",
            EntityType.ProductModel => "PRODUCTMODEL",
            EntityType.PurchaseOrder => "PURCHASEORDER",
            EntityType.PurchaseOrderLine => "PURCHASEORDERLINE",
            EntityType.PurchaseReceipt => "PURCHASERECEIPT",
            EntityType.PurchaseReceiptLine => "PURCHASERECEIPTLINE",
            EntityType.PurchaseReturn => "PURCHASERETURN",
            EntityType.PurchaseReturnLine => "PURCHASERETURNLINE",
            EntityType.PurchaseShipment => "PURCHASESHIP",
            EntityType.PurchaseShipmentLine => "PURCHASESHIPLINE",
            EntityType.SalesCredit => "SALESCREDIT",
            EntityType.SalesCreditLine => "SALESCREDITLINE",
            EntityType.SalesInvoice => "SALESINVOICE",
            EntityType.SalesInvoiceLine => "SALESINVOICELINE",
            EntityType.SalesOrder => "SALESORDER",
            EntityType.SalesOrderLine => "SALESORDERLINE",
            EntityType.Subcategory => "SUBCATEGORY",
            EntityType.TaxGroup => "TAXGROUP",
            EntityType.Vendor => "VENDOR",
            _ => throw new ArgumentOutOfRangeException(nameof(entityType), entityType, null)
        };
    }

    public static string GetEventName(this EntityType entityType)
    {
        return entityType switch
        {
            EntityType.Adjustment => "adjustments",
            EntityType.Battery => "batteries",
            EntityType.Category => "categories",
            EntityType.Claim => "claims",
            EntityType.Clinic => "clinics",
            EntityType.Color => "colors",
            EntityType.Company => "companies",
            EntityType.Manufacturer => "manufacturers",
            EntityType.Model => "models",
            EntityType.Patient => "patients",
            EntityType.Payment => "payments",
            EntityType.PaymentMethod => "paymentmethods",
            EntityType.Payor => "payors",
            EntityType.Product => "products",
            EntityType.ProductModel => "productmodels",
            EntityType.PurchaseOrder => "purchaseorders",
            EntityType.PurchaseReceipt => "purchasereceipts",
            EntityType.PurchaseReturn => "purchasereturns",
            EntityType.PurchaseShipment => "purchaseshipments",
            EntityType.SalesCredit => "salescredits",
            EntityType.SalesInvoice => "salesinvoices",
            EntityType.SalesOrder => "salesorders",
            EntityType.Subcategory => "subcategories",
            EntityType.TaxGroup => "taxgroups",
            EntityType.Vendor => "vendors",
            _ => throw new ArgumentOutOfRangeException(nameof(entityType), entityType, null)
        };
    }
}
