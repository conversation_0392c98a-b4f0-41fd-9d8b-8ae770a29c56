﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;

namespace WSA.Retail.Integration.Manage.Models.Batteries;

class BatteryEventHandler(
    BatteryGetByQueryHandler queryHandler)
{
    protected readonly BatteryGetByQueryHandler _queryHandler = queryHandler;


    [Function("BatteryGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "batteries")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}
