﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Runtime.CompilerServices;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Products;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductToEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ProductToEventHandler> logger,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    ProductManageIntegrator manageIntegrator)
    : GenericToEventHandler<
        Product,
        IEntitySubscriberService,
        ProductManageIntegrator,
        ProductResponse>(
            appSettings,
            entitySubscriberService,
            couplingService,
            manageIntegrator,
            EntityType.Product)
{
    private readonly ILogger<ProductToEventHandler> _logger = logger;

    protected override void LogMethodStart([CallerMemberName] string? callingMethod = null)
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(ex);
    }
}