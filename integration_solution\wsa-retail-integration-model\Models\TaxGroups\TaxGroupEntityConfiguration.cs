using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.TaxGroups
{
    public class TaxGroupEntityConfiguration : IEntityTypeConfiguration<TaxGroupEntity>
    {
        public void Configure(EntityTypeBuilder<TaxGroupEntity> builder)
        {
            builder.ToTable("TaxGroup", "dbo");

            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(c => c.Rate)
                .HasColumnName("Rate")
                .HasColumnType("money");
        }
    }
}