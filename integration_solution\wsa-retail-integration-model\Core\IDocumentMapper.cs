﻿using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Core;

public interface IDocumentMapper<TModel, TLineModel, TEntity, TLineEntity>
    where TModel : IDocument<TLineModel>
    where TLineModel : IDocumentLine
    where TEntity : IDocumentEntity<TLineEntity>
    where TLineEntity : IDocumentLineEntity
{
    TEntity ToEntity(TModel model);

    ExternalDocumentReference ToExternalDocumentReference(TModel domainModel);
}
