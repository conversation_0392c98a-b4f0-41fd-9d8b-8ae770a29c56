﻿using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Manage.Tests.Configuration;
using WSA.Retail.Integration.Manage.Tests.Data;
using WSA.Retail.Integration.Manage.Data;

namespace WSA.Retail.Integration.Tests.Utilities;

public class ServiceFactory
{
    private readonly string _dbName;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<IDbContextFactory<ManageDbContext>> _dbContextFactoryMock;
    private readonly Mock<IEventGridPublisher> _eventGridPublisherMock;

    public string DbName => _dbName;
    public AppSettings AppSettings => _optionsMock.Object.Value;
    public IDbContextFactory<ManageDbContext> ManageDbContextFactory => _dbContextFactoryMock.Object;
    public Mock<IEventGridPublisher> EventGridPublisherMock => _eventGridPublisherMock;

    public ServiceFactory()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());
        _eventGridPublisherMock = new Mock<IEventGridPublisher>();

        _dbName = $"TestDb_{Guid.NewGuid()}";
        _dbContextFactoryMock = new Mock<IDbContextFactory<ManageDbContext>>();
        _dbContextFactoryMock
            .Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(() => TestDbContextHelper.GetInMemoryDbContext(_dbName));
    }
}
