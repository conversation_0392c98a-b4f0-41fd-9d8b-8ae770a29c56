using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.SalesInvoices
{
    public class SalesInvoiceConfiguration : IEntityTypeConfiguration<SalesInvoiceEntity>
    {
        public void Configure(EntityTypeBuilder<SalesInvoiceEntity> builder)
        {
            builder.ToTable("SalesInvoice", "dbo");

            builder.<PERSON><PERSON><PERSON>(b => b.Id);

            builder.ConfigureSalesDocumentFields<SalesInvoiceEntity, SalesInvoiceLineEntity>();
        }
    }
}