using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Products
{
    public class ProductEntityConfiguration : IEntityTypeConfiguration<ProductEntity>
    {
        public void Configure(EntityTypeBuilder<ProductEntity> builder)
        {
            builder.ToTable("Product", "dbo");

            builder.<PERSON><PERSON>ey(p => p.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(p => p.PimProductId)
                .HasColumnName("PimProductId")
                .HasColumnType("nvarchar(20)")
                .HasMaxLength(20);

            builder.Property(p => p.CategoryId)
                .HasColumnName("ProductCategoryId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.VendorId)
                .HasColumnName("VendorId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.ManufacturerId)
                .HasColumnName("ManufacturerId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.ColorId)
                .HasColumnName("ColorId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.TaxGroupId)
                .HasColumnName("TaxGroupId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.ProductModelId)
                .HasColumnName("ProductModelId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.BatteryId)
                .HasColumnName("BatteryId")
                .HasColumnType("uniqueidentifier");

            builder.Property(p => p.VendorItemNo)
                .HasColumnName("VendorItemNo")
                .HasColumnType("nvarchar(20)")
                .HasMaxLength(20);

            builder.Property(p => p.GTIN)
                .HasColumnName("GTIN")
                .HasColumnType("nvarchar(20)")
                .HasMaxLength(20);

            builder.Property(p => p.ImageUrl)
                .HasColumnName("ImageUrl")
                .HasColumnType("nvarchar(255)")
                .HasMaxLength(255);

            builder.Property(p => p.IsSerialized)
                .HasColumnName("IsSerialized")
                .HasColumnType("bit");

            builder.Property(p => p.IsInventory)
                .HasColumnName("IsInventory")
                .HasColumnType("bit");

            builder.Property(p => p.UnitCost)
                .HasColumnName("UnitCost")
                .HasColumnType("decimal(18, 4)");

            builder.HasOne(p => p.CategoryEntity)
                .WithMany()
                .HasForeignKey(p => p.CategoryId);

            builder.HasOne(p => p.VendorEntity)
                .WithMany()
                .HasForeignKey(p => p.VendorId);

            builder.HasOne(p => p.ManufacturerEntity)
                .WithMany()
                .HasForeignKey(p => p.ManufacturerId);

            builder.HasOne(p => p.ColorEntity)
                .WithMany()
                .HasForeignKey(p => p.ColorId);

            builder.HasOne(p => p.TaxGroupEntity)
                .WithMany()
                .HasForeignKey(p => p.TaxGroupId);

            builder.HasOne(p => p.ProductModelEntity)
                .WithMany(m => m.Products)
                .HasForeignKey(p => p.ProductModelId);

            builder.HasOne(p => p.BatteryEntity)
                .WithMany()
                .HasForeignKey(p => p.BatteryId);
        }
    }
}