using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReturns;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public static class SimplyPurchaseOrderExtensions
{
    public static PurchaseOrder ToPurchaseOrder(this SimplyPOHeaderEntity entity)
    {
        var purchaseOrder = new PurchaseOrder
        {
            ExternalReference = entity.PONumber.ToString(),
            DocumentDate = entity.PODate
        };
        
        // Add vendor reference if available
        if (!string.IsNullOrEmpty(entity.POSCode))
        {
            purchaseOrder.Vendor = new ExternalReference
            {
                ExternalCode = entity.POSCode,
                Code = entity.POSCode
            };
        }
        
        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.ClinicCode))
        {
            purchaseOrder.Clinic = new ExternalReference
            {
                Code = entity.ClinicCode
            };
        }
        
        return purchaseOrder;
    }
    
    public static PurchaseOrderLine ToPurchaseLine(this SimplyPODetailsEntity entity)
    {
        var purchaseOrderLine = new PurchaseOrderLine
        {
            Sequence = entity.LineNumber,
            Quantity = entity.Quantity,
            ExternalReference = entity.LineNumber.ToString()
        };
        
        // Add product reference if available
        if (!string.IsNullOrEmpty(entity.ProductCode))
        {
            purchaseOrderLine.Product = new ExternalReference
            {
                Code = entity.ProductCode,
                ExternalCode = entity.ProductCode
            };
        }
        
        return purchaseOrderLine;
    }

    public static PurchaseReturn ToPurchaseReturn(this SimplyPOHeaderEntity entity)
    {
        var purchaseReturn = new PurchaseReturn
        {
            ExternalReference = entity.PONumber.ToString(),
            DocumentDate = entity.PODate
        };

        // Add vendor reference if available
        if (!string.IsNullOrEmpty(entity.POSCode))
        {
            purchaseReturn.Vendor = new ExternalReference
            {
                ExternalCode = entity.POSCode
            };
        }

        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.ClinicCode))
        {
            purchaseReturn.Clinic = new ExternalReference
            {
                Code = entity.ClinicCode
            };
        }

        return purchaseReturn;
    }

    public static PurchaseReturnLine ToPurchaseReturnLine(this SimplyPODetailsEntity entity)
    {
        var purchaseReturnLine = new PurchaseReturnLine
        {
            Sequence = entity.LineNumber,
            Quantity = -entity.Quantity,
            ExternalReference = entity.LineNumber.ToString()
        };

        // Add product reference if available
        if (!string.IsNullOrEmpty(entity.ProductCode))
        {
            purchaseReturnLine.Product = new ExternalReference
            {
                Code = entity.ProductCode,
                ExternalCode = entity.ProductCode
            };
        }

        return purchaseReturnLine;
    }
}