using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Batteries
{
    public class BatteryEntityConfiguration : IEntityTypeConfiguration<BatteryEntity>
    {
        public void Configure(EntityTypeBuilder<BatteryEntity> builder)
        {
            builder.ToTable("Battery", "dbo");

            builder.<PERSON><PERSON><PERSON>(b => b.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();
        }
    }
}
