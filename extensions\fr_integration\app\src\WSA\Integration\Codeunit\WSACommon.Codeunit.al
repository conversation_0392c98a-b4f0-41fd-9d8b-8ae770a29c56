namespace WSA.Integration;

using Microsoft.Inventory.Location;
using Microsoft.Inventory.Tracking;
using Microsoft.Inventory.Item.Catalog;
using Microsoft.Inventory.Ledger;
using Microsoft.Purchases.Vendor;
using Microsoft.Purchases.Document;
using Microsoft.Purchases.History;
using Microsoft.Inventory.Item;
using Microsoft.Finance.GeneralLedger.Setup;
using Microsoft.Finance.VAT.Setup;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Document;
using Microsoft.Sales.History;
using Microsoft.Sales.Receivables;


codeunit 50110 "WSA Common"
{
    procedure GetJsonFromBlob(var Request: Record "WSA Integration Request Log") Json: JsonObject
    var
        stream: InStream;
        requestBody: Text;

    begin
        Request.CalcFields("Request Content");
        if not Request."Request Content".HasValue() then
            exit;

        Request."Request Content".CreateInStream(stream);
        stream.Read(requestBody);
        Json.ReadFrom(requestBody);
    end;


    procedure GetJsonValue(Json: JsonObject; Path: Text[250]) Value: JsonValue
    var
        jToken: JsonToken;

    begin
        if Json.SelectToken(Path, jToken) then
            exit(jToken.AsValue);
    end;


    procedure ValidateFieldFromJson(
        Json: JsonObject;
        Path: Text[250];
        var RecRef: RecordRef;
        FieldNo: Integer;
        var TempFieldSet: Record 2000000041 temporary)

    var
        value: JsonValue;
        fldRef: FieldRef;

    begin
        value := GetJsonValue(Json, Path);
        if value.IsNull() then
            exit;

        fldRef := RecRef.Field(FieldNo);
        case fldRef.Type of
            fldRef.Type::Text:
                begin
                    if (Format(fldRef.Value) <> value.AsText()) then
                        fldRef.Validate(CopyStr(value.AsText(), 1, fldRef.Length));
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
            fldRef.Type::Code:
                begin
                    if (Format(fldRef.Value) <> value.AsCode()) then
                        fldRef.Validate(CopyStr(value.AsCode(), 1, fldRef.Length));
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
            fldRef.Type::Boolean:
                begin
                    fldRef.Validate(value.AsBoolean());
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
            fldRef.Type::Integer:
                begin
                    if (Format(fldRef.Value) <> Format(value.AsInteger())) then
                        fldRef.Validate(value.AsInteger());
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
            fldRef.Type::Decimal:
                begin
                    if (Format(fldRef.Value) <> Format(value.AsDecimal())) then
                        fldRef.Validate(value.AsDecimal());
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
            fldRef.Type::Date:
                begin
                    if (Format(fldRef.Value) <> Format(value.AsDate())) then
                        fldRef.Validate(value.AsDate());
                    RegisterFieldSet(RecRef.Number, FieldNo, TempFieldSet);
                end;
        end;

    end;


    procedure RegisterFieldSet(
        TableNo: Integer;
        FieldNo: Integer;
        var TempFieldSet: Record 2000000041 temporary)

    begin
        if TempFieldSet.Get(TableNo, FieldNo) then
            exit;

        TempFieldSet.Init();
        TempFieldSet.TableNo := TableNo;
        TempFieldSet.Validate("No.", FieldNo);
        TempFieldSet.Insert(true);
    end;


    procedure SetCreatedResponse(
        var Request: Record "WSA Integration Request Log";
        JsonResponse: JsonObject)

    var
        responseBody: Text;
        responseStream: OutStream;

    begin
        JsonResponse.WriteTo(responseBody);
        Request."Response Content".CreateOutStream(responseStream);
        responseStream.WriteText(responseBody);

        Request.Status := Request.Status::created;
        Request.Modify();
        Commit();
    end;


    procedure SetOkResponse(
        var Request: Record "WSA Integration Request Log";
        JsonResponse: JsonObject)

    var
        responseBody: Text;
        responseStream: OutStream;

    begin
        JsonResponse.WriteTo(responseBody);
        Request."Response Content".CreateOutStream(responseStream);
        responseStream.WriteText(responseBody);

        Request.Status := Request.Status::ok;
        Request.Modify();
        Commit();
    end;


    procedure SetNoContentResponse(
        var Request: Record "WSA Integration Request Log")

    begin
        Request.Status := Request.Status::noContent;
        Request.Modify();
        Commit();
    end;


    procedure SetErrorResponse(
        var Request: Record "WSA Integration Request Log";
        Error: Text)

    var
        jObject: JsonObject;
        responseBody: Text;
        responseStream: OutStream;
        customDimensions: Dictionary of [Text, Text];

    begin
        if Error = '' then
            Error := GetLastErrorText();

        jObject.Add('error', Error);

        jObject.WriteTo(responseBody);
        Request."Response Content".CreateOutStream(responseStream);
        responseStream.Write(responseBody);

        Request.Status := Request.Status::badRequest;
        Request.Modify();

        Commit();
    end;


    procedure ClinicToJson(ResponsibilityCenter: Record "Responsibility Center") Json: JsonObject
    begin
        Json.Add('code', ResponsibilityCenter.Code);
        Json.Add('externalCode', Format(ResponsibilityCenter.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', ResponsibilityCenter.Name);
        Json.Add('address', ResponsibilityCenter.Address);
        Json.Add('address2', ResponsibilityCenter."Address 2");
        Json.Add('city', ResponsibilityCenter.City);
        Json.Add('region', ResponsibilityCenter.County);
        Json.Add('postalCode', ResponsibilityCenter."Post Code");
        Json.Add('country', ResponsibilityCenter."Country/Region Code");
        Json.Add('phone', ResponsibilityCenter."Phone No.");
        exit(Json);
    end;


    procedure PatientToJson(Customer: Record Customer) Json: JsonObject
    begin
        Json.Add('code', Customer."No.");
        Json.Add('externalCode', Format(Customer.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Customer.Name);
        Json.Add('address', Customer.Address);
        Json.Add('address2', Customer."Address 2");
        Json.Add('city', Customer.City);
        Json.Add('region', Customer.County);
        Json.Add('postalCode', Customer."Post Code");
        Json.Add('country', Customer."Country/Region Code");
        Json.Add('phone', Customer."Phone No.");
        Json.Add('email', Customer."E-Mail");
        Json.Add('identificationNumber', Customer."WSA Patient Id No.");
        exit(Json);
    end;


    procedure PayorToJson(Customer: Record Customer) Json: JsonObject
    begin
        Json.Add('code', Customer."No.");
        Json.Add('externalCode', Format(Customer.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Customer.Name);
        Json.Add('address', Customer.Address);
        Json.Add('address2', Customer."Address 2");
        Json.Add('city', Customer.City);
        Json.Add('region', Customer.County);
        Json.Add('postalCode', Customer."Post Code");
        Json.Add('country', Customer."Country/Region Code");
        Json.Add('phone', Customer."Phone No.");
        Json.Add('email', Customer."E-Mail");
        Json.Add('accountNo', Customer."Our Account No.");
        exit(Json);
    end;


    procedure VendorToJson(Vendor: Record Vendor) Json: JsonObject
    begin
        Json.Add('code', Vendor."No.");
        Json.Add('externalCode', Format(Vendor.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Vendor.Name);
        Json.Add('address', Vendor.Address);
        Json.Add('address2', Vendor."Address 2");
        Json.Add('city', Vendor.City);
        Json.Add('region', Vendor.County);
        Json.Add('postalCode', Vendor."Post Code");
        Json.Add('country', Vendor."Country/Region Code");
        Json.Add('phone', Vendor."Phone No.");
        Json.Add('email', Vendor."E-Mail");
        Json.Add('accountNo', Vendor."Our Account No.");
        Json.Add('integrateWithPos', Vendor."Integrate with POS");
        exit(Json);
    end;


    procedure ManufacturerToJson(Manufacturer: Record Manufacturer) Json: JsonObject
    begin
        Json.Add('code', Manufacturer.Code);
        Json.Add('externalCode', Format(Manufacturer.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Manufacturer.Name);
        Json.Add('address', Manufacturer.Address);
        Json.Add('address2', Manufacturer."Address 2");
        Json.Add('city', Manufacturer.City);
        Json.Add('region', Manufacturer.County);
        Json.Add('postalCode', Manufacturer."Post Code");
        Json.Add('country', Manufacturer."Country/Region Code");
        Json.Add('phone', Manufacturer."Phone No.");
        Json.Add('email', Manufacturer."E-Mail");
        exit(Json);
    end;


    procedure ColorToJson(Color: Record Color) Json: JsonObject
    begin
        Json.Add('code', Color.Code);
        Json.Add('externalCode', Format(Color.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Color.Description);
        Json.Add('hexCode', Color."Hex Code");
        exit(Json);
    end;


    procedure CategoryToJson(Category: Record "Item Category") Json: JsonObject
    begin
        Json.Add('code', Category.Code);
        Json.Add('externalCode', Format(Category.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Category.Description);
        exit(Json);
    end;


    procedure SubcategoryToJson(Category: Record "Item Category") Json: JsonObject
    begin
        Json.Add('code', Category.Code);
        Json.Add('externalCode', Format(Category.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Category.Description);
        Json.Add('parentCategory', Category."Parent Category");
        exit(Json);
    end;


    procedure ProductToJson(Item: Record Item) Json: JsonObject
    var
        itemCategory: Record "Item Category";
        category: Record "Item Category";
        subcategory: Record "Item Category";
        vendor: Record Vendor;
        manufacturer: Record Manufacturer;
        color: Record Color;
        categoryJson: JsonObject;
        subcategoryJson: JsonObject;
        vendorJson: JsonObject;
        manufacturerJson: JsonObject;
        colorJson: JsonObject;

    begin
        Json.Add('code', Item."No.");
        Json.Add('externalCode', Format(Item.SystemId).TrimStart('{').TrimEnd('}'));
        Json.Add('name', Item.Description);
        Json.Add('pimProductId', Item."WSA PIM Product No.");

        if Item."Item Category Code" <> '' then begin
            itemCategory.Get(Item."Item Category Code");
            if itemCategory."Parent Category" <> '' then begin
                category.Get(itemCategory."Parent Category");
                subcategory.Get(itemCategory.Code);
            end else begin
                category.Get(itemCategory.Code);
            end;

            if not IsNullGuid(category.SystemId) then begin
                //categoryJson.Add('id', category."External Code");
                categoryJson.Add('code', category.Code);
                categoryJson.Add('externalCode', Format(category.SystemId).TrimStart('{').TrimEnd('}'));
                categoryJson.Add('name', category.Description);
                Json.Add('category', categoryJson);
            end;

            if not IsNullGuid(subcategory.SystemId) then begin
                //subcategoryJson.Add('id', subcategory."External Code");
                subcategoryJson.Add('code', subcategory.Code);
                subcategoryJson.Add('externalCode', Format(subcategory.SystemId).TrimStart('{').TrimEnd('}'));
                subcategoryJson.Add('name', subcategory.Description);
                Json.Add('subcategory', subcategoryJson);
            end;
        end;

        if Item."Vendor No." <> '' then begin
            Vendor.Get(Item."Vendor No.");
            //vendorJson.Add('id', Vendor."External Code");
            vendorJson.Add('code', Vendor."No.");
            vendorJson.Add('externalCode', Format(Vendor.SystemId).TrimStart('{').TrimEnd('}'));
            vendorJson.Add('name', Vendor.Name);
            Json.Add('vendor', vendorJson);
        end;

        if Item."Manufacturer Code" <> '' then begin
            Manufacturer.Get(Item."Manufacturer Code");
            //manufacturerJson.Add('id', Manufacturer."External Code");
            manufacturerJson.Add('code', Manufacturer.Code);
            manufacturerJson.Add('externalCode', Format(Manufacturer.SystemId).TrimStart('{').TrimEnd('}'));
            manufacturerJson.Add('name', Manufacturer.Name);
            Json.Add('manufacturer', manufacturerJson);
        end;

        if Item."WSA Color" <> '' then begin
            Color.Get(Item."WSA Color");
            //colorJson.Add('id', Color."External Code");
            colorJson.Add('code', Color.Code);
            colorJson.Add('externalCode', Format(Color.SystemId).TrimStart('{').TrimEnd('}'));
            colorJson.Add('name', Color.Description);
            Json.Add('color', colorJson);
        end;

        Json.Add('batteryType', Item."WSA Battery Type");
        Json.Add('vendorItemNo', Item."Vendor Item No.");
        Json.Add('gtin', Item.GTIN);
        Json.Add('imageUrl', Item."WSA Image URI");

        exit(Json);
    end;


    procedure PurchaseOrderToJson(PurchaseOrder: Record "Purchase Header") Json: JsonObject
    var
        purchaseOrderLine: Record "Purchase Line";
        lines: JsonArray;
        line: JsonObject;

    begin
        Json.Add('id', PurchaseOrder."External Code");
        Json.Add('documentNumber', PurchaseOrder."No.");
        Json.Add('externalReference', Format(PurchaseOrder.SystemId).TrimStart('{').TrimEnd('}'));

        if PurchaseOrder."Buy-from Vendor No." <> '' then
            Json.Add('vendor', VendorExternalReferenceJson(PurchaseOrder."Buy-from Vendor No."));

        if PurchaseOrder."Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(PurchaseOrder."Responsibility Center"));

        Json.Add('documentDate', PurchaseOrder."Document Date");

        purchaseOrderLine.SetRange("Document Type", PurchaseOrder."Document Type");
        purchaseOrderLine.SetRange("Document No.", PurchaseOrder."No.");
        purchaseOrderLine.SetFilter("No.", '<>%1', '');
        if purchaseOrderLine.FindSet() then begin
            repeat
                Clear(line);
                line.Add('sequence', purchaseOrderLine."Line No.");
                if (purchaseOrderLine."Type" = purchaseOrderLine."Type"::Item) and (purchaseOrderLine."No." <> '') then
                    line.Add('product', ProductExternalReferenceJson(purchaseOrderLine."No."));
                line.Add('description', purchaseOrderLine.Description);
                line.Add('quantity', purchaseOrderLine."Quantity");
                line.Add('unitPrice', purchaseOrderLine."Direct Unit Cost");
                line.Add('grossAmount', purchaseOrderLine.Quantity * purchaseOrderLine."Direct Unit Cost");
                line.Add('discountAmount', purchaseOrderLine."Line Discount Amount");
                line.Add('amountExclTax', purchaseOrderLine.Amount);
                line.Add('taxAmount', purchaseOrderLine."Amount Including VAT" - purchaseOrderLine.Amount);
                line.Add('amountInclTax', purchaseOrderLine."Amount Including VAT");

                lines.Add(line);
            until purchaseOrderLine.Next() = 0;
            Json.Add('purchaseOrderLines', lines);
        end;

        exit(Json);
    end;


    procedure PurchaseReceiptToJson(PurchRcptHeader: Record "Purch. Rcpt. Header") Json: JsonObject
    var
        purchRcptLine: Record "Purch. Rcpt. Line";
        reservationEntry: Record "Reservation Entry";
        purchaseLine: Record "Purchase Line";
        lines: JsonArray;
        line: JsonObject;

    begin
        Json.Add('documentNumber', PurchRcptHeader."No.");
        Json.Add('externalReference', Format(PurchRcptHeader.SystemId).TrimStart('{').TrimEnd('}'));

        if PurchRcptHeader."Order No." <> '' then
            Json.Add('purchaseOrder', PurchaseOrderExternalReferenceJson(PurchRcptHeader."Order No."));

        if PurchRcptHeader."Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(PurchRcptHeader."Responsibility Center"));

        Json.Add('documentDate', PurchRcptHeader."Document Date");

        purchRcptLine.SetRange("Document No.", PurchRcptHeader."No.");
        purchRcptLine.SetFilter("No.", '<>%1', '');
        if purchRcptLine.FindSet() then begin
            repeat
                Clear(line);
                line.Add('sequence', purchRcptLine."Line No.");
                if (purchRcptLine."Type" = purchRcptLine."Type"::Item) and (purchRcptLine."No." <> '') then
                    line.Add('product', ProductExternalReferenceJson(purchRcptLine."No."));
                line.Add('description', purchRcptLine.Description);
                line.Add('quantity', purchRcptLine."Quantity");

                reservationEntry.SetRange("Item No.", purchRcptLine."No.");
                reservationEntry.SetRange("Location Code", PurchRcptHeader."Location Code");
                reservationEntry.SetRange("Source Type", Database::"Purchase Line");
                reservationEntry.SetRange("Source Subtype", purchaseLine.FieldNo("Document Type"));
                reservationEntry.SetRange("Source Ref. No.", purchRcptLine."Order Line No.");
                if reservationEntry.FindFirst() then
                    line.Add('serialNumber', reservationEntry."Serial No.");

                lines.Add(line);
            until purchRcptLine.Next() = 0;
            Json.Add('purchaseReceiptLines', lines);
        end;

    end;


    procedure SalesOrderToJson(SalesOrder: Record "Sales Header") Json: JsonObject
    var
        salesOrderLine: Record "Sales Line";
        lines: JsonArray;
        line: JsonObject;

    begin
        Json.Add('id', SalesOrder."External Code");
        Json.Add('documentNumber', SalesOrder."No.");
        Json.Add('externalReference', Format(SalesOrder.SystemId).TrimStart('{').TrimEnd('}'));

        if SalesOrder."Sell-to Customer No." <> '' then
            Json.Add('patient', PatientExternalReferenceJson(SalesOrder."Sell-to Customer No."));

        if SalesOrder."Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(SalesOrder."Responsibility Center"));

        Json.Add('documentDate', SalesOrder."Document Date");

        salesOrderLine.SetRange("Document Type", SalesOrder."Document Type");
        salesOrderLine.SetRange("Document No.", SalesOrder."No.");
        salesOrderLine.SetFilter("No.", '<>%1', '');
        if salesOrderLine.FindSet() then begin
            repeat
                Clear(line);
                line.Add('sequence', salesOrderLine."Line No.");
                if (salesOrderLine."Type" = salesOrderLine."Type"::Item) and (salesOrderLine."No." <> '') then
                    line.Add('product', ProductExternalReferenceJson(salesOrderLine."No."));
                line.Add('description', salesOrderLine.Description);
                line.Add('quantity', salesOrderLine."Quantity");
                line.Add('unitPrice', salesOrderLine."Unit Price");
                line.Add('grossAmount', salesOrderLine.Quantity * salesOrderLine."Unit Price");
                line.Add('discountAmount', salesOrderLine."Line Discount Amount");
                line.Add('amountExclTax', salesOrderLine.Amount);
                line.Add('taxAmount', salesOrderLine."Amount Including VAT" - salesOrderLine.Amount);
                line.Add('amountInclTax', salesOrderLine."Amount Including VAT");

                lines.Add(line);
            until salesOrderLine.Next() = 0;
            Json.Add('salesOrderLines', lines);
        end;

        exit(Json);
    end;


    procedure SalesInvoiceToJson(SalesInvoiceHeader: Record "Sales Invoice Header") Json: JsonObject
    var
        salesInvoiceLine: Record "Sales Invoice Line";
        lines: JsonArray;
        line: JsonObject;

    begin
        Json.Add('documentNumber', SalesInvoiceHeader."No.");
        Json.Add('externalReference', Format(SalesInvoiceHeader.SystemId).TrimStart('{').TrimEnd('}'));

        if SalesInvoiceHeader."Sell-to Customer No." <> '' then
            Json.Add('patient', PatientExternalReferenceJson(SalesInvoiceHeader."Sell-to Customer No."));

        if SalesInvoiceHeader."Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(SalesInvoiceHeader."Responsibility Center"));

        Json.Add('documentDate', SalesInvoiceHeader."Document Date");

        salesInvoiceLine.SetRange("Document No.", SalesInvoiceHeader."No.");
        salesInvoiceLine.SetFilter("No.", '<>%1', '');
        if salesInvoiceLine.FindSet() then begin
            repeat
                Clear(line);
                line.Add('sequence', salesInvoiceLine."Line No.");
                if (salesInvoiceLine."Type" = salesInvoiceLine."Type"::Item) and (salesInvoiceLine."No." <> '') then
                    line.Add('product', ProductExternalReferenceJson(salesInvoiceLine."No."));
                line.Add('description', salesInvoiceLine.Description);
                line.Add('quantity', salesInvoiceLine."Quantity");
                line.Add('unitPrice', salesInvoiceLine."Unit Price");
                line.Add('grossAmount', salesInvoiceLine.Quantity * salesInvoiceLine."Unit Price");
                line.Add('discountAmount', salesInvoiceLine."Line Discount Amount");
                line.Add('amountExclTax', salesInvoiceLine.Amount);
                line.Add('taxAmount', salesInvoiceLine."Amount Including VAT" - salesInvoiceLine.Amount);
                line.Add('amountInclTax', salesInvoiceLine."Amount Including VAT");

                lines.Add(line);
            until salesInvoiceLine.Next() = 0;
            Json.Add('salesOrderLines', lines);
        end;

        exit(Json);
    end;


    procedure SalesCreditToJson(SalesCreditHeader: Record "Sales Cr.Memo Header") Json: JsonObject
    var
        salesCreditLine: Record "Sales Cr.Memo Line";
        lines: JsonArray;
        line: JsonObject;

    begin
        Json.Add('documentNumber', SalesCreditHeader."No.");
        Json.Add('externalReference', Format(SalesCreditHeader.SystemId).TrimStart('{').TrimEnd('}'));

        if SalesCreditHeader."Sell-to Customer No." <> '' then
            Json.Add('patient', PatientExternalReferenceJson(SalesCreditHeader."Sell-to Customer No."));

        if SalesCreditHeader."Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(SalesCreditHeader."Responsibility Center"));

        Json.Add('documentDate', SalesCreditHeader."Document Date");

        salesCreditLine.SetRange("Document No.", SalesCreditHeader."No.");
        salesCreditLine.SetFilter("No.", '<>%1', '');
        if salesCreditLine.FindSet() then begin
            repeat
                Clear(line);
                line.Add('sequence', salesCreditLine."Line No.");
                if (salesCreditLine."Type" = salesCreditLine."Type"::Item) and (salesCreditLine."No." <> '') then
                    line.Add('product', ProductExternalReferenceJson(salesCreditLine."No."));
                line.Add('description', salesCreditLine.Description);
                line.Add('quantity', salesCreditLine."Quantity");
                line.Add('unitPrice', salesCreditLine."Unit Price");
                line.Add('grossAmount', salesCreditLine.Quantity * salesCreditLine."Unit Price");
                line.Add('discountAmount', salesCreditLine."Line Discount Amount");
                line.Add('amountExclTax', salesCreditLine.Amount);
                line.Add('taxAmount', salesCreditLine."Amount Including VAT" - salesCreditLine.Amount);
                line.Add('amountInclTax', salesCreditLine."Amount Including VAT");

                lines.Add(line);
            until salesCreditLine.Next() = 0;
            Json.Add('saleCreditLines', lines);
        end;

        exit(Json);
    end;


    procedure PaymentToJson(CustLedgEntry: Record "Cust. Ledger Entry") Json: JsonObject
    begin
        CustLedgEntry.CalcFields(Amount);

        Json.Add('number', CustLedgEntry."Document No.");
        Json.Add('externalReference', Format(CustLedgEntry.SystemId).TrimStart('{').TrimEnd('}'));

        if CustLedgEntry."Sell-to Customer No." <> '' then
            Json.Add('patient', PatientExternalReferenceJson(CustLedgEntry."Sell-to Customer No."));

        if CustLedgEntry."WSA Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(CustLedgEntry."WSA Responsibility Center"));

        Json.Add('paymentDate', CustLedgEntry."Document Date");
        Json.Add('paymentMethod', CustLedgEntry."Payment Method Code");
        Json.Add('amount', -CustLedgEntry."Amount");
    end;


    procedure AdjustmentToJson(ItemLedgEntry: Record "Item Ledger Entry") Json: JsonObject
    begin
        Json.Add('number', ItemLedgEntry."Document No.");
        Json.Add('externalReference', Format(ItemLedgEntry.SystemId).TrimStart('{').TrimEnd('}'));

        Json.Add('product', productExternalReferenceJson(ItemLedgEntry."Item No."));

        if ItemLedgEntry."WSA Responsibility Center" <> '' then
            Json.Add('clinic', ClinicExternalReferenceJson(ItemLedgEntry."WSA Responsibility Center"));

        Json.Add('adjustmentDate', ItemLedgEntry."Document Date");
        Json.Add('quantity', ItemLedgEntry.Quantity);
        Json.Add('serialNumber', ItemLedgEntry."Serial No.");
    end;


    local procedure ClinicExternalReferenceJson(clinicCode: Code[10]) Json: JsonObject
    var
        clinic: Record "Responsibility Center";
        clinicJson: JsonObject;

    begin
        clinic.Get(clinicCode);
        clinicJson.Add('code', clinic.Code);
        clinicJson.Add('externalCode', Format(clinic.SystemId).TrimStart('{').TrimEnd('}'));
        clinicJson.Add('name', clinic.Name);
        exit(clinicJson);
    end;


    local procedure VendorExternalReferenceJson(vendorCode: Code[20]) Json: JsonObject
    var
        vendor: Record Vendor;
        vendorJson: JsonObject;

    begin
        vendor.Get(vendorCode);
        vendorJson.Add('code', vendor."No.");
        vendorJson.Add('externalCode', Format(vendor.SystemId).TrimStart('{').TrimEnd('}'));
        vendorJson.Add('name', vendor.Name);
        exit(vendorJson);
    end;


    local procedure PatientExternalReferenceJson(patientCode: Code[20]) Json: JsonObject
    var
        patient: Record Customer;
        patientJson: JsonObject;

    begin
        patient.Get(patientCode);
        patientJson.Add('code', patient."No.");
        patientJson.Add('externalCode', Format(patient.SystemId).TrimStart('{').TrimEnd('}'));
        patientJson.Add('name', patient.Name);
        exit(patientJson);
    end;


    local procedure ProductExternalReferenceJson(productCode: Code[20]) Json: JsonObject
    var
        product: Record Item;
        productJson: JsonObject;

    begin
        product.Get(productCode);
        productJson.Add('code', product."No.");
        productJson.Add('externalCode', Format(product.SystemId).TrimStart('{').TrimEnd('}'));
        productJson.Add('name', product.Description);
        exit(productJson);
    end;

    local procedure PurchaseOrderExternalReferenceJson(purchaseOrderNo: Code[20]) Json: JsonObject
    var
        purchaseOrder: Record "Purchase Header";
        purchaseOrderJson: JsonObject;

    begin
        purchaseOrder.Get(purchaseOrder."Document Type"::Order, purchaseOrderNo);
        purchaseOrderJson.Add('documentNumber', purchaseOrder."No.");
        purchaseOrderJson.Add('externalReference', Format(purchaseOrder.SystemId).TrimStart('{').TrimEnd('}'));
        exit(purchaseOrderJson);
    end;


    procedure ValidateVATPostingGroup(var salesLine: Record "Sales Line"; taxRate: Decimal): Boolean
    var
        vatPostingSetup: Record "VAT Posting Setup";

    begin
        if vatPostingSetup.Get(salesLine."VAT Bus. Posting Group", salesLine."VAT Prod. Posting Group") then begin
            if Abs(taxRate - vatPostingSetup."VAT %") < 0.1 then
                exit(true);
        end;

        vatPostingSetup.Reset();
        vatPostingSetup.SetRange("VAT Bus. Posting Group", salesLine."VAT Bus. Posting Group");
        vatPostingSetup.SetRange("VAT %", taxRate);
        if vatPostingSetup.FindFirst() then begin
            salesLine.Validate("VAT Prod. Posting Group", vatPostingSetup."VAT Prod. Posting Group");
            exit(true);
        end;

        vatPostingSetup.Reset();
        vatPostingSetup.SetRange("VAT Bus. Posting Group", salesLine."VAT Bus. Posting Group");
        vatPostingSetup.SetFilter("VAT %", '>%1&<2', taxRate - 0.1, taxRate + 0.1);
        if vatPostingSetup.FindFirst() then begin
            salesLine.Validate("VAT Prod. Posting Group", vatPostingSetup."VAT Prod. Posting Group");
            exit(true);
        end;

        exit(false);
    end;


    procedure GetTaxRate(json: JsonObject): Decimal
    var
        GenLedgerSetup: Record "General Ledger Setup";
        amountIncludingTax: Decimal;
        amountExcludingTax: Decimal;
        taxAmount: Decimal;
        taxRate: Decimal;

    begin
        GenLedgerSetup.Get();
        amountIncludingTax := GetJsonValue(Json, '$.amountInclTax').AsDecimal();
        amountExcludingTax := GetJsonValue(Json, '$.amountExclTax').AsDecimal();
        taxAmount := amountIncludingTax - amountExcludingTax;
        taxRate := Round((taxAmount / amountExcludingTax) * 100, 0.0001);
        exit(taxRate);
    end;


    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
}
