﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Attributes;

namespace WSA.Retail.Integration.PIM.Models.Products;

public interface IPimProductAttributeEntity : IIdentifiable, IAuditInfo
{
    public Guid ProductId { get; set; }
    public Guid AttributeId { get; set; }
    public string? Value { get; set; }

    public abstract PimProductEntity? PimProductEntity { get; set; }
    public abstract PimAttributeEntity? PimAttributeEntity { get; set; }
}
