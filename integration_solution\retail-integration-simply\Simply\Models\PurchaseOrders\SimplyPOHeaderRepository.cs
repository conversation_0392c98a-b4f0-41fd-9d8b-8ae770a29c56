using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPOHeaderRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPOHeaderRepository> logger,
    SimplyContext simplyContext)
    : ISimplyPOHeaderRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPOHeaderRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<SimplyPOHeaderEntity>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplyPOHeaderEntity
                .Where(x => x.IntegrationRequired == true)
                .Include(x => x.Details)
                .ToListAsync();

            return list ?? [];
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplyPOHeaderEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}