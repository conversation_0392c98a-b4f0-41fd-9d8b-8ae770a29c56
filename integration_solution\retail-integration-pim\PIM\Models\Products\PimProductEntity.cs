﻿using WSA.Retail.Integration.PIM.Models.Brands;
using WSA.Retail.Integration.PIM.Models.BundleSets;
using WSA.Retail.Integration.PIM.Models.Categories;
using WSA.Retail.Integration.PIM.Models.Colors;
using WSA.Retail.Integration.PIM.Models.Images;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductEntity : IPimProductEntity, WSA.Retail.Integration.PIM.Core.IIdentifiable
    {
        public required Guid Id { get; set; } = Guid.Empty;
        public required string? Name { get; set; }
        public Guid? ColorId { get; set; }
        public bool? IsAvailable { get; set; }
        public bool? IsPhasedOut { get; set; }
        public decimal? ListPrice { get; set; }
        public string? ProductSource { get; set; }
        public string? ProductType { get; set; }
        public int? Ranking { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public string? Sku { get; set; }
        public string? State { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public required bool IntegrationRequired { get; set; } = true;
        public DateTime? IntegratedOn { get; set; }
        public DateTime SystemCreatedOn { get; set; }
        public DateTime SystemModifiedOn { get; set; }

        public virtual PimColorEntity? PimColorEntity { get; set; }
        
        public virtual ICollection<PimProductAttributeEntity> Attributes { get; set; } = [];
        public virtual ICollection<PimImageEntity> Images { get; set; } = [];
        public virtual ICollection<PimProductEntity> ChildProducts { get; set; } = [];
        public virtual ICollection<PimProductEntity> ParentProducts { get; set; } = [];
        public virtual ICollection<PimBrandEntity> Brands { get; set; } = [];
        public virtual ICollection<PimCategoryEntity> Categories { get; set; } = [];
        public virtual ICollection<PimBundleSetEntity> BundleSets { get; set; } = [];
    }
}
