using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.ValueGeneration;

namespace WSA.Retail.Integration.Models.Couplings
{
    public class CouplingConfiguration : IEntityTypeConfiguration<CouplingEntity>
    {
        public void Configure(EntityTypeBuilder<CouplingEntity> builder)
        {
            builder.ToTable("Coupling", "dbo");

            builder.<PERSON><PERSON>ey(c => c.Id);

            builder.Property(c => c.Id)
                .HasColumnName("Id")
                .HasColumnType("uniqueidentifier")
                .HasValueGenerator<GuidValueGenerator>()
                .IsRequired();

            builder.Property(c => c.ExternalSystemId)
                .HasColumnName("ExternalSystemId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(c => c.EntityId)
                .HasColumnName("EntityId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(c => c.RecordId)
                .HasColumnName("RecordId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(c => c.ExternalRecordId)
                .HasColumnName("ExternalRecordId")
                .HasColumnType("nvarchar(100)")
                .IsRequired();

            builder.Property(c => c.CreatedOn)
                .HasColumnName("CreatedOn")
                .HasColumnType("datetime2")
                .ValueGeneratedOnAdd();

            builder.Property(c => c.ModifiedOn)
                .HasColumnName("ModifiedOn")
                .HasColumnType("datetime2")
                .ValueGeneratedOnAdd();

            builder.HasOne(c => c.EntityEntity)
                .WithMany()
                .HasForeignKey(c => c.EntityId);

            builder.HasOne(c => c.ExternalSystemEntity)
                .WithMany()
                .HasForeignKey(c => c.ExternalSystemId);
        }
    }
}