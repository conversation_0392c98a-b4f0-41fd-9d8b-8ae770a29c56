﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.Manufacturers;

public class ManufacturerEventHandler(
    ManufacturerFromEventHandler fromEventHandler,
    ManufacturerGetByQueryHandler queryHandler) : IEventHandler
{
    protected readonly ManufacturerFromEventHandler _fromEventHandler = fromEventHandler;
    protected readonly ManufacturerGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }


    [Function("ManufacturerGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "manufacturers")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}