﻿DECLARE @entity TABLE (
       Code NVARCHAR(20), 
       Name NVARCHAR(100), 
       Prefix NVARCHAR(10), 
       LastNumber INT)

INSERT INTO @entity VALUES
       ('BATTERY', 'Battery', 'BAT', 0),
       ('CATEGORY', 'Category', 'CAT', 0),
       ('CLAIM', 'Claim', 'CLM', 0),
       ('CLINIC', 'Clinic', 'CLI', 0),
       ('COLOR', 'Color', 'COL', 0),
       ('MANUFACTURER', 'Manufacturer', 'MFR', 0),
       ('PATIENT', 'Patient', 'PAT', 0),
       ('PAYMENT', 'Payment', 'PMT', 0),
       ('PAYMENTMETHOD', 'Payment Methods', 'PMETH', 0),
       ('PAYOR', 'Payor', 'PAY', 0),
       ('PRODUCT', 'Product', 'ITEM', 0),
       ('PRODUCTMODEL', 'Product Model', 'PM', 0),
       ('PURCHASEORDER', 'Purchase Order', 'PORD', 0),
       ('PURCHASERECEIPT', 'Purchase Receipt', 'PREC', 0),
       ('PURCHASERETURN', 'Purchase Return Order', 'PRO', 0),
       ('PURCHASESHIP', 'Purchase Shipment', 'PSHIP', 0),
       ('SALESCREDIT', 'Sales Credit', 'SCM', 0),
       ('SALESINVOICE', 'Sales Invoice', 'SINV', 0),
       ('SALESORDER', 'Sales Order', 'SORD', 0),
       ('TAXGROUP', 'Tax Group', 'TG', 0),
       ('VENDOR', 'Vendor', 'VDR', 0)


 MERGE dbo.Entity AS Target
 USING @entity AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target THEN
INSERT (Code,
       [Name],
       Prefix,
       LastNumber)

VALUES (Source.Code,
       Source.[Name],
       Source.Prefix,
       Source.LastNumber)

  WHEN MATCHED AND (
       (Target.Prefix IS NULL)
       ) THEN 
       
 UPDATE 
    SET Prefix = Source.[Prefix],
        LastNumber = Source.LastNumber

;