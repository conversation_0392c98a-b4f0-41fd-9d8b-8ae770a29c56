﻿using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Core;

public interface IClinicAssociated
{
    public ExternalReference? Clinic { get; set; }

    public bool HasChanges(IClinicAssociated? oldEntity)
    {
        if (oldEntity == null) return true;
        if (!Common.AreEqual(Clinic, oldEntity.Clinic)) return true;
        return false;
    }
}
