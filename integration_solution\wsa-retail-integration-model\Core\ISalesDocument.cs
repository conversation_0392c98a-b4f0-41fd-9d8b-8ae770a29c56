﻿namespace WSA.Retail.Integration.Core;

public interface ISalesDocument<TLineModel> :
    IDocument<TLineModel>,
    IPatientAssociated,
    IHasClaims
    where TLineModel : ISalesDocumentLine
{
    public new IEnumerable<string> ValidateRequiredProperties()
    {
        var errors = new List<string>();
        var documentErrors = ((IDocument<TLineModel>)this).ValidateRequiredProperties();

        if (Patient?.Code == null)
            errors.Add("Patient code is required.");
        
        return errors.Concat(documentErrors);
    }
}