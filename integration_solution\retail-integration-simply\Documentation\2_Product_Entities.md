# Product Entities Diagram

This diagram shows the product-related entities and their relationships in the integration database.

```mermaid
erDiagram
    ProductCategory ||--o{ ProductCategory : "parent of"
    ProductCategory ||--o{ Product : "categorizes"
    ProductSubcategory ||--o{ Product : "subcategorizes"
    
    Manufacturer ||--o{ Product : "produces"
    Color ||--o{ Product : "has color"
    TaxGroup ||--o{ Product : "has tax group"
    
    Battery ||--o{ Product : "associated with"
    Model ||--o{ Product : "has model"
    
    Vendor ||--o{ Product : "supplies"
    Vendor ||--o{ PurchaseOrder : "receives"
    
    Product ||--o{ SalesOrderLine : "ordered in"
    Product ||--o{ SalesInvoiceLine : "sold in"
    Product ||--o{ PurchaseOrderLine : "purchased in"
    Product ||--o{ PurchaseReceiptLine : "received in"
    
    ProductCategory {
        UUID Id PK
        string Code
        string Name
        UUID ParentId FK
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    ProductSubcategory {
        UUID Id PK
        string Code
        string Name
        UUID ProductCategoryId FK
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Product {
        UUID Id PK
        string Code
        string Name
        string PimProductId
        UUID ProductCategoryId FK
        UUID ProductSubcategoryId FK
        UUID VendorId FK
        UUID ManufacturerId FK
        UUID ColorId FK
        UUID TaxGroupId FK
        string VendorItemNo
        string GTIN
        string ImageUrl
        bool IsSerialized
        bool IsInventory
        bool IsTaxable
        decimal UnitCost
        UUID ProductModelId FK
        UUID BatteryId FK
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Manufacturer {
        UUID Id PK
        string Code
        string Name
        string Address
        string Address2
        string City
        string Region
        string Country
        string PostalCode
        string Phone
        string Email
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Vendor {
        UUID Id PK
        string Code
        string Name
        string Address
        string Address2
        string City
        string Region
        string Country
        string PostalCode
        string Phone
        string Email
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Color {
        UUID Id PK
        string Code
        string Name
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    TaxGroup {
        UUID Id PK
        string Code
        string Name
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
```