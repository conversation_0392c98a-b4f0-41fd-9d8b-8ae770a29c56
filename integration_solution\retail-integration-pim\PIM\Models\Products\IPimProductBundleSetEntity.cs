﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.BundleSets;

namespace WSA.Retail.Integration.PIM.Models.Products;

public interface IPimProductBundleSetEntity : IIdentifiable, IAuditInfo
{
    public Guid ProductId { get; set; }
    public Guid BundleSetId { get; set; }
    public bool? IsAvailable { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsPhasedOut { get; set; }
    public int? Ranking { get; set; }
    public string? Sku {  get; set; }
    public string? State { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public abstract PimProductEntity ProductEntity { get; set; }
    public abstract PimBundleSetEntity BundleSetEntity { get; set; }
}