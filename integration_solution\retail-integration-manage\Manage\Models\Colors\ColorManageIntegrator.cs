﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Configuration;
using System.Runtime.CompilerServices;


namespace WSA.Retail.Integration.Manage.Models.Colors;

public class ColorManageIntegrator(
    IOptions<AppSettings> appSettings,
    ILogger<ColorManageIntegrator> logger,
    IColorService colorService,
    IEntitySubscriberService subscriberService,
    IManageRequestAdapter<DictionaryRequest, Color> requestAdapter,
    IManageResponseAdapter<DictionaryResponse, Color> responseAdapter,
    ManageAPI manageAPI) :
        GenericManageIntegrator<
            IColorService,
            Color,
            DictionaryRequest,
            DictionaryResponse,
            DictionaryResponseIEnumerableListResponse,
            IEntitySubscriberService>(
                appSettings: appSettings,
                domainService: colorService,
                requestAdapter: requestAdapter,
                responseAdapter: responseAdapter,
                entitySubscriberService: subscriberService,
                manageAPI: manageAPI,
                entityType: EntityType.Color),
        IColorManageIntegrator
{
    private readonly ILogger<ColorManageIntegrator> _logger = logger;

    protected override async Task<DictionaryResponse?> GetEntity(Guid id)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ColorsGET2Async(id);
    }

    protected override async Task<DictionaryResponseIEnumerableListResponse?> GetEntityList()
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ColorsGETAsync();
    }

    protected override async Task<CreatedResponse?> PostEntity(DictionaryRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ColorsPOSTAsync(request);
    }

    protected override async Task PutEntity(Guid id, DictionaryRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        await _manageApi.ColorsPUTAsync(id, request);
    }

    protected override ICollection<DictionaryResponse> GetEntitiesFromEntityList(DictionaryResponseIEnumerableListResponse responseList)
    {
        return responseList.Data;
    }


    protected override void LogMethodStart([CallerMemberName] string? callingMethod = null)
    {
        _logger.LogMethodStart(
            methodName: callingMethod!);
    }
    protected override void LogCustomInformation(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomInformation(
            message: message,
            methodName: callingMethod!);
    }
    protected override void LogCustomWarning(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomWarning(
            message: message,
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex,
            message: message,
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex,
            methodName: callingMethod!);
    }
}