﻿using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Colors;

public class ColorTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly ColorService _colorService;

    public ColorTests()
    {
        _serviceFactory = new ServiceFactory();
        _colorService = _serviceFactory.CreateColorService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnClinic()
    {
        // Act
        var result = await _colorService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "BG");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("5B7D5A14-F626-4BDB-B489-60F80A6965B3"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnClinic()
    {
        // Act
        var result = await _colorService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-BG");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("5B7D5A14-F626-4BDB-B489-60F80A6965B3"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _colorService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "BG");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("5B7D5A14-F626-4BDB-B489-60F80A6965B3"), result.Id);
    }
}

