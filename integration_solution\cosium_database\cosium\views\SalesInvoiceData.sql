﻿CREATE VIEW [cosium].[SalesInvoiceData]
AS
SELECT CONCAT('SINV-', FORMAT(CAST(facture.id AS INT), '00000000')) AS 'DocumentNumber',
       facture.id AS 'ExternalReference',
       facture.numfacture AS 'AlternateNumber',
       CONCAT('PAT-', FORMAT(CAST(client.numeroclient AS INT), '**********')) AS 'Patient_Code',
       client.id AS 'Patient_ExternalCode',
       TRIM(TRIM(ISNULL(client.prenom, '')) + ' ' + TRIM(ISNULL(client.nom, ''))) AS 'Patient_Name', 
       site.codesite AS 'Clinic_Code',
       site.id AS 'Clinic_ExternalCode',
       site.nomsite AS 'Clinic_Name',
       facture.datefacture AS 'DocumentDate',
       articlefacture.id AS 'SalesInvoiceLine_Sequence',
       CONCAT('ITEM-', FORMAT(CAST(produit.id AS INT), '000000')) AS 'SalesInvoiceLine_Product_Code',
       produit.id AS 'SalesInvoiceLine_Product_ExternalCode',
       produit.libelle AS 'SalesInvoiceLine_Product_Name',
       articlefacture.libelle AS 'SalesInvoiceLine_Description',
       CAST(articlefacture.qtearticle AS DECIMAL(18,4)) AS 'SalesInvoiceLine_Quantity',
       ISNULL(articles.numserie, '') AS 'SalesInvoiceLine_SerialNumber',
       IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, CAST(articlefacture.prixunitttc AS DECIMAL(18,4)), CAST(0 AS DECIMAL(18,4))) AS 'SalesInvoiceLine_UnitPrice',
       CAST(articlefacture.qtearticle AS DECIMAL(18,4)) * 
            IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, CAST(articlefacture.prixunitttc AS DECIMAL(18,4)), CAST(0 AS DECIMAL(18,4))) AS 'SalesInvoiceLine_GrossAmount',
       IIF(ISNUMERIC(articlefacture.remise) = 1, CAST(articlefacture.remise AS DECIMAL(18,4)), CAST(0 AS DECIMAL(18,4))) AS 'SalesInvoiceLine_DiscountAmount',
       CAST(CAST(articlefacture.totalarticlettc AS DECIMAL(18,4)) / 
           (1 + (CAST(articlefacture.tauxtva AS DECIMAL(18,4))/100)) AS DECIMAL(18,4)) AS 'SalesInvoiceLine_AmountExclTax',
       CAST(articlefacture.totalarticlettc AS DECIMAL(18,4)) - 
           CAST(CAST(articlefacture.totalarticlettc AS DECIMAL(18,4)) / 
           (1 + (CAST(articlefacture.tauxtva AS DECIMAL(18,4))/100)) AS DECIMAL(18,4)) AS 'SalesInvoiceLine_TaxAmount',
       CAST(articlefacture.totalarticlettc AS DECIMAL(18,4)) AS 'SalesInvoiceLine_AmountInclTax'

  FROM cosium.facture

       LEFT JOIN cosium.client
              ON facture.refclient = client.id

       LEFT JOIN cosium.site
              ON facture.centre = site.id

       LEFT JOIN cosium.articlefacture
              ON articlefacture.reffacture = facture.id

       LEFT JOIN cosium.articles
              ON articlefacture.refarticle = articles.id

       LEFT JOIN cosium.produit
              ON articlefacture.refproduit = produit.id

 WHERE facture.typefacture = 0