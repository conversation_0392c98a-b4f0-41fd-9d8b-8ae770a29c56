codeunit 50109 "WSA Graph Mgt. Events"
{
    SingleInstance = true;


    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Library - Graph Mgt", 'OnAfterInitializeWebRequestWithURL', '', false, false)]
    local procedure OnAfterInitializeWebRequestWithURL(var HttpWebRequestMgt: Codeunit "Http Web Request Mgt.")
    begin
        if WebServicesKey = '' then
            WebServicesKey := IdentityManagement.GetWebServicesKey(UserSecurityId());

        HttpWebRequestMgt.AddBasicAuthentication(UserId(), WebServicesKey);
    end;


    var
        IdentityManagement: Codeunit "Identity Management";
        WebServicesKey: Text[80];
}
