CREATE TABLE [dbo].[ProductModel]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_ProductModel_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_ProductModel_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_ProductModel_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_ProductModel_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductModel_Code
ON [dbo].[ProductModel] ([Code])
GO

CREATE INDEX IX_ProductModel_Name
ON [dbo].[ProductModel] ([Name])
GO