{"name": "Manage-BC Data Transfer Documentation", "description": "Comprehensive documentation of entities and their properties involved in data transfer between Manage and Business Central (BC) systems", "version": "1.0.0", "sources": [{"name": "integration_solution", "path": "../integration_solution", "include": ["**/*.cs", "**/*.md"], "exclude": ["**/bin/**", "**/obj/**", "**/packages/**", "**/.vs/**", "**/node_modules/**"]}], "output": {"format": "markdown", "destination": "./generated"}, "templates": {"entity": {"pattern": "**/*Entity.cs", "template": "entity_template.md"}, "adapter": {"pattern": "**/*Adapter.cs", "template": "adapter_template.md"}, "configuration": {"pattern": "**/*Configuration.cs", "template": "configuration_template.md"}}, "watch": {"enabled": true, "patterns": ["**/*.cs"]}, "metadata": {"author": "ERP Integration Team", "created": "2025-01-15", "tags": ["ERP", "Integration", "Manage", "Business Central", "Data Transfer"]}}