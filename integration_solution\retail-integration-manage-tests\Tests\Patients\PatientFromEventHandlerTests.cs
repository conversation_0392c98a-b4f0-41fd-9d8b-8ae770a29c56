using Moq;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Models.Patients;
using WSA.Retail.Integration.Manage.Tests.Configuration;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;

namespace WSA.Retail.Integration.Tests.Tests.Patients;

public class PatientFromEventHandlerTests
{
    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly PatientFromEventHandler _handler;
    private readonly Mock<IPatientService> _patientServiceMock;
    private readonly Mock<IEntitySubscriberService> _subscriberService;

    public PatientFromEventHandlerTests()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());
        _patientServiceMock = new Mock<IPatientService>();
        _subscriberService = new Mock<IEntitySubscriberService>();

        _handler = new PatientFromEventHandler(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PatientFromEventHandler>>().Object,
            domainModelService: _patientServiceMock.Object,
            entitySubscriberService: _subscriberService.Object,
            couplingService: new Mock<ICouplingService>().Object,
            eventHubEntityAdapter: new Manage.Models.Patients.PatientAdapter());
    }

    private static string LoadJsonFromFile(string entity, string fileName)
    {
        var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, entity, "TestData", fileName);
        if (!File.Exists(path))
        {
            throw new FileNotFoundException($"Test JSON file not found: {path}");
        }
        return File.ReadAllText(path);
    }

    [Fact]
    public async Task HandleFromQueueAsync_ValidMessage_ShouldContainExpectedData()
    {
        // Arrange
        _subscriberService
            .Setup(s => s.GetAsync(
                _optionsMock.Object.Value.ExternalSystemCode,
                EntityType.Patient.GetEntityCode()))
            .ReturnsAsync(new EntitySubscriber
            {
                FromExternalSystem = true
            } );

        _patientServiceMock
            .Setup(s => s.UpsertAsync(It.IsAny<Patient>()))
            .ReturnsAsync((Patient p) =>
            {
                p.Id = Guid.NewGuid();
                return p;
            });

        // Load JSON from file
        var jsonMessage = LoadJsonFromFile("Patients", "ValidPatientMessage.json");
        var eventMessage = JsonSerializer.Deserialize<EventHubMessage>(jsonMessage);

        // Act
        var result = await _handler.HandleFromQueueAsync(eventMessage!);

        // Assert
        Assert.True(result);
        
    }
}