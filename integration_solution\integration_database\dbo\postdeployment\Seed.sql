﻿/*
Post-Deployment Script Template                                                 
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.              
 Use SQLCMD syntax to include a file in the post-deployment script.                     
 Example:      :r .\myfile.sql                                                        
 Use SQLCMD syntax to reference a variable in the post-deployment script.              
 Example:      :setvar TableName MyTable                                                 
               SELECT * FROM [$(TableName)]                                   
--------------------------------------------------------------------------------------
*/

PRINT 'Beginning Post-Deployment Script execution';


:r .\referencedata\Entities.sql
:r .\referencedata\ExternalSystems.sql
:r .\referencedata\EntitySubscribers.sql

:r .\referencedata\Categories.sql