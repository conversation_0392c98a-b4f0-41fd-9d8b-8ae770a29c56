﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.Patients;

public class PatientEventHandler(
    PatientFromEventHandler fromEventHandler,
    PatientGetByQueryHandler queryHandler) : IEventHandler
{
    protected readonly PatientFromEventHandler _fromEventHandler = fromEventHandler;
    protected readonly PatientGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }


    [Function("PatientGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "patients")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}