﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Manage.Models.Countries;

namespace WSA.Retail.Integration.Manage.Data.Repositories;

public class InventoryCountryRepository(
    IOptions<AppSettings> options,
    ILogger<InventoryCountryRepository> logger,
    IDbContextFactory<ManageDbContext> dbContextFactory)
    : IInventoryCountryRepository
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<InventoryCountryRepository> _logger = logger;
    private readonly IDbContextFactory<ManageDbContext> _dbContextFactory = dbContextFactory;

    public async Task<InventoryCountry?> GetByNameAsync(string name)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be null or empty", nameof(name));

        using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var entity = await dbContext.Set<InventoryCountryEntity>()
            .AsNoTracking()
            .FirstOrDefaultAsync(e => e.Name == name);

        if (entity == null)
            return null;

        return MapToModel(entity);
    }

    public async Task<InventoryCountry?> GetByCodeAsync(string code)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Code cannot be null or empty", nameof(code));

        using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        var entity = await dbContext.Set<InventoryCountryEntity>()
            .AsNoTracking()
            .FirstOrDefaultAsync(e => e.Code == code);

        if (entity == null)
            return null;

        return MapToModel(entity);
    }

    private static InventoryCountry MapToModel(InventoryCountryEntity entity)
    {
        return new InventoryCountry
        {
            Id = entity.Id,
            Name = entity.Name,
            Code = entity.Code,
            CreatedOn = entity.CreatedOn,
            ModifiedOn = entity.ModifiedOn
        };
    }
}
