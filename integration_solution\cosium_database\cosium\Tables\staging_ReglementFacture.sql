﻿CREATE TABLE [cosium].[staging_ReglementFacture] (
    [id]                NVARCHAR (50)  NOT NULL,
    [numcheque]         NVARCHAR (50)  NULL,
    [dateecheance]      NVARCHAR (50)  NULL,
    [montant]           NVARCHAR (50)  NULL,
    [dateencaissee]     NVARCHAR (50)  NULL,
    [typereglement]     NVARCHAR (50)  NULL,
    [refremisebanque]   NVARCHAR (50)  NULL,
    [refbanque]         NVARCHAR (50)  NULL,
    [nomemetteur]       NVARCHAR (200) NULL,
    [datecreation]      NVARCHAR (200) NULL,
    [refclient]         NVARCHAR (50)  NULL,
    [centre]            NVARCHAR (50)  NULL,
    [typeemetteur]      NVARCHAR (50)  NULL,
    [refcoordbancaires] NVARCHAR (50)  NULL,
    [refsymetrique]     NVARCHAR (50)  NULL,
    [reffournisseur]    NVARCHAR (50)  NULL,
    [reftiers]          NVARCHAR (50)  NULL,
    [multiclients]      NVARCHAR (50)  NULL,
    [alerterglt]        NVARCHAR (50)  NULL,
    [montantorigine]    NVARCHAR (50)  NULL,
    [numordrevrt]       NVARCHAR (50)  NULL,
    [dateexportcompta]  NVARCHAR (50)  NULL,
    [munom]             NVARCHAR (50)  NULL,
    [csnom]             NVARCHAR (100)  NULL,
    [datemodif]         NVARCHAR (50)  NULL
);
GO