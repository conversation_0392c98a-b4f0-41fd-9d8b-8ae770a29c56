﻿using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Core;

public interface IProductAssociated
{
    public ExternalReference? Product { get; set; }

    public bool HasChanges(IProductAssociated? oldEntity)
    {
        if (oldEntity == null) return true;
        if (!Common.AreEqual(Product, oldEntity.Product)) return true;
        return false;
    }
}