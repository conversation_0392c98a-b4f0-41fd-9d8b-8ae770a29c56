﻿CREATE TABLE [dbo].[PurchaseShipmentLine]
(
    [Id]                        UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseShipmentLine_Id] DEFAULT NEWID() NOT NULL,
    [PurchaseShipmentId]        UNIQUEIDENTIFIER NOT NULL,
    [Sequence]                  INT NOT NULL,
    [PurchaseReturnLineId]      UNIQUEIDENTIFIER NULL,
    [ProductId]                 UNIQUEIDENTIFIER NULL,
    [Quantity]                  MONEY NULL,
    [SerialNumber]              NVARCHAR(30) NULL,
    [IsActive]                  BIT CONSTRAINT [DF_PurchaseShipmentLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]                 DATETIME2 CONSTRAINT [DF_PurchaseShipmentLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]                DATETIME2 CONSTRAINT [DF_PurchaseShipmentLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT                  [PK_PurchaseShipmentLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT                  [FK_PurchaseShipmentLine_PurchaseShipment] FOREIGN KEY (PurchaseShipmentId) REFERENCES [dbo].[PurchaseShipment](Id) ON DELETE CASCADE,
    CONSTRAINT                  [FK_PurchaseShipmentLine_PurchaseReturnLine] FOREIGN KEY (PurchaseReturnLineId) REFERENCES [dbo].[PurchaseReturnLine](Id),
    CONSTRAINT                  [FK_PurchaseShipmentLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseShipmentLine_PurchaseShipmentId_Sequence
ON [dbo].[PurchaseShipmentLine] ([PurchaseShipmentId], [Sequence])
GO