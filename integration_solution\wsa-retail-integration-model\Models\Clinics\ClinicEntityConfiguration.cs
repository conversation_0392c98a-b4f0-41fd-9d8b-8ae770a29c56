﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Clinics;

public class ClinicEntityConfiguration : IEntityTypeConfiguration<ClinicEntity>
{
    public void Configure(EntityTypeBuilder<ClinicEntity> builder)
    {
        builder.ToTable("Clinic");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureMasterDataFields();
        builder.ConfigureAddressFields();
        builder.ConfigureContactFields();
        builder.ConfigureAuditInfoFields();

        builder.Property(x => x.CompanyId)
            .HasColumnName("CompanyId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        builder
            .HasOne(x => x.CompanyEntity)
            .WithMany()
            .HasForeignKey(x => x.CompanyId);
    }
}