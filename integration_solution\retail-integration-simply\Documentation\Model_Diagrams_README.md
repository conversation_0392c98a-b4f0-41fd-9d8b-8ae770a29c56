# WSA Retail Integration Model - Architecture Diagrams

This document provides an overview of the architecture diagrams for the WSA Retail Integration Model project.

## Class Diagrams

1. [Core Architecture Class Diagram](6_Model_Core_Class_Diagram.md) - Shows the core architecture with base classes and interfaces
2. [Entity Class Diagram](7_Model_Entity_Class_Diagram.md) - Shows the entity classes and their relationships
3. [Service Layer Class Diagram](8_Model_Service_Class_Diagram.md) - Shows the service layer with interfaces and implementations
4. [Repository Layer Class Diagram](9_Model_Repository_Class_Diagram.md) - Shows the repository layer with interfaces and implementations
5. [Event System Class Diagram](10_Event_System_Class_Diagram.md) - Shows the event system classes and their relationships

## Sequence Diagrams

1. [Master Data Operation Sequence](11_Master_Data_Operation_Sequence.md) - Shows the flow of operations for master data entities
2. [Transaction Data Operation Sequence](12_Transaction_Data_Operation_Sequence.md) - Shows the flow of operations for transaction entities
3. [Event Publishing Sequence](13_Event_Publishing_Sequence.md) - Shows the flow of operations for publishing events
4. [Dependency Injection Registration Sequence](14_DI_Registration_Sequence.md) - Shows the flow of operations for registering dependencies

## Database Entity Diagrams

1. [Core Entities](1_Core_Entities.md) - Shows the core entities and their relationships
2. [Product Entities](2_Product_Entities.md) - Shows the product-related entities and their relationships
3. [Sales Transaction Entities](3_Sales_Transaction_Entities.md) - Shows the sales transaction entities and their relationships
4. [Purchase Transaction Entities](4_Purchase_Transaction_Entities.md) - Shows the purchase transaction entities and their relationships
5. [Integration Entities](5_Integration_Entities.md) - Shows the integration-specific entities and their relationships

## Architecture Overview

The WSA Retail Integration Model project follows a layered architecture:

1. **Entity Layer**: Represents the data model with Entity Framework Core entities
2. **Repository Layer**: Provides data access through repositories
3. **Service Layer**: Implements business logic and coordinates operations
4. **Event System**: Enables event-driven communication between components

The architecture follows these design principles:

- **Separation of Concerns**: Each layer has a specific responsibility
- **Dependency Injection**: All dependencies are injected, making the system modular and testable
- **Repository Pattern**: Data access is abstracted through repositories
- **Event-Driven Architecture**: Changes are communicated through events
- **Generic Interfaces**: Common operations are defined in generic interfaces

The system handles two main types of entities:

1. **Master Data Entities**: Reference data like Clinics, Patients, Products
2. **Transaction Entities**: Business transactions like Sales Orders, Sales Invoices, Purchase Orders

Both types follow similar patterns but with different specific operations based on their nature.