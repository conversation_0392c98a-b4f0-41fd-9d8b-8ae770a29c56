﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Categories;

namespace WSA.Retail.Integration.PIM.Models.Products;

public interface IPimProductCategoryEntity : IIdentifiable, IAuditInfo
{
    public Guid ProductId { get; set; }
    public Guid CategoryId { get; set; }

    public abstract PimProductEntity ProductEntity { get; set; }
    public abstract PimCategoryEntity CategoryEntity { get; set; }
}