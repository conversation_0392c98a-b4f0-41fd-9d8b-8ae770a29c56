﻿namespace WSA.Retail.Integration.PIM.Models.Products;

public class PimProductUpsertTableType
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? ColorCode { get; set; }
    public bool IsAvailable { get; set; }
    public bool IsPhasedOut { get; set; }
    public decimal ListPrice { get; set; }
    public string? ProductSource { get; set; }
    public string? ProductType { get; set; }
    public int Ranking { get; set; }
    public DateTime ReleaseDate { get; set; }
    public string? Sku { get; set; }
    public string? State { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}