﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;


namespace WSA.Retail.Integration.Models.Payors
{
    public class PayorEntityConfiguration : IEntityTypeConfiguration<PayorEntity>
    {
        public void Configure(EntityTypeBuilder<PayorEntity> builder)
        {
            builder.ToTable("Payor");
            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAddressFields();
            builder.ConfigureContactFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.AccountNo)
                .HasColumnName("AccountNo")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);
        }
    }
}