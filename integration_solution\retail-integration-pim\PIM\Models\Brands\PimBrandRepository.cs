﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.Brands;

public class PimBrandRepository(
    ILogger<PimBrandRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
        PimProductRelatedEntityRepositoryBase<PimBrandUpsertTableType>(
            logger,
            dbContextFactory),
    IPimBrandRepository
{
    protected override string StoredProcedureName => "pim.BrandUpsert";
    protected override string TableTypeName => "pim.BrandUpsertTableType";

    protected override List<PimBrandUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .SelectMany(p => p.Brands.Select(b => new PimBrandUpsertTableType
            {
                ProductId = p.ProductId,
                Code = ""
            })).ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("ProductId", typeof(Guid));
        dataTable.Columns.Add("Code", typeof(string));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimBrandUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["ProductId"] = entity.ProductId;
            dataRow["Code"] = entity.Code;
        }
        return dataRow;
    }
}