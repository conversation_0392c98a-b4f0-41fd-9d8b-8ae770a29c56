﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Manage.Core;

namespace WSA.Retail.Integration.Manage.Models.PurchaseReceipts;

public class OrderLineItemAcceptedEventConfiguration : IEntityTypeConfiguration<OrderLineItemAcceptedEventEntity>
{
    public void Configure(EntityTypeBuilder<OrderLineItemAcceptedEventEntity> builder)
    {
        builder.ToTable("OrderLineItemAcceptedEvent");
        builder.HasKey(x => x.Id);

        builder.ConfigureManageIdField();
        builder.ConfigureManageEventMetadataFields();
        builder.ConfigureManageOrderIdentifersFields();
        builder.ConfigureManageEventProcessingInfoFields();
        builder.ConfigureAuditInfoFields();
        
        // Configure the LineItemId field
        builder.Property(x => x.LineItemId)
            .HasColumnName("LineItemId")
            .HasColumnType("UNIQUEIDENTIFIER")
            .IsRequired(true);
    }
}