﻿using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Queries;

public interface IProductQueryService
{
    Task UpdateProductsFromPIMAsync(string countryCode, string brandCode);

    Task<List<PisProductResponseForAllProductsOutputType>> GetProductsFromPIMAsync(string countryCode, string brandCode);

    Task<GraphQLProductQueryData?> SendQueryAsync(string countryCode, string brandCode, int pageNumber);
}
