# Integration Database Entity Diagrams

This directory contains entity relationship diagrams for the WSA Retail Integration database structure, created using Mermaid diagrams.

## Diagram Files

The entity diagrams are split into multiple files to maintain readability:

1. [Core Entities](1_Core_Entities.md) - Basic organizational entities like Company, Clinic, Patient
2. [Product Entities](2_Product_Entities.md) - Product-related entities and their relationships
3. [Sales Transaction Entities](3_Sales_Transaction_Entities.md) - Sales orders, invoices, and related entities 
4. [Purchase Transaction Entities](4_Purchase_Transaction_Entities.md) - Purchase orders, receipts, and related entities
5. [Integration Entities](5_Integration_Entities.md) - Integration-specific entities for system coupling

## Key Relationships

The database is designed around several core relationships:

- **Companies** own multiple **Clinics**
- **Clinics** serve multiple **Patients**
- **Products** are categorized in **ProductCategories** and **ProductSubcategories**
- **SalesOrders** contain multiple **SalesOrderLines** for specific **Products**
- **SalesInvoices** are generated from **SalesOrders**
- **PurchaseOrders** are placed to **Vendors** for specific **Products**
- **External Systems** integrate with the database through defined **EntitySubscribers**

## Database Schema

The database uses a standard relational model with consistent patterns:
- All entities have a UUID primary key
- Foreign keys maintain referential integrity
- Standard fields like IsActive, CreatedOn, and ModifiedOn are present on all tables
- Business code fields utilize unique indexes for efficient lookups

Refer to individual diagram files for detailed entity properties and relationships.