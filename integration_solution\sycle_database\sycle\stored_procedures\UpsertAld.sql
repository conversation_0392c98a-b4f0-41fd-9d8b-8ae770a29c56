﻿CREATE   PROCEDURE [sycle].[sp_UpsertAld]
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO [sycle].[ald] (
        ald_id, ald_uuid, ald_description, base_price, sales_tax, do_inventory, 
        clinic_id, provider_cost, actual_cost, archive, item_number, warranty_length, 
        last_update, editable, edi_package_id, po_enabled, pag_enabled, 
        IntegrationRequired, CreatedOn, ModifiedOn
    )
    SELECT 
        s.ald_id, s.ald_uuid, s.ald_description, s.base_price, s.sales_tax, s.do_inventory, 
        s.clinic_id, s.provider_cost, s.actual_cost, s.archive, s.item_number, s.warranty_length, 
        s.last_update, s.editable, s.edi_package_id, s.po_enabled, s.pag_enabled, 
        1 AS IntegrationRequired, SYSUTCDATETIME(), SYSUTCDATETIME()
    FROM [sycle].[Ald_staging] s
    WHERE NOT EXISTS (
        SELECT 1 FROM [sycle].[ald] a WHERE s.ald_id = a.ald_id
    );
    UPDATE a
    SET 
        a.ald_uuid = s.ald_uuid,
        a.ald_description = s.ald_description,
        a.base_price = s.base_price,
        a.sales_tax = s.sales_tax,
        a.do_inventory = s.do_inventory,
        a.clinic_id = s.clinic_id,
        a.provider_cost = s.provider_cost,
        a.actual_cost = s.actual_cost,
        a.archive = s.archive,
        a.item_number = s.item_number,
        a.warranty_length = s.warranty_length,
        a.last_update = s.last_update,
        a.editable = s.editable,
        a.edi_package_id = s.edi_package_id,
        a.po_enabled = s.po_enabled,
        a.pag_enabled = s.pag_enabled,
        a.ModifiedOn = SYSUTCDATETIME(),
        a.IntegrationRequired = 1
    FROM [sycle].[Ald] a
    INNER JOIN [sycle].[Ald_staging] s ON a.ald_id = s.ald_id
    WHERE (
        a.ald_uuid <> s.ald_uuid OR
        a.ald_description <> s.ald_description OR
        a.base_price <> s.base_price OR
        a.sales_tax <> s.sales_tax OR
        a.do_inventory <> s.do_inventory OR
        a.clinic_id <> s.clinic_id OR
        a.provider_cost <> s.provider_cost OR
        a.actual_cost <> s.actual_cost OR
        a.archive <> s.archive OR
        a.item_number <> s.item_number OR
        a.warranty_length <> s.warranty_length OR
        a.last_update < s.last_update OR
        a.editable <> s.editable OR
        ISNULL(a.edi_package_id, '') <> ISNULL(s.edi_package_id, '') OR
        a.po_enabled <> s.po_enabled OR
        a.pag_enabled <> s.pag_enabled
    );

END;
GO