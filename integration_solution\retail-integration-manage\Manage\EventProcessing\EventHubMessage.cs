﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class EventHubMessage
{
    [JsonPropertyName("messageId")] public required Guid MessageId { get; set; }
    [JsonPropertyName("event")] public required string Event { get; set; }
    [JsonPropertyName("version")] public string? Version { get; set; }
    [JsonPropertyName("domain")] public required string Domain { get; set; }
    [JsonPropertyName("message")] public required object Message { get; set; }
    [JsonPropertyName("timestamp")] public required DateTime Timestamp { get; set; }
    [JsonPropertyName("headers")] public EventHubHeaders? Headers { get; set; }
    [Json<PERSON>ropertyName("path")] public string? BlobPath { get; set; }
    [JsonPropertyName("archive")] public bool? Archive { get; set; } = true;
}