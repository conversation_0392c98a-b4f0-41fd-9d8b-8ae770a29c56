# Sales Transaction Entities Diagram

This diagram shows the sales transaction entities and their relationships in the integration database.

```mermaid
erDiagram
    SalesOrder ||--o{ SalesOrderLine : "contains"
    SalesOrder ||--o{ SalesInvoice : "invoiced as"
    SalesOrder ||--o{ Payment : "paid with"
    
    SalesInvoice ||--o{ SalesInvoiceLine : "contains"
    SalesInvoice ||--o{ Payment : "paid with"
    
    SalesCredit ||--o{ SalesCreditLine : "contains"
    SalesCredit ||--o{ Payment : "refunded with"
    
    Patient ||--o{ SalesOrder : "places"
    Patient ||--o{ SalesInvoice : "billed on"
    Patient ||--o{ SalesCredit : "credited to"
    
    Clinic ||--o{ SalesOrder : "processes"
    Clinic ||--o{ SalesInvoice : "processes"
    Clinic ||--o{ SalesCredit : "processes"
    
    Product ||--o{ SalesOrderLine : "ordered in"
    Product ||--o{ SalesInvoiceLine : "sold in"
    Product ||--o{ SalesCreditLine : "credited in"
    
    SalesReconciliation ||--o{ ReconciliationPaymentTable : "contains"
    
    SalesOrder {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID PatientId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    SalesOrderLine {
        UUID Id PK
        UUID SalesOrderId FK
        int Sequence
        UUID ProductId FK
        string Description
        decimal Quantity
        string SerialNumber
        decimal UnitPrice
        decimal GrossAmount
        decimal DiscountAmount
        decimal AmountExclTax
        decimal TaxAmount
        decimal AmountInclTax
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    SalesInvoice {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID SalesOrderId FK
        UUID PatientId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    SalesInvoiceLine {
        UUID Id PK
        UUID SalesInvoiceId FK
        int Sequence
        UUID SalesOrderLineId FK
        UUID ProductId FK
        string Description
        decimal Quantity
        string SerialNumber
        decimal UnitPrice
        decimal GrossAmount
        decimal DiscountAmount
        decimal AmountExclTax
        decimal TaxAmount
        decimal AmountInclTax
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    SalesCredit {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID SalesInvoiceId FK
        UUID PatientId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    SalesCreditLine {
        UUID Id PK
        UUID SalesCreditId FK
        int Sequence
        UUID SalesInvoiceLineId FK
        UUID ProductId FK
        string Description
        decimal Quantity
        string SerialNumber
        decimal UnitPrice
        decimal GrossAmount
        decimal DiscountAmount
        decimal AmountExclTax
        decimal TaxAmount
        decimal AmountInclTax
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Payment {
        UUID Id PK
        UUID SalesOrderId FK
        UUID SalesInvoiceId FK
        UUID SalesCreditId FK
        string DocumentNumber
        UUID PatientId FK
        UUID PayorId FK
        UUID ClinicId FK
        string AppliesTo
        string ReferenceNumber
        string AuthCode
        date DocumentDate
        string PaymentMethod
        decimal Amount
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
```