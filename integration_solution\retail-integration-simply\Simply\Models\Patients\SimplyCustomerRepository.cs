﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Core;
using WSA.Retail.Integration.Logging;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Data;

namespace WSA.Retail.Integration.Simply.Models.Patients;

public class SimplyCustomerRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyCustomerRepository> logger,
    SimplyContext simplyContext)
    : ISimplyCustomerRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyCustomerRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<SimplyCustomerEntity>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplyCustomerEntity
                .Where(x => x.IntegrationRequired == true).ToListAsync();

            return list ?? [];

        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);

        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplyCustomerEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}