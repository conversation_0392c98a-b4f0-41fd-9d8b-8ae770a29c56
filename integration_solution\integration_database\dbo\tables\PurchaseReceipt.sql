﻿CREATE TABLE [dbo].[PurchaseReceipt]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseReceipt_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NOT NULL,
    [PurchaseOrderId]   UNIQUEIDENTIFIER NULL,
    [VendorId]          UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NOT NULL,
    [DocumentDate]      DATE NULL,
    [IsActive]          BIT CONSTRAINT [DF_PurchaseReceipt_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_PurchaseReceipt_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_PurchaseReceipt_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT          [PK_PurchaseReceipt_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_PurchaseReceipt_PurchaseOrder] FOREIGN KEY (PurchaseOrderId) REFERENCES [dbo].[PurchaseOrder](Id),
    CONSTRAINT          [FK_PurchaseReceipt_Vendor] FOREIGN KEY (VendorId) REFERENCES [dbo].[Vendor](Id),
    CONSTRAINT          [FK_PurchaseReceipt_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseReceipt_Code
ON [dbo].[PurchaseReceipt] ([DocumentNumber])
GO