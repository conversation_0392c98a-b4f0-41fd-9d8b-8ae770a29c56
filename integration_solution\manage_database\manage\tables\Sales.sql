﻿CREATE TABLE [manage].[Sales]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Sales_Id] DEFAULT NEWID() NOT NULL,
    [SaleId]           UNIQUEIDENTIFIER NOT NULL,
    [ExternalNumber]    NVARCHAR(50),
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Sales_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Sales_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Sales_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Sales_SalesId
ON [manage].[Sales] ([SaleId])
GO
