﻿CREATE PROCEDURE dbo.GetWatermark
    @WatermarkCode NVARCHAR(50)  -- Increase size if needed
AS
BEGIN
    SET NOCOUNT ON;

    -- Insert if not exists
    IF NOT EXISTS (SELECT 1 FROM dbo.Watermark WHERE Code = @WatermarkCode)
    BEGIN
        INSERT INTO dbo.Watermark (Code, [Name], LastModifiedOn)
        VALUES (@WatermarkCode, @WatermarkCode, '1900-01-01T00:00:00');
    END

    -- Always return the current value
    SELECT LastModifiedOn
    FROM dbo.Watermark
    WHERE Code = @WatermarkCode;
END