﻿CREATE PROCEDURE [bc].[CustomerUpsert]
(
    @loadId NVARCHAR(40)
)
WITH EXECUTE AS OWNER
AS
BEGIN
    BEGIN TRY
        SET XACT_ABORT ON;
        BEGIN TRANSACTION;

        -- Update existing records
        UPDATE dest
           SET CompanyId = IIF(dest.ExternalCompanyId IS DISTINCT FROM source.CompanyId, NULL, dest.CompanyId),
               CustomerNumber = source.CustomerNumber,
               Name = source.Name,
               Name2 = source.Name2,
               Address = source.Address,
               Address2 = source.Address2,
               City = source.City,
               PostCode = source.PostCode,
               Region = source.Region,  
               CountryRegionCode = source.CountryRegionCode,
               PhoneNumber = source.PhoneNumber,
               Email = source.Email,
               CustomerType = source.CustomerType,
               Blocked = source.Blocked,
               ExternalCreatedOn = source.SystemCreatedAt,
               ExternalModifiedOn = source.SystemModifiedAt,
               ModifiedOn = SYSUTCDATETIME()
          FROM bc.Customer AS dest
         INNER JOIN bc.Customer_Staging AS source
            ON dest.ExternalCompanyId = source.CompanyId
           AND dest.ExternalId = source.Id
           AND source.ETL_LoadId = @loadId
         WHERE dest.ExternalModifiedOn < source.SystemModifiedAt
            OR dest.ExternalModifiedOn IS NULL;

        -- Insert new records
        INSERT INTO bc.Customer (
               ExternalCompanyId,
               ExternalId,
               CustomerNumber,
               Name,
               Name2,
               Address,
               Address2,
               City,
               PostCode,
               Region,
               CountryRegionCode,
               PhoneNumber,
               Email,
               CustomerType,
               Blocked,
               ExternalCreatedOn,
               ExternalModifiedOn
        )
        SELECT
               source.CompanyId,
               source.Id,
               source.CustomerNumber,
               source.Name,
               source.Name2,
               source.Address,
               source.Address2,
               source.City,
               source.PostCode,
               source.Region,
               source.CountryRegionCode,
               source.PhoneNumber,
               source.Email,
               source.CustomerType,
               source.Blocked,
               source.SystemCreatedAt,
               source.SystemModifiedAt
          FROM bc.Customer_Staging AS source
         WHERE source.ETL_LoadId = @loadId
           AND NOT EXISTS (
                SELECT 1
                  FROM bc.Customer AS dest
                 WHERE dest.ExternalCompanyId = source.CompanyId
                   AND dest.ExternalId = source.Id
           );

        UPDATE bc.CustomerLedgerEntry
           SET CustomerId = source.Id,
               ModifiedOn = SYSUTCDATETIME()
          FROM bc.CustomerLedgerEntry
         INNER JOIN bc.Customer AS source
            ON CustomerLedgerEntry.ExternalCustomerId = source.ExternalId
         WHERE CustomerLedgerEntry.CustomerId IS NULL

        TRUNCATE TABLE bc.Customer_Staging;

        COMMIT TRANSACTION;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
