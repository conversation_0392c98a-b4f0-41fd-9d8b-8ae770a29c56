using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Models.SalesCredits;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public static class SimplySalesExtensions
{
    public static SalesInvoice ToSalesInvoice(this SimplySalesHeaderEntity entity)
    {
        var salesInvoice = new SalesInvoice
        {
            ExternalReference = entity.InvoiceNumber.ToString(),
            AlternateNumber = entity.InvoiceNumber.ToString(),
            DocumentDate = entity.DocumentDate
        };
        
        // Add customer reference if available
        if (!string.IsNullOrEmpty(entity.PatientNumber))
        {
            salesInvoice.Patient = new ExternalReference
            {
                ExternalCode = entity.PatientNumber.Trim()
            };
        }
        
        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.Clinic))
        {
            salesInvoice.Clinic = new ExternalReference
            {
                Code = entity.Clinic
            };
        }
        
        return salesInvoice;
    }
    
    public static SalesInvoiceLine ToSalesInvoiceLine(this SimplySalesDetailEntity entity)
    {
        var salesInvoiceLine = new SalesInvoiceLine
        {
            Sequence = entity.Line,
            Quantity = entity.Quantity,
            ExternalReference = entity.Line.ToString(),
            AmountInclTax = entity.Amount + entity.GST,
            TaxAmount = entity.GST,
            DiscountAmount = entity.DiscountAmount,
            SerialNumber = entity.SerialNumber
        };

        salesInvoiceLine.AmountExclTax = salesInvoiceLine.AmountInclTax - salesInvoiceLine.TaxAmount;
        salesInvoiceLine.GrossAmount = salesInvoiceLine.AmountExclTax + salesInvoiceLine.DiscountAmount;

        if (salesInvoiceLine.Quantity != 0)
        {
            salesInvoiceLine.UnitPrice = salesInvoiceLine.GrossAmount / salesInvoiceLine.Quantity;
        }

        if (salesInvoiceLine.Quantity < 0)
        {
            salesInvoiceLine.UnitPrice = salesInvoiceLine.UnitPrice * -1;
            salesInvoiceLine.GrossAmount = salesInvoiceLine.GrossAmount * -1;
            salesInvoiceLine.DiscountAmount = salesInvoiceLine.DiscountAmount * -1;
            salesInvoiceLine.AmountExclTax = salesInvoiceLine.AmountExclTax * -1;
            salesInvoiceLine.TaxAmount = salesInvoiceLine.TaxAmount * -1;
            salesInvoiceLine.AmountInclTax = salesInvoiceLine.AmountInclTax * -1;
        }

            // Add product reference if available
            if (!string.IsNullOrEmpty(entity.ProductNumber))
        {
            salesInvoiceLine.Product = new ExternalReference
            {
                Code = entity.ProductNumber,
                ExternalCode = entity.ProductNumber
            };
        }
        
        return salesInvoiceLine;
    }

    public static SalesCredit ToSalesCredit(this SimplySalesHeaderEntity entity)
    {
        var salesCredit = new SalesCredit
        {
            ExternalReference = entity.InvoiceNumber.ToString(),
            AlternateNumber = entity.InvoiceNumber.ToString(),
            DocumentDate = entity.DocumentDate
        };

        // Add customer reference if available
        if (!string.IsNullOrEmpty(entity.PatientNumber))
        {
            salesCredit.Patient = new ExternalReference
            {
                ExternalCode = entity.PatientNumber.Trim()
            };
        }

        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.Clinic))
        {
            salesCredit.Clinic = new ExternalReference
            {
                Code = entity.Clinic
            };
        }

        return salesCredit;
    }

    public static SalesCreditLine ToSalesCreditLine(this SimplySalesDetailEntity entity)
    {
        var salesCreditLine = new SalesCreditLine
        {
            Sequence = entity.Line,
            Quantity = -entity.Quantity,
            ExternalReference = entity.Line.ToString(),
            AmountInclTax = entity.Amount + entity.GST,
            TaxAmount = entity.GST,
            DiscountAmount = entity.DiscountAmount,
            SerialNumber = entity.SerialNumber
        };

        salesCreditLine.AmountExclTax = salesCreditLine.AmountInclTax - salesCreditLine.TaxAmount;
        salesCreditLine.GrossAmount = salesCreditLine.AmountExclTax + salesCreditLine.DiscountAmount;

        if (salesCreditLine.Quantity != 0)
        {
            salesCreditLine.UnitPrice = salesCreditLine.GrossAmount / salesCreditLine.Quantity;
        }

        if (salesCreditLine.Quantity < 0)
        {
            salesCreditLine.UnitPrice = salesCreditLine.UnitPrice * -1;
            salesCreditLine.GrossAmount = salesCreditLine.GrossAmount * -1;
            salesCreditLine.DiscountAmount = salesCreditLine.DiscountAmount * -1;
            salesCreditLine.AmountExclTax = salesCreditLine.AmountExclTax * -1;
            salesCreditLine.TaxAmount = salesCreditLine.TaxAmount * -1;
            salesCreditLine.AmountInclTax = salesCreditLine.AmountInclTax * -1;
        }

        // Add product reference if available
        if (!string.IsNullOrEmpty(entity.ProductNumber))
        {
            salesCreditLine.Product = new ExternalReference
            {
                Code = entity.ProductNumber,
                ExternalCode = entity.ProductNumber
            };
        }

        return salesCreditLine;
    }
}