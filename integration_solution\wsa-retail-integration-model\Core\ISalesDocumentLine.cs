﻿namespace WSA.Retail.Integration.Core;

public interface ISalesDocumentLine : 
    IDocumentLine,
    IProductAssociated,
    IDescribable,
    IQuantifiable,
    ISerializable,
    IPriced
{
    public new IEnumerable<string> ValidateRequiredProperties()
    {
        var errors = new List<string>();
        var documentErrors = ((IDocumentLine)this).ValidateRequiredProperties();

        if (Product?.Code == null)
            errors.Add("Product code is required.");

        if (Quantity == null || Quantity == 0)
            errors.Add("Quantity code is required.");

        return documentErrors.Concat(errors);
    }
}
