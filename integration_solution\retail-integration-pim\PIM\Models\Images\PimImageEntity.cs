﻿using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Images
{
    public class PimImageEntity : IPimImageEntity
    {
        public Guid Id { get; set; } = Guid.Empty;
        public Guid ProductId { get; set; } = Guid.Empty;
        public Guid? BrandId { get; set; }
        public string? CDNUrl { get; set; }
        public bool IsDefault { get; set; }
        public DateTime SystemCreatedOn { get; set; }
        public DateTime SystemModifiedOn { get; set; }

        public PimProductEntity? ProductEntity { get; set; }
    }
}