﻿namespace WSA.Retail.Integration.Models.Configuration;

public enum EntityType
{
    Adjustment,
    Battery,
    Category,
    Claim,
    Clinic,
    Color,
    Company,
    Manufacturer,
    Model,
    Patient,
    Payment,
    PaymentMethod,
    Payor,
    Product,
    ProductModel,
    PurchaseOrder,
    PurchaseOrderLine,
    PurchaseReceipt,
    PurchaseReceiptLine,
    PurchaseReturn,
    PurchaseReturnLine,
    PurchaseShipment,
    PurchaseShipmentLine,
    SalesCredit,
    SalesCreditLine,
    SalesInvoice,
    SalesInvoiceLine,
    SalesOrder,
    SalesOrderLine,
    Subcategory,
    TaxGroup,
    Vendor
}
