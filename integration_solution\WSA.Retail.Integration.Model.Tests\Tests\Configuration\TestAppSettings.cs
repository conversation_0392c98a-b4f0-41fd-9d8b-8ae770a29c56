﻿using WSA.Retail.Integration.Configuration;

namespace WSA.Retail.Integration.Tests.Configuration;

public static class TestAppSettings
{
    public static AppSettings CreateDefault()
    {
        return new AppSettings
        {
            AppName = "TestApp",
            EventGridEndpoint = "https://test-eventgrid.azure.net/api/events",
            EventGridAccessKey = "test",
            ExternalSystemCode = "TEST1",
            FromStorageQueueName = "from-test",
            QueueRetryInterval = "5",
            QueueMaxRetryInterval = "1440",
            SqlConnectionString = "Server=tcp:test.database.windows.net,1433;Initial Catalog=test;Persist Security Info=False;User ID=test;Password=test;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
            StorageQueueConnectionString = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net",
            ToStorageQueueName = "to-test",
        };
    }
}