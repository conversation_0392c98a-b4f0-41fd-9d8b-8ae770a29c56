namespace WSA.Integration.API.V1;

using WSA.Integration;
using Microsoft.Bank.BankAccount;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Receivables;
using Microsoft.Finance.GeneralLedger.Posting;
using Microsoft.Finance.GeneralLedger.Journal;


codeunit 50125 "WSA Payment Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Payment Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    begin
        if not TryHandlePost(Request) then
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        CustLedgEntry: Record "Cust. Ledger Entry";

    begin
        if not TryHandleGet(Request, CustLedgEntry) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log")

    var
        genJournalLine: Record "Gen. Journal Line";
        custLedgEntry: Record "Cust. Ledger Entry";
        ApiCommon: Codeunit "WSA API Common";
        json: JsonObject;
        descriptionValue: JsonValue;
        description: Text[250];
        paymentAmount: Decimal;
        firstLineNo: Integer;
        lastLineNo: Integer;

    begin
        json := Common.GetJsonFromBlob(Request);

        if IsDuplicate(json) then begin
            Common.SetErrorResponse(Request, 'Duplicate payment');
            exit
        end;

        ValidateExternalReferences(json);

        paymentAmount := Common.GetJsonValue(json, '$.amount').AsDecimal();
        if paymentAmount = 0 then begin
            Common.SetNoContentResponse(Request);
            exit;
        end;

        if not InitGenJournalLine(json, genJournalLine) then begin
            Common.SetErrorResponse(Request, GetLastErrorText());
            exit
        end;

        SetDocumentType(json, genJournalLine);
        genJournalLine.Validate("Document No.", Common.GetJsonValue(json, '$.documentNumber').AsCode());
        genJournalLine.Validate("WSA Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
        SetDates(json, genJournalLine);
        SetPatient(json, genJournalLine);
        SetPaymentMethod(json, genJournalLine);
        genJournalLine.Validate(Amount, -paymentAmount);
        SetAppliesTo(json, genJournalLine);

        descriptionValue := Common.GetJsonValue(json, '$.description');
        if not descriptionValue.IsNull() then begin
            description := descriptionValue.AsText();
            if description <> '' then
                genJournalLine.Description := description;
        end;

        genJournalLine.Insert();
        firstLineNo := genJournalLine."Line No.";

        SetBalancingLine(genJournalLine);
        if description <> '' then
            genJournalLine.Description := description;

        genJournalLine.Insert();
        lastLineNo := genJournalLine."Line No.";

        genJournalLine.SetRange("Journal Template Name", genJournalLine."Journal Template Name");
        genJournalLine.SetRange("Journal Batch Name", genJournalLine."Journal Batch Name");
        genJournalLine.SetFilter("Line No.", '%1..%2', firstLineNo, lastLineNo);

        if PostPayment(genJournalLine, custLedgEntry) then begin
            ApiCommon.ApplyCustLedgEntries(custLedgEntry."Customer No.", custLedgEntry."Document No.", custLedgEntry."External Document No.");
            Common.SetCreatedResponse(Request, Common.PaymentToJson(custLedgEntry))
        end else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure InitGenJournalLine(
        json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line"): Boolean

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        GenJouranlTemplate: Record "Gen. Journal Template";

    begin
        RetailIntegrationSetup.SafeGet();
        RetailIntegrationSetup.TestField("Payment Journal Template");
        RetailIntegrationSetup.TestField("Payment Journal Batch");

        GenJouranlTemplate.Get(RetailIntegrationSetup."Payment Journal Template");

        GenJournalLine.Init();
        GenJournalLine.Validate("Journal Template Name", RetailIntegrationSetup."Payment Journal Template");
        GenJournalLine.Validate("Journal Batch Name", RetailIntegrationSetup."Payment Journal Batch");
        GenJournalLine.Validate("Line No.", GetLastLineNo(
            RetailIntegrationSetup."Payment Journal Template",
            RetailIntegrationSetup."Payment Journal Batch") + 10000);
        GenJournalLine.Validate("Source Code", GenJouranlTemplate."Source Code");
        exit(true);
    end;


    [TryFunction]
    local procedure TryHandleGet(
         var Request: Record "WSA Integration Request Log";
         var CustLedgEntry: Record "Cust. Ledger Entry")

    var
        recRef: RecordRef;
        json: JsonObject;
        idValue: JsonValue;
        documentNoValue: JsonValue;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        idValue := Common.GetJsonValue(json, '$.id');
        if not (idValue.IsNull) then begin
            if CustLedgEntry.GetBySystemId(idValue.AsText()) then begin
                //Request := SalesInvoice."No.";
                //Request.Modify();
                Common.SetOkResponse(Request, Common.PaymentToJson(CustLedgEntry));
                exit;
            end;
        end;

        documentNoValue := Common.GetJsonValue(json, '$.documentNumber');
        if not (documentNoValue.IsNull) then begin
            CustLedgEntry.SetRange("Document No.", documentNoValue.AsCode());
            if CustLedgEntry.FindFirst() then begin
                //Request."Sales Invoice No." := SalesInvoice."No.";
                //Request.Modify();
                Common.SetOkResponse(Request, Common.PaymentToJson(CustLedgEntry));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    local procedure GetLastLineNo(
        TemplateCode: Code[10];
        BatchCode: Code[10]): Integer

    var
        genJournalLine: Record "Gen. Journal Line";

    begin
        genJournalLine.SetRange("Journal Template Name", TemplateCode);
        genJournalLine.SetRange("Journal Batch Name", BatchCode);
        if genJournalLine.FindLast() then
            exit(genJournalLine."Line No.")
        else
            exit(0);
    end;


    local procedure SetDocumentType(
        json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        paymentAmount: Decimal;

    begin
        paymentAmount := Common.GetJsonValue(json, '$.amount').AsDecimal();
        if paymentAmount > 0 then
            GenJournalLine.Validate("Document Type", GenJournalLine."Document Type"::Payment)
        else
            GenJournalLine.Validate("Document Type", GenJournalLine."Document Type"::Refund);
    end;


    local procedure SetDates(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if newDate <> 0D then begin
            GenJournalLine.Validate("Posting Date", newDate);
            GenJournalLine.Validate("Document Date", newDate);
        end;
    end;


    local procedure SetPatient(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        PatientValue: JsonValue;
        PayorValue: JsonValue;
        CustomerNo: Code[20];

    begin
        PatientValue := Common.GetJsonValue(json, '$.patient.code');
        if not PatientValue.IsNull() then
            CustomerNo := PatientValue.AsCode();

        if CustomerNo = '' then begin
            PayorValue := Common.GetJsonValue(json, '$.payor.code');
            if not PayorValue.IsNull() then
                CustomerNo := PayorValue.AsCode();
        end;

        GenJournalLine.Validate("Account Type", GenJournalLine."Account Type"::Customer);
        GenJournalLine.Validate("Account No.", CustomerNo);
    end;


    local procedure SetPaymentMethod(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        PaymentMethod: Record "Payment Method";

    begin
        PaymentMethod.Get(Common.GetJsonValue(json, '$.paymentMethod').AsCode());
        GenJournalLine.Validate("Payment Method Code", PaymentMethod.Code);
    end;


    local procedure SetAppliesTo(
        Json: JsonObject;
        var GenJournalLine: Record "Gen. Journal Line")

    var
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.appliesTo');
        if not jValue.IsNull then begin
            GenJournalLine.Validate("External Document No.", jValue.AsCode());
        end;
    end;


    local procedure SetBalancingLine(
        var GenJournalLine: Record "Gen. Journal Line")

    var
        GenJournalLine2: Record "Gen. Journal Line";
        PaymentMethod: Record "Payment Method";

    begin
        GenJournalLine2 := GenJournalLine;

        PaymentMethod.Get(genJournalLine."Payment Method Code");
        PaymentMethod.TestField("Bal. Account No.");

        GenJournalLine."Line No." := GenJournalLine."Line No." + 10000;
        case PaymentMethod."Bal. Account Type" of
            PaymentMethod."Bal. Account Type"::"G/L Account":
                GenJournalLine.Validate("Account Type", "Gen. Journal Account Type"::"G/L Account");
            PaymentMethod."Bal. Account Type"::"Bank Account":
                GenJournalLine.Validate("Account Type", "Gen. Journal Account Type"::"Bank Account");
            PaymentMethod."Bal. Account Type"::Customer:
                GenJournalLine.Validate("Account Type", "Gen. Journal Account Type"::Customer);
        end;

        GenJournalLine.Validate("Account No.", PaymentMethod."Bal. Account No.");
        GenJournalLine.Validate("WSA Responsibility Center", GenJournalLine2."WSA Responsibility Center");
        GenJournalLine.Validate(Amount, -GenJournalLine2.Amount);
        GenJournalLine.Description := GenJournalLine2.Description;
    end;


    local procedure PostPayment(
        var GenJournalLine: Record "Gen. Journal Line";
        var CustLedgEntry: Record "Cust. Ledger Entry") Ok: Boolean

    var
        GenJournalLine2: Record "Gen. Journal Line";

    begin
        ClearLastError();
        GenJournalLine2 := GenJournalLine;

        GenJournalLine.SendToPosting(Codeunit::"Gen. Jnl.-Post Batch");
        CustLedgEntry.SetRange("Document Type", GenJournalLine2."Document Type");
        CustLedgEntry.SetRange("Document No.", GenJournalLine2."Document No.");
        exit(CustLedgEntry.FindLast());
        exit(false);
    end;


    local procedure ValidateExternalReferences(json: JsonObject)
    begin
        ValidatePatient(json);
    end;

    local procedure IsDuplicate(json: JsonObject): Boolean
    var
        CustLedgEntry: Record "Cust. Ledger Entry";
        DocumentNo: Code[20];
        PatientValue: JsonValue;
        PayorValue: JsonValue;
        CustomerNo: Code[20];

    begin
        DocumentNo := Common.GetJsonValue(json, '$.documentNumber').AsCode();

        PatientValue := Common.GetJsonValue(json, '$.patient.code');
        if not PatientValue.IsNull() then
            CustomerNo := PatientValue.AsCode();

        if CustomerNo = '' then begin
            PayorValue := Common.GetJsonValue(json, '$.payor.code');
            if not PayorValue.IsNull() then
                CustomerNo := PayorValue.AsCode();
        end;

        CustLedgEntry.SetRange("Customer No.", CustomerNo);
        CustLedgEntry.SetRange("Document No.", DocumentNo);
        CustLedgEntry.SetRange(Reversed, false);
        exit(not CustLedgEntry.IsEmpty());
    end;


    local procedure ValidatePatient(json: JsonObject): Boolean
    var
        Customer: Record Customer;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.patient.code');
        if jValue.IsNull() then
            exit(false);
    end;


    var
        Common: Codeunit "WSA Common";
}
