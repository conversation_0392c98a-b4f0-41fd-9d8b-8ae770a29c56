namespace WSA.Integration;

codeunit 50105 "Color Events"
{
    [EventSubscriber(ObjectType::Table, Database::Color, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Color;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendColorToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Color, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Color;
        var xRec: Record Color;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendColorToIntegrationAPI(Rec);
    end;


    local procedure SendColorToIntegrationAPI(Color: Record Color)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Color.Code = '' then
            exit;

        if Color.Description = '' then
            exit;

        if IsNullGuid(Color.SystemId) then
            exit;

        JObject.Add('code', Color.Code);
        JObject.Add('externalCode', Format(Color.SystemId).TrimStart('{').TrimEnd('}'));
        JObject.Add('name', Color.Description);
        JObject.Add('hexCode', Color."Hex Code");

        if not IntegrationManagement.TrySendToApi(JObject, 'colors') then
            exit;
    end;
}