using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Payments;
using WSA.Retail.Integration.Models.Configuration;

namespace WSA.Retail.Integration.Simply.Models.Payments;

public class SimplyCashReceiptsProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyCashReceiptsProcessor> logger,
    ISimplyCashReceiptsRepository simplyCashReceiptsRepository,
    IPaymentService paymentService,
    IEntityService entityService)
    : ISimplyCashReceiptsProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyCashReceiptsProcessor> _logger = logger;
    private readonly ISimplyCashReceiptsRepository _simplyCashReceiptsRepository = simplyCashReceiptsRepository;
    private readonly IPaymentService _paymentService = paymentService;
    private readonly IEntityService _entityService = entityService;

    public async Task ProcessSimplyCashReceiptsTransactionsAsync()
    {
        _logger.LogMethodStart();

        var records = await _simplyCashReceiptsRepository.GetIntegrationRecordsAsync();
        if (records == null || records.Count == 0)
        {
            _logger.LogCustomInformation("No payment records found for integration");
            return ;
        }

        _logger.LogCustomInformation($"Found {records.Count} payment records for integration");
        await Parallel.ForEachAsync(
            records,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (record, cancellationToken) =>
            {
                await ProcessSimplyCashReceiptsTransactionAsync(record);
            });
    }

    public async Task<bool> ProcessSimplyCashReceiptsTransactionAsync(SimplyCashReceiptsEntity record)
    {
        _logger.LogMethodStart();
        var payment = record.ToPayment();
        payment.ExternalSystemCode = _appSettings.ExternalSystemCode;

        // Set document number
        payment.DocumentNumber ??= await _entityService.GetNextNumberAsync(EntityType.Payment.GetEntityCode());

        // Resolve external references
        await _paymentService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, payment);

        // Verify required data
        bool isValid = VerifyRequiredData(payment);
        if (!isValid)
        {
            _logger.LogCustomError($"Payment {payment.DocumentNumber} has missing required data");
            return false;
        }

        if (payment.Description == null && payment.Patient != null)
        {
            payment.Description = $"{payment.Patient.Code ?? "n/a"} {payment.PaymentMethod ?? "n/a"}";
        }

        if (payment.Description == null && payment.Payor != null)
        {
            payment.Description = $"{payment.Payor.Code ?? "n/a"} {payment.PaymentMethod ?? "n/a"}";
            //payment.Description = payment.Description ?? payment.Payor.Name;
        }

        // Upsert the payment
        var result = await _paymentService.UpsertAsync(payment);
        if (result != null)
        {
            // Update integration status
            await _simplyCashReceiptsRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed payment {payment.DocumentNumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process payment {payment.DocumentNumber}");
            return false;
        }
    }
      
    private bool VerifyRequiredData(Payment payment)
    {
        if (string.IsNullOrEmpty(payment.ExternalSystemCode))
        {
            _logger.LogCustomError("ExternalSystemCode is required");
            return false;
        }

        if (string.IsNullOrEmpty(payment.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }
        
        if (payment.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }
        
        if (payment.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (payment.PaymentMethod == null)
        {
            _logger.LogCustomError("PaymentMethod is required");
            return false;
        }

        if (payment.Patient?.Code == null && payment.Payor?.Code == null)
        {
            _logger.LogCustomError("Either Patient or Payor is required");
            return false;
        }

        return true;
    }
}