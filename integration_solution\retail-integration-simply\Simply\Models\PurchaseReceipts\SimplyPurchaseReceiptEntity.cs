namespace WSA.Retail.Integration.Simply.Models.PurchaseReceipts;

public class SimplyPurchaseReceiptEntity
{
    public Guid Id { get; set; }
    public int? TotalQuantity { get; set; }
    public decimal? UnitCost { get; set; }
    public int? Quantity { get; set; }
    public string? ItemNumber { get; set; }
    public int? LineNumber { get; set; }
    public int? DocNumber { get; set; }
    public string? OrderType { get; set; }
    public DateTime? TransactionDate { get; set; }
    public string? SerialNumber { get; set; }
    public string? FileName { get; set; }
    public DateTime? CreatedOn { get; set; }
    public DateTime? ModifiedOn { get; set; }
    public bool IntegrationRequired { get; set; }
    public DateTime? IntegrationDate { get; set; }
}