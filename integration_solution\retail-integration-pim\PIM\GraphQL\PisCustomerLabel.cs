﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisCustomerLabel
{
    [JsonPropertyName("code")] public string? Code { get; set; }

    [JsonPropertyName("description")] public string? Description { get; set; }

    [JsonPropertyName("isDefault")] public string? IsDefault { get; set; }

    [JsonPropertyName("state")] public StateOutputType? State { get; set; }
}
