﻿using GraphQL.Client.Http;
using GraphQL.Client.Serializer.SystemTextJson;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.Models.Attributes;
using WSA.Retail.Integration.PIM.Models.Brands;
using WSA.Retail.Integration.PIM.Models.BundleSets;
using WSA.Retail.Integration.PIM.Models.Categories;
using WSA.Retail.Integration.PIM.Models.Colors;
using WSA.Retail.Integration.PIM.Models.Countries;
using WSA.Retail.Integration.PIM.Models.Images;
using WSA.Retail.Integration.PIM.Models.Products;
using WSA.Retail.Integration.PIM.Queries;

namespace WSA.Retail.Integration.PIM.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPimServices(
        this IServiceCollection services,
        AppSettings appSettings)
    {
        // ==== ProCAT API =================================================
        services.AddSingleton(sp =>
        {
            var graphQLClient = new GraphQLHttpClient(
                new GraphQLHttpClientOptions
                {
                    EndPoint = new Uri(appSettings.PimBaseUrl)
                },
                new SystemTextJsonSerializer());

            graphQLClient.HttpClient.Timeout = TimeSpan.FromMinutes(5);
            return graphQLClient;
        });


        // ==== DB CONTEXT =================================================
        bool enableDetailedDbLogging = Convert.ToBoolean(Environment.GetEnvironmentVariable("EnableDetailedDbLogging") ?? "false");

        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.SqlConnectionString, "SqlConnectionString");
        services.AddDbContextFactory<PimDbContext>(options =>
        {
            var dbOptionsBuilder = options.UseSqlServer(
                appSettings.SqlConnectionString,
                sqlServerOptions => sqlServerOptions.CommandTimeout(180));

            if (enableDetailedDbLogging)
            {
                dbOptionsBuilder
                    .EnableDetailedErrors(true)
                    .EnableSensitiveDataLogging(true)
                    .LogTo(s => System.Diagnostics.Debug.WriteLine(s));
            }
        });

        // ==== INTERFACES =================================================
        services.AddScoped<IPimAttributeRepository, PimAttributeRepository>();
        services.AddScoped<IPimBrandRepository, PimBrandRepository>();
        services.AddScoped<IPimBundleSetRepository, PimBundleSetRepository>();
        services.AddScoped<IPimCategoryRepository, PimCategoryRepository>();
        services.AddScoped<IPimColorRepository, PimColorRepository>();
        services.AddScoped<IPimCountryRepository, PimCountryRepository>();
        services.AddScoped<IPimImageRepository, PimImageRepository>();
        services.AddScoped<IPimParentProductRepository, PimParentProductRepository>();
        services.AddScoped<IPimProductRepository, PimProductRepository>();

        services.AddScoped<IBaseProductService, BaseProductService>();
        services.AddScoped<ICategoryQueryService, CategoryQueryService>();
        services.AddScoped<IProductQueryService, ProductQueryService>();

        services.AddScoped<ITokenService, TokenService>();
        return services;
    }
}

