using Azure.Messaging.EventHubs;
using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class FromManageEventProcessor : LoggingBase<FromManageEventProcessor>
{
    private readonly AppSettings _appSettings;
    private readonly QueueClient _queueClient;
    private readonly BlobContainerClient _archiveContainerClient;
    private readonly EventProcessor _eventProcessor;
    private static readonly SemaphoreSlim _apiSemaphore = new(1, 1);

    public FromManageEventProcessor(
        IOptions<AppSettings> appSettings,
        ILogger<FromManageEventProcessor> logger,
        Dictionary<string, QueueClient> queueClients,
        BlobServiceClient blobServiceClient,
        EventProcessor eventProcessor)
        : base(logger)
    {
        _appSettings = appSettings.Value;

        if (!queueClients.TryGetValue("from-manage", out var queueClient) || queueClient == null)
        {
            throw new InvalidOperationException("QueueClient for 'from-manage' not found.");
        }
        _queueClient = queueClient;
        _eventProcessor = eventProcessor;

        _archiveContainerClient = blobServiceClient.GetBlobContainerClient(_appSettings.EventArchiveContainerName);
        _archiveContainerClient.CreateIfNotExists();
    }

    [Function("HandleEventHubMessageAsync")]
    public async Task HandleEventHubMessageAsync(
        [EventHubTrigger("%EventHubName%", Connection = "EventHubConnectionString", ConsumerGroup = "%EventHubConsumerGroup%")] 
        EventData[] events)
    {
        LogMethodStart();

        foreach (EventData eventData in events)
        {
            try
            {
                var msgString = eventData.EventBody.ToString();
                var message = JsonSerializer.Deserialize<EventHubMessage>(msgString, Utilities.Common.GetJsonOptions());
                LogCustomInformation(
                    $"Received event:\n{JsonSerializer.Serialize(message, Utilities.Common.GetJsonOptions())}");

                if (message?.Event != null)
                {
                    _queueClient.CreateIfNotExists();
                    if (_queueClient.Exists())
                    {
                        byte[] utf8Bytes = Encoding.UTF8.GetBytes(eventData.EventBody.ToString());
                        string base64String = Convert.ToBase64String(utf8Bytes);
                        SendReceipt receipt = await _queueClient.SendMessageAsync(base64String);
                        LogCustomInformation(
                            $"Queue message sent. MessageId: {receipt.MessageId}, " +
                            $"PopReceipt: {receipt.PopReceipt}, " +
                            $"TimeNextVisible: {receipt.TimeNextVisible}, " +
                            $"ExpirationTime: {receipt.ExpirationTime}");
                    }
                }
                else
                {
                    LogCustomError("Event did not deserialize successfully");
                }
            }
                catch (Exception ex)
                {
                    LogCustomError(ex);
                }
        }

    }

    [Function(nameof(HandleFromQueueMessageAsync))]
    public async Task HandleFromQueueMessageAsync([QueueTrigger("%FromStorageQueueName%", Connection = "StorageQueueConnectionString")] QueueMessage queueMessage)
    {
        LogMethodStart();

        string messageId = queueMessage.MessageId;
        string popReceipt = queueMessage.PopReceipt;
        string message = queueMessage.Body.ToString();
        var dequeueCount = queueMessage.DequeueCount;

        await _apiSemaphore.WaitAsync();
        try
        {
            var eventMessage = JsonSerializer.Deserialize<EventHubMessage>(message, Utilities.Common.GetJsonOptions());
            if (eventMessage?.Event != null)
            {
                if ((eventMessage.Headers?.TenantId ?? Guid.Empty) != _appSettings.ManageTenantId)
                {
                    LogCustomWarning($"Tenant Id mismatch: {eventMessage.Headers?.TenantId} != {_appSettings.ManageTenantId}");
                    await _queueClient.DeleteMessageAsync(messageId, popReceipt);
                    return;
                }

                if (eventMessage.Archive ?? true)
                {
                    await ArchiveEventDataAsync(eventMessage);
                }
                else
                {
                    LogCustomInformation($"Archiving skipped. BlobPath already exists: {eventMessage.BlobPath}");
                }

                if (await _eventProcessor.ProcessFromQueueEventAsync(eventMessage.Event, eventMessage))
                {
                    await _queueClient.DeleteMessageAsync(messageId, popReceipt);
                    return;
                }
                else
                {
                    await SetMessageToRetryAsync(messageId, popReceipt, message, dequeueCount);
                    return;
                };
            }
            else
            {
                await SetMessageToRetryAsync(messageId, popReceipt, message, dequeueCount);
                return;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            await SetMessageToRetryAsync(messageId, popReceipt, message, dequeueCount);
            return;
        }
        finally
        {
            _apiSemaphore.Release();
        }
    }

    private async Task SetMessageToRetryAsync(
        string messageId,
        string popReceipt,
        string message,
        long dequeueCount)
    {
        LogMethodStart();

        byte[] utf8Bytes = Encoding.UTF8.GetBytes(message);
        string base64String = Convert.ToBase64String(utf8Bytes);

        var queueRetryInterval = _appSettings.QueueRetryInterval;
        var queueMaxRetryInterval = _appSettings.QueueMaxRetryInterval;

        int baseDelay = int.Parse(queueRetryInterval ?? "0");
        int retryDelay = baseDelay * (int)Math.Pow(2, dequeueCount - 1);
        int maxDelayMinutes = int.Parse(queueMaxRetryInterval ?? "0");
        retryDelay = Math.Min(retryDelay, maxDelayMinutes);

        await _queueClient.UpdateMessageAsync(messageId, popReceipt, base64String, TimeSpan.FromMinutes(retryDelay));

        LogCustomInformation(
            $"Retrying message {messageId} after {retryDelay} minutes. Dequeue Count: {dequeueCount}");
    }

    private async Task ArchiveEventDataAsync(EventHubMessage message)
    {
        LogMethodStart();

        try
        {
            // Create a unique blob name with timestamp and sequence number
            var timestamp = DateTimeOffset.UtcNow;
            var sequenceNumber = message.MessageId;
            string blobName = $"{message.Domain}\\{timestamp:yyyy}\\{timestamp:MM}\\{timestamp:dd}\\{timestamp:yyyyMMddHHmmss}-{sequenceNumber}.json";

            // Get a reference to the blob
            BlobClient blobClient = _archiveContainerClient.GetBlobClient(blobName);
            string eventBodyRaw = JsonSerializer.Serialize(message, Utilities.Common.GetJsonOptions());

            // Convert to JSON and upload to blob storage
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(eventBodyRaw));
            await blobClient.UploadAsync(stream, overwrite: true);
            message.BlobPath = blobName;
            LogCustomInformation($"Archived event to {blobName}");
        }
        catch (Exception ex)
        {
            LogCustomError(ex, $"Failed to archive event: {ex.Message}");
            // We don't want archiving failures to stop the main processing flow
        }
    }
}