﻿CREATE TABLE [cosium].[staging_produit] (
    [id]                                   NVARCHAR (50)  NOT NULL,
    [reffournisseur]                       NVARCHAR (50)  NULL,
    [refmarque]                            NVARCHAR (50)  NULL,
    [famille]                              NVARCHAR (50)  NULL,
    [codeproduit]                          NVARCHAR (50)  NULL,
    [cote]                                 NVARCHAR (50)  NULL,
    [osseuxaerien]                         NVARCHAR (50)  NULL,
    [intracontour]                         NVARCHAR (50)  NULL,
    [matiere]                              NVARCHAR (50)  NULL,
    [numagrement]                          NVARCHAR (50)  NULL,
    [cache]                                NVARCHAR (50)  NULL,
    [dureegarantie]                        NVARCHAR (50)  NULL,
    [typedureegarantie]                    NVARCHAR (50)  NULL,
    [avecnumserie]                         NVARCHAR (50)  NULL,
    [numeriqueanalogique]                  NVARCHAR (50)  NULL,
    [codebarrecomm]                        NVARCHAR (50)  NULL,
    [libelle]                              NVARCHAR (200) NULL,
    [seuilproduit]                         NVARCHAR (50)  NULL,
    [libref]                               NVARCHAR (100) NULL,
    [prixventeht]                          NVARCHAR (50)  NULL,
    [rangniveau]                           NVARCHAR (50)  NULL,
    [prixachatproduit]                     NVARCHAR (50)  NULL,
    [prixachatcatalogue]                   NVARCHAR (50)  NULL,
    [reftva]                               NVARCHAR (50)  NULL,
    [prixventettc]                         NVARCHAR (50)  NULL,
    [codemarketinggammefournisseur]        NVARCHAR (50)  NULL,
    [prixgarantie]                         NVARCHAR (50)  NULL,
    [prixprestation]                       NVARCHAR (50)  NULL,
    [typeprestation]                       NVARCHAR (50)  NULL,
    [datemodif]                            NVARCHAR (50)  NULL,
    [refproduitsuccursale]                 NVARCHAR (50)  NULL,
    [productsbenefitslistcodehearingclass] NVARCHAR (50)  NULL,
    [taille1]                              NVARCHAR (50)  NULL,
    [taille2]                              NVARCHAR (50)  NULL,
    [taille3]                              NVARCHAR (50)  NULL,
    [geometrie]                            NVARCHAR (50)  NULL,
    [photochromic]                         NVARCHAR (50)  NULL,
    [teinte]                               NVARCHAR (50)  NULL,
    [codeedi]                              NVARCHAR (50)  NULL,
    [codecommandeedi]                      NVARCHAR (50)  NULL,
    [codegtin]                             NVARCHAR (50)  NULL,
    [contactlensduration]                  NVARCHAR (50)  NULL,
    [lenspackaging]                        NVARCHAR (50)  NULL,
    [sex]                                  NVARCHAR (50)  NULL,
    [deleted]                              NVARCHAR (50)  NULL
);
GO