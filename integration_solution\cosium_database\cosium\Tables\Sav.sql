﻿CREATE TABLE [cosium].[Sav] (
    [id]                   NVARCHAR (50)  NOT NULL,
    [refappareil]          NVARCHAR (50)  NULL,
    [refclient]            NVARCHAR (50)  NULL,
    [bonreparation]        NVARCHAR (50)  NULL,
    [creationdate]         NVARCHAR (50)  NULL,
    [etat]                 NVARCHAR (50)  NULL,
    [typesav]              NVARCHAR (50)  NULL,
    [reparateur]           NVARCHAR (50)  NULL,
    [commentaire]          NVARCHAR (MAX) NULL,
    [enddate]              NVARCHAR (50)  NULL,
    [patientdate]          NVARCHAR (50)  NULL,
    [refdevis]             NVARCHAR (50)  NULL,
    [bontransport]         NVARCHAR (50)  NULL,
    [refuser]              NVARCHAR (50)  NULL,
    [savtermine]           NVARCHAR (50)  NULL,
    [typebon]              NVARCHAR (50)  NULL,
    [reffacture]           NVARCHAR (50)  NULL,
    [commentairerepa]      NVARCHAR (MAX) NULL,
    [fingarantie]          NVARCHAR (50)  NULL,
    [refmarque]            NVARCHAR (50)  NULL,
    [centre]               NVARCHAR (50)  NULL,
    [imperativereturndate] NVARCHAR (50)  NULL,
    [modificationdate]     NVARCHAR (50)  NULL,
    [CreatedOn]            DATETIME2 CONSTRAINT [DF_Sav_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]           DATETIME2 CONSTRAINT [DF_Sav_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_Sav] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].[Sav_UpdateModified]
ON [cosium].[Sav]
AFTER UPDATE 
AS
   UPDATE [cosium].[Sav]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[Sav].[id] = i.[id]
GO