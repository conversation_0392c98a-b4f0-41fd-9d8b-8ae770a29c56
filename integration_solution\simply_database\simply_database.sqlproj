﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>simply_database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{2b28f60b-20e1-488c-bd11-fd1bd84ada1b}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>simply_database</RootNamespace>
    <AssemblyName>simply_database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="simply" />
    <Folder Include="simply\schemas" />
    <Folder Include="simply\tables" />
    <Folder Include="simply\sproc" />
    <Folder Include="simply\script" />
    <Folder Include="dbo" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="simply\tables\CashReceipts.sql" />
    <Build Include="simply\tables\Customer.sql" />
    <Build Include="simply\schemas\simply.sql" />
    <Build Include="simply\tables\PO_Detail.sql" />
    <Build Include="simply\tables\PO_Header.sql" />
    <Build Include="simply\tables\PurchaseReceipt.sql" />
    <Build Include="simply\tables\SalesDetail.sql" />
    <Build Include="simply\tables\SalesHeader.sql" />
    <Build Include="simply\tables\SalesFunder.sql" />
    <Build Include="simply\tables\Payment.sql" />
    <Build Include="simply\tables\Staging_Payment.sql" />
    <Build Include="simply\sproc\UpsertPayment.sql" />
    <Build Include="simply\sproc\GetProducts.sql" />
    <Build Include="simply\sproc\GetItemAdjustments.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="simply\script\Watermark.sql" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\integration_database\integration_database.sqlproj">
      <Name>integration_database</Name>
      <Project>{aa403ac7-dd2d-4ca2-bd2a-c2d5fb3671cb}</Project>
      <Private>True</Private>
      <SuppressMissingDependenciesErrors>False</SuppressMissingDependenciesErrors>
      <DatabaseVariableLiteralValue>$(integration_database)</DatabaseVariableLiteralValue>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <SqlCmdVariable Include="integration_database">
      <DefaultValue>integration_database</DefaultValue>
      <Value>$(SqlCmdVar__1)</Value>
    </SqlCmdVariable>
  </ItemGroup>
</Project>