﻿using WSA.Retail.Integration.Manage.Configuration;

namespace WSA.Retail.Integration.Manage.Tests.Configuration;

public static class TestAppSettings
{
    public static AppSettings CreateDefault()
    {
        return new AppSettings
        {
            AppName = "TestApp",
            //ClientId = "test",
            // ClientSecret = "test",
            EventGridEndpoint = "https://test-eventgrid.azure.net/api/events",
            EventGridAccessKey = "test",
            EventHubConnectionString = "Endpoint=sb://test.servicebus.windows.net/;SharedAccessKeyName=wsa",
            EventHubConsumerGroup = "wsa_bc",
            EventHubName = "entityevents-test",
            ExternalSystemCode = "TEST",
            FromStorageQueueName = "from-test",
            LocalTimeZone = "Australia/Melbourne",
            ManageApiBaseUrl = "https://test-manageapigateway.auditdata.app",
            ManageApiKey = "test",
            ManageTenantId = Guid.Parse("b4f1cf87-d54b-4887-a3f5-a515a59d7f02"),
            QueueRetryInterval = "5",
            QueueMaxRetryInterval = "1440",
            // Scope = "test",
            SqlConnectionString = "Server=tcp:test.database.windows.net,1433;Initial Catalog=test;Persist Security Info=False;User ID=test;Password=test;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
            StorageQueueConnectionString = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net",
            ToStorageQueueName = "to-test",
            EventHubConnectionString = "test",
            EventHubConsumerGroup = "testGroup",
            EventHubName = "test",
            LocalTimeZone = "UTC-01",
            ManageTenantId = new Guid()
        };
    }
}
