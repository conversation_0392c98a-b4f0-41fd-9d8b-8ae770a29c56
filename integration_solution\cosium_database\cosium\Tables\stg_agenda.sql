﻿CREATE TABLE [cosium].[stg_agenda](
	[id] [nvarchar](50) NOT NULL,
	[object] [varchar](max) NULL,
	[journee<PERSON><PERSON>e] [varchar](max) NULL,
	[remarque] [varchar](max) NULL,
	[utilisateur] [varchar](max) NULL,
	[refclient] [varchar](max) NULL,
	[categorie] [varchar](max) NULL,
	[daterdv] [varchar](max) NULL,
	[datefinrdv] [varchar](max) NULL,
	[supprime] [varchar](max) NULL,
	[rdvmanque] [varchar](max) NULL,
	[rdvprive] [varchar](max) NULL,
	[rdvarrive] [varchar](max) NULL,
	[compterendu] [nvarchar](max) NULL,
	[datemodif] [varchar](max) NULL,
	[creationdate] [varchar](max) NULL,
	[rdvannule] [varchar](max) NULL,
	[creator_id] [varchar](max) NULL,
	[site_id] [varchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO


