﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Data;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Utilities;


namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class SalesInvoiceEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<SalesInvoiceEventHandler> logger,
    BlobServiceClient blobServiceClient,
    Dictionary<string, QueueClient> queueClients,
    IManageAPI manageApi,
    IDbContextFactory<ManageDbContext> contextFactory,
    ISalesInvoiceService salesInvoiceService,
    IClinicService clinicService,
    IPatientService patientService,
    IProductService productService) : 
        EventHandlerBase<DeliveryNoteCreatedEvent, SalesInvoice, InvoiceEventEntity>(
            appSettings, 
            logger, 
            blobServiceClient,
            queueClients),
        ISalesInvoiceEventHandler
{
    private readonly IManageAPI _manageApi = manageApi;
    private readonly IDbContextFactory<ManageDbContext> _contextFactory = contextFactory;
    private readonly ISalesInvoiceService _salesInvoiceService = salesInvoiceService;
    private readonly IClinicService _clinicService = clinicService;
    private readonly IPatientService _patientService = patientService;
    private readonly IProductService _productService = productService;

    protected override async Task<EventProcessingStatus> OnAfterDeserialize(DeliveryNoteCreatedEvent? eventData)
    {
        LogMethodStart();

        ArgumentNullException.ThrowIfNull(eventData);

        var dbContext = await _contextFactory.CreateDbContextAsync();
        var newSalesEntity = new SalesEntity
        {
            Id = Guid.NewGuid(),
            SaleId = eventData.SaleId
        };

        try
        {
            dbContext.SalesEntity.Add(newSalesEntity);
            await dbContext.SaveChangesAsync();
            return EventProcessingStatus.New;
        }
        catch(DbUpdateException ex) when (DbErrors.IsUniqueConstraintViolation(ex))
        {
            LogCustomInformation("SalesEntity with the same SaleId already exists.  Skip processing");
            return EventProcessingStatus.Skipped;
        }
        catch(Exception ex)
        {
            LogCustomError(ex);
            return EventProcessingStatus.SoftFail;
        }
    }

    protected  async Task<SalesInvoice?> GetFromApiAsync(InvoiceEventHubEvent? eventData)
    {
        LogMethodStart();

        if (eventData == null)
        {
            LogCustomWarning("Event data is null.");
            return default;
        }

        AuSaleResponse? manageSale;
        ICollection<AuSaleProductResponse>? manageProducts;
        ICollection<AuSaleInvoiceResponse>? manageInvoices;
        try
        {
            var manageSaleTask = _manageApi.SalesAsync(eventData.SaleId);
            var manageProductsTask = _manageApi.ProductsAllAsync(eventData.SaleId);
            var manageInvoicesTask = _manageApi.InvoicesAsync(eventData.SaleId);
            await Task.WhenAll(manageProductsTask, manageInvoicesTask);

            manageSale = manageSaleTask.Result;
            manageProducts = manageProductsTask.Result;
            manageInvoices = manageInvoicesTask.Result;

            ArgumentNullException.ThrowIfNull(manageSale);
            ArgumentNullException.ThrowIfNull(manageProducts);
            ArgumentNullException.ThrowIfNull(manageInvoices);
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return default;
        }

        var salesInvoice = MapApiDataToSalesInvoice(manageProducts, manageInvoices);
        return salesInvoice;
    }
    
    private SalesInvoice? MapApiDataToSalesInvoice(
        ICollection<AuSaleProductResponse> manageProducts,
        ICollection<AuSaleInvoiceResponse> manageInvoices)
    {
        var primaryInvoice = manageInvoices.Where(x => x.PayerType == "Patient").FirstOrDefault() ?? manageInvoices.FirstOrDefault();
        if (primaryInvoice == null)
        {
            LogCustomWarning("No primary invoice found for the sale.");
            return null;
        }

        var salesInvoice = new SalesInvoice
        {
            Id = Guid.NewGuid(),
            ExternalSystemCode = _appSettings.AppName,
            DocumentNumber = primaryInvoice.Number,
            ExternalReference = primaryInvoice.Id.ToString(),
            AlternateNumber = primaryInvoice.Number,
            Patient = new ExternalReference
            {
                ExternalCode = primaryInvoice.PatientId.ToString()
            },
            Clinic = new ExternalReference
            {
                ExternalCode = primaryInvoice.LocationId.ToString()
            }
        };
        salesInvoice.DocumentDate = TimeZoneHelper.ConvertToTimeZone(
            primaryInvoice.CreatedOn.UtcDateTime,
            _appSettings.LocalTimeZone).Date;

        int sequence = 0;
        foreach (var item in manageProducts)
        {
            sequence++;
            var salesLine = new SalesInvoiceLine
            {
                Sequence = sequence,
                ExternalReference = item.Id.ToString(),
                Product = new ExternalReference
                {
                    ExternalCode = item.ProductId.ToString()
                },
                Description = item.ProductName,
                Quantity = item.Quantity,
                SerialNumber = item.SerialNumber,
                UnitPrice = (decimal)item.UnitPrice,
                DiscountAmount = (decimal)item.DiscountAmount,
                TaxAmount = (decimal)item.Taxes.Sum(t => t.Amount)
            };
            salesLine.GrossAmount = salesLine.UnitPrice * salesLine.Quantity;
            salesLine.AmountExclTax = salesLine.GrossAmount - salesLine.DiscountAmount;
            salesLine.AmountInclTax = salesLine.AmountExclTax + salesLine.TaxAmount;
            salesInvoice.Lines.Add(salesLine);
        }
        return salesInvoice;
    }

    protected override async Task<SalesInvoice?> GetFromRepositoryAsync(SalesInvoice modelData)
    {
        LogMethodStart();
        try
        {
            var existingDomainEntity = await _salesInvoiceService.GetAsync(
                externalSystemCode: _appSettings.AppName,
                externalReference: modelData.ExternalReference);
            return existingDomainEntity;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<SalesInvoice> OnAfterGetModelDataFromRepositoryAsync(
        SalesInvoice? existingModelData,
        SalesInvoice newModelData)
    {
        LogMethodStart();

        if (existingModelData != null)
        {
            newModelData.Patient = existingModelData.Patient;
            newModelData.Clinic = existingModelData.Clinic;

            foreach (var line in newModelData.Lines)
            {
                var existingLine = existingModelData.Lines
                    .Where(x => x.ExternalReference == line.ExternalReference)
                    .FirstOrDefault();

                if (existingLine != null)
                {
                    line.Sequence = existingLine.Sequence;
                    line.Product = existingLine.Product;
                }
            }
        }

        return await Task.FromResult(newModelData);
    }

    protected override async Task<SalesInvoice?> ValidateExternalReferencesAsync(SalesInvoice modelData)
    {
        LogMethodStart();

        if (modelData.Clinic != null)
        {
            var clinic = await _clinicService.ValidateExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                modelData.Clinic);

            if (clinic != null)
            {
                modelData.Clinic = clinic;
            }

            if (clinic == null)
            {
                LogCustomError($"Clinic ExternalCode = '{modelData.Clinic.ExternalCode}' does not exist.");
            }
        }

        if (modelData.Patient != null)
        {
            var patient = await _patientService.ValidateExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                modelData.Patient);

            if (patient != null)
            {
                modelData.Patient = patient;
            }

            if (patient == null)
            {
                LogCustomError($"Patient ExternalCode = '{modelData.Patient.ExternalCode}' does not exist.");
            }
        }

        foreach (var line in modelData.Lines)
        {
            if (line.Product != null)
            {
                var product = await _productService.ValidateExternalReferenceAsync(
                    _appSettings.ExternalSystemCode,
                    line.Product);

                if (product != null)
                {
                    line.Product = product;
                }

                if (product == null)
                {
                    LogCustomError($"Product ExternalCode = '{line.Product.ExternalCode}' does not exist.");
                }
            }
        }

        return modelData;

    }

    protected override async Task<SalesInvoice?> VerifyRequiredFieldsAsync(SalesInvoice modelData)
    {
        LogMethodStart();

        try
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.DocumentNumber, nameof(modelData.DocumentNumber));
            ArgumentNullException.ThrowIfNull(modelData.DocumentDate, nameof(modelData.DocumentDate));
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Clinic?.Code, $"{nameof(modelData.Clinic)}.{nameof(modelData.Clinic.Code)}");
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Patient?.Code, $"{nameof(modelData.Patient)}.{nameof(modelData.Patient.Code)}");
            foreach (var line in modelData.Lines)
            {
                ArgumentNullException.ThrowIfNull(line.Sequence, nameof(line.Sequence));
                ArgumentException.ThrowIfNullOrWhiteSpace(line.Product?.Code, $"{nameof(line.Product)}.{nameof(line.Product.Code)}");
                ArgumentNullException.ThrowIfNull(line.Quantity, nameof(line.Quantity));
            }

            return await Task.FromResult(modelData);
        }
        catch (Exception ex)
        {
            LogCustomError(ex.Message);
            return null;
        }
    }

    protected override async Task<SalesInvoice?> UpsertAsync(SalesInvoice modelData)
    {
        LogMethodStart();

        try
        {
            var salesInvoice = await _salesInvoiceService.UpsertAsync(modelData);
            if (salesInvoice == null)
            {
                LogCustomError($"Failed to upsert SalesInvoice {modelData.DocumentNumber}");
                return null;
            }
            else
            {
                return salesInvoice;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<InvoiceEventEntity?> LogMessageMetadataAsync(EventHubMessage message)
    {
        LogMethodStart();

        var msg = JsonSerializer.Serialize(message.Message, Common.GetJsonOptions());
        var invoiceEvent = JsonSerializer.Deserialize<InvoiceEventHubEvent>(msg!, Common.GetJsonOptions());
        if (invoiceEvent == null)
        {
            LogCustomWarning("InvoiceEventHubEvent deserialization failed.");
            return null;
        }

        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.InvoiceEventEntity.Where(x => x.MessageId == message.MessageId).FirstOrDefaultAsync();
        if (eventEntity != null)
        {
            bool isDirty = false;

            if (eventEntity.SaleId != invoiceEvent.SaleId)
            {
                eventEntity.SaleId = invoiceEvent.SaleId;
                isDirty = true;
            }

            if (eventEntity.ExternalNumber != invoiceEvent.Number)
            {
                eventEntity.ExternalNumber = invoiceEvent.Number;
                isDirty = true;
            }

            if (eventEntity.PatientId != invoiceEvent.PatientId)
            {
                eventEntity.PatientId = invoiceEvent.PatientId;
                isDirty = true;
            }

            if (eventEntity.FunderId != invoiceEvent.FunderId)
            {
                eventEntity.FunderId = invoiceEvent.FunderId;
                isDirty = true;
            }

            if (eventEntity.Timestamp != message.Timestamp)
            {
                eventEntity.Timestamp = message.Timestamp;
                isDirty = true;
            }

            var timeZoneId = _appSettings.LocalTimeZone;
            var localTimestamp = TimeZoneHelper.ConvertToTimeZone(message.Timestamp, timeZoneId);
            if (eventEntity.LocalTimestamp != localTimestamp)
            {
                eventEntity.LocalTimestamp = localTimestamp;
                isDirty = true;
            }

            if (!string.IsNullOrWhiteSpace(message.BlobPath) && eventEntity.BlobPath != message.BlobPath)
            {
                eventEntity.BlobPath = message.BlobPath;
                isDirty = true;
            }

            if (isDirty)
            {
                eventEntity.ModifiedOn = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();
                return eventEntity;
            }
        }
        else
        {
            eventEntity = new InvoiceEventEntity
            {
                Id = Guid.NewGuid(),
                EventType = message.Event,
                MessageId = message.MessageId,
                SaleId = invoiceEvent.SaleId,
                ExternalNumber = invoiceEvent.Number,
                PatientId = invoiceEvent.PatientId,
                FunderId = invoiceEvent.FunderId,
                Timestamp = message.Timestamp,
                LocalTimestamp = message.Timestamp,
                BlobPath = message.BlobPath ?? string.Empty,
                Status = EventProcessingStatus.New
            };
            dbContext.InvoiceEventEntity.Add(eventEntity);
            await dbContext.SaveChangesAsync();
            return eventEntity;
        }
        return null;
    }

    protected override async Task<InvoiceEventEntity?> UpdateMessageLogStatusAsync(InvoiceEventEntity log, EventProcessingStatus status)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.InvoiceEventEntity
            .FirstOrDefaultAsync(x => x.Id == log.Id);
        if (eventEntity != null)
        {
            eventEntity.Status = status;
            eventEntity.ProcessedOn = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
            return eventEntity;
        }

        return default;
    }

    protected override async Task<InvoiceEventEntity?> GetMetadataByMessageId(Guid messageId)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.InvoiceEventEntity
            .FirstOrDefaultAsync(x => x.Id == messageId);
        if (eventEntity != null)
        {
            return eventEntity;
        }

        return default;
    }


    [Function("RequeueInvoiceEvent")]
    public async Task<IActionResult> RequeueInvoiceEvent([HttpTrigger(AuthorizationLevel.Function, "post", Route = "requeueInvoiceEvent/{id}")] HttpRequest req,
        Guid id)
    {
        LogMethodStart();

        if (id == Guid.Empty)
        {
            LogCustomError("Invalid ID provided for requeueing order event.");
            return new BadRequestObjectResult("Invalid ID provided.");
        }

        try
        {
            await RetryFromLogAsync(id);
            return new OkObjectResult($"Order event with ID {id} has been requeued successfully.");
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return new BadRequestObjectResult($"Failed to requeue order event: {ex.Message}");
        }
    }
}