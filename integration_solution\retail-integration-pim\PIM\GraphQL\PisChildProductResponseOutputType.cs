﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisChildProductResponseOutputType
{
    [JsonPropertyName("createdAt")] public DateTime CreatedAt { get; set; }
    [JsonPropertyName("defaultChild")] public bool DefaultChild { get; set; }
    [JsonPropertyName("isAvailable")] public bool IsAvailable { get; set; }
    [JsonPropertyName("isPhasedOut")] public bool IsPhasedOut { get; set; }
    [JsonPropertyName("ranking")] public int Ranking { get; set; }
    [JsonPropertyName("sku")] public string? Sku { get; set; }
    [JsonPropertyName("state")] public StateOutputType? State { get; set; }
    [JsonPropertyName("updatedAt")] public DateTime UpdatedAt { get; set; }
}