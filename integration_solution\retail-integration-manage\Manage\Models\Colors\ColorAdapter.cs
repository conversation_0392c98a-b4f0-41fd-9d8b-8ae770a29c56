﻿using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Colors;

namespace WSA.Retail.Integration.Manage.Models.Colors;

public class ColorAdapter : 
    IEventHubEntityAdapter<ColorEventHubEvent, Color>,
    IManageRequestAdapter<DictionaryRequest, Color>,
    IManageResponseAdapter<DictionaryResponse, Color>
{
    public Color ToDomainModel(
        ColorEventHubEvent source,
        string externalSystemCode)
    {
        return new Color()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id.ToString(),
            Code = source.Name ?? "",
            Name = source.Name
        };
    }

    public DictionaryRequest ToManageRequest(Color color)
    {
        return new DictionaryRequest
        {
            Name = color.Name,
            IsActive = true
        };
    }

    public Color? ToDomainModel(
        DictionaryResponse? response,
        string externalSystemCode)
    {
        if (response == null) return null;

        return new Color()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = response.Id.ToString(),
            Name = response.Name,
            Code = response.Name ?? "UNSPECIFIED"
        };
    }
}