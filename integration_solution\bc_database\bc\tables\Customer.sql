﻿CREATE TABLE [bc].[Customer] (
    [Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [CompanyId] UNIQUEIDENTIFIER,
    [ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
    [ExternalId] UNIQUEIDENTIFIER NOT NULL,
    [CustomerNumber] NVARCHAR(20),
    [Name] NVARCHAR(100),
    [Name2] NVARCHAR(100),
    [Address] NVARCHAR(100),
    [Address2] NVARCHAR(100),
    [City] NVARCHAR(50),
    [PostCode] NVARCHAR(20),
    [Region] NVARCHAR(30),
    [CountryRegionCode] NVARCHAR(10),
    [PhoneNumber] NVARCHAR(30),
    [Email] NVARCHAR(80),
    [CustomerType] NVARCHAR(20),
    [Blocked] NVARCHAR(20),
    [Balance] DECIMAL(18,2),
    [ExternalCreatedOn] DATETIME2(7),
    [ExternalModifiedOn] DATETIME2(7),
    [CreatedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
    [ModifiedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),

    CONSTRAINT [PK_Customer_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
);
GO

CREATE UNIQUE INDEX [IX_Customer_CompanyId_ExternalId]
ON [bc].[Customer] ([CompanyId], [ExternalId]);
