namespace WSA.Integration;

table 50101 "WSA Integration Request Log"
{
    Caption = 'WSA Integration Request Log';
    DataClassification = SystemMetadata;

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
        }

        field(2; Status; Enum "WSA Request Status")
        {
            Caption = 'Status';
        }

        field(3; "Type"; Enum "WSA Request Type")
        {
            Caption = 'Type';
        }

        field(4; Method; Enum "WSA Request Method")
        {
            Caption = 'Method';
        }

        field(5; "Request Content"; Blob)
        {
            Caption = 'Request';
            Subtype = Json;
        }

        field(6; "Response Content"; Blob)
        {
            Caption = 'Response';
            Subtype = Json;
        }

        field(9; Notes; Text[1024])
        {
            Caption = 'Notes';
        }

        field(100; "Clinic No."; Code[10])
        {
            Caption = 'Clinic No.';
        }

        field(101; "Patient No."; Code[20])
        {
            Caption = 'Patient No.';
        }

        field(103; "Product No."; Code[20])
        {
            Caption = 'Product No.';
        }

        field(104; "Payor No."; Code[20])
        {
            Caption = 'Payor No.';
        }

        field(105; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
        }

        field(106; "Color Code"; Code[20])
        {
            Caption = 'Color Code';
        }

        field(107; "Battery Code"; Code[20])
        {
            Caption = 'Battery Code';
        }

        field(108; "Manufacturer Code"; Code[20])
        {
            Caption = 'Manufacturer Code';
        }

        field(109; "Product Model Code"; Code[20])
        {
            Caption = 'Product Model Code';
        }

        field(110; "Purchase Order No."; Code[20])
        {
            Caption = 'Purchase Order No.';
        }

        field(111; "Purchase Receipt No."; Code[20])
        {
            Caption = 'Purchase Receipt No.';
        }

        field(120; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
        }

        field(121; "Sales Invoice No."; Code[20])
        {
            Caption = 'Sales Invoice No.';
        }

        field(122; "Sales Credit Memo No."; Code[20])
        {
            Caption = 'Sales Credit Memo No.';
        }

        field(131; "Claim No."; Code[20])
        {
            Caption = 'Claim No.';
        }

        field(201; "Document No."; Code[20])
        {
            Caption = 'Document No.';
        }

        field(202; "Document Date"; Date)
        {
            Caption = 'Document Date.';
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }

        key(status; Status)
        {
        }
    }

    trigger OnInsert()
    begin
    end;


    procedure ProcessAllHeaders()
    var
        Request: Record "WSA Integration Request Log";
        Request2: Record "WSA Integration Request Log";
        Handler: Interface "WSA Integration Request";
        Window: Dialog;
        DialogMsg: Label 'Requests to process:  #1####\\Current Request:  #2########\', Comment = '#1#### = Request Count, #2######## = Current Request No.';
        TotalRecords: Integer;
        CurrentRecordCount: Integer;
        Progress: Decimal;
        CurrentRecord: Text;
        ProgressLbl: Label '%1 %2 (%3%)', Comment = '%1 = interface, %2 = key, %3 = progress';

    begin
        Request.SetCurrentKey(status);
        Request.CopyFilters(Rec);
        Request.SetFilter(status, '%1', Request.status::ready);
        if Request.FindSet() then
            if GuiAllowed() then begin
                TotalRecords := Request.Count();
                Window.Open(DialogMsg, TotalRecords, CurrentRecord, Progress);
            end;

        repeat
            if GuiAllowed() then begin
                CurrentRecordCount += 1;
                Progress := Round((CurrentRecordCount / TotalRecords) * 100, 0.1);
                CurrentRecord := StrSubstNo(ProgressLbl, Request.Type, Request."Entry No.", Progress);
                Window.Update(2, CurrentRecord);
            end;

            Request2.Get(Request."Entry No.");
            Handler := Request2."Type";
            Handler.HandleRequest(Request2);
        until Request.Next() = 0;

        if GuiAllowed() then
            Window.Close();
    end;
}
