using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Categories
{
    public class CategoryEntityConfiguration : IEntityTypeConfiguration<CategoryEntity>
    {
        public void Configure(EntityTypeBuilder<CategoryEntity> builder)
        {
            builder.ToTable("ProductCategory", "dbo");

            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(c => c.ParentId)
                .HasColumnName("ParentId")
                .HasColumnType("uniqueidentifier");

            builder.Property(c => c.IsHearingAid)
                .HasColumnName("IsHearingAid")
                .HasColumnType("bit");

            builder.Property(c => c.IsInventory)
                .HasColumnName("IsInventory")
                .HasColumnType("bit");

            builder.Property(c => c.IsSerialized)
                .HasColumnName("IsSerialized")
                .HasColumnType("bit");

            builder.HasOne(c => c.ParentEntity)
                .WithMany()
                .HasForeignKey(p => p.ParentId);
        }
    }
}