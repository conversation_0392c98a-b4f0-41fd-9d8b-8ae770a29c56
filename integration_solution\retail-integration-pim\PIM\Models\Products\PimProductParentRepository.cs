﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.Products;

public class PimParentProductRepository(
    ILogger<PimParentProductRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
        PimProductRelatedEntityRepositoryBase<PimProductParentUpsertTableType>(
            logger,
            dbContextFactory),
        IPimParentProductRepository
{
    protected override string StoredProcedureName => "pim.ProductParentUpsert";
    protected override string TableTypeName => "pim.ProductParentUpsertTableType";

    protected override List<PimProductParentUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .Where(p => p.ParentProductSku != null)
            .SelectMany(p => p.ParentProductSku
            .Select(s => new PimProductParentUpsertTableType
            {
                ProductId = p.ProductId,
                ParentSku = s
            }))
            .ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("ProductId", typeof(Guid));
        dataTable.Columns.Add("ParentSku", typeof(string));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimProductParentUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["ProductId"] = entity.ProductId;
            dataRow["ParentSku"] = entity.ParentSku;
        }
        return dataRow;
    }
}