﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoiceRepository(
    ILogger<SalesInvoiceRepository> logger,
    IDbContextFactory<IntegrationContext> dbContextFactory,
    IDocumentQueryBuilder<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity> queryBuilder,
    IDocumentMapper<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity> mapper)
    : DocumentRepositoryBase<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>(
        logger: logger,
        dbContextFactory: dbContextFactory,
        queryBuilder: queryBuilder,
        mapper: mapper),
    ISalesInvoiceRepository
{
}