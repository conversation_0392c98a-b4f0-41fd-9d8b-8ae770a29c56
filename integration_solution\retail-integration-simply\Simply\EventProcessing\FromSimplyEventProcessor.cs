﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Models.Patients;
using WSA.Retail.Integration.Simply.Models.Payments;
using WSA.Retail.Integration.Simply.Models.PurchaseOrders;
using WSA.Retail.Integration.Simply.Models.PurchaseReceipts;
using WSA.Retail.Integration.Simply.Models.SalesInvoices;

namespace WSA.Retail.Integration.Simply.EventProcessing
{
    public class FromSimplyEventProcessor(
        IOptions<AppSettings> appSettings,
        ILogger<FromSimplyEventProcessor> logger,
        ISimplyPatientProcessor simplyPatientProcessor,
        ISimplyPurchaseOrderProcessor simplyPurchaseOrderProcessor,
        ISimplyPurchaseReceiptProcessor simplyPurchaseReceiptProcessor,
        ISimplySalesProcessor simplySalesProcessor,
        ISimplyCashReceiptsProcessor simplyCashReceiptsProcessor)
    {
        private readonly AppSettings _appSettings = appSettings.Value;
        private readonly ILogger<FromSimplyEventProcessor> _logger = logger;
        private readonly ISimplyPatientProcessor _simplyPatientProcessor = simplyPatientProcessor;
        private readonly ISimplyPurchaseOrderProcessor _simplyPurchaseOrderProcessor = simplyPurchaseOrderProcessor;
        private readonly ISimplyPurchaseReceiptProcessor _simplyPurchaseReceiptProcessor = simplyPurchaseReceiptProcessor;
        private readonly ISimplySalesProcessor _simplySalesProcessor = simplySalesProcessor;
        private readonly ISimplyCashReceiptsProcessor _simplyCashReceiptsProcessor = simplyCashReceiptsProcessor;

        [Function("FromSimplyTimerEventAsync")]
        public async Task FromSimplyTimerEventAsync([TimerTrigger("%Schedule1%", RunOnStartup = true)] TimerInfo myTimer)
        {
            _logger.LogMethodStart();

            try
            {
                if (myTimer.ScheduleStatus is not null)
                {
                    await MasterDataPostAsync();
                    await TransactionPostAsync();

                    _logger.LogCustomInformation(
                        "Successfully completed processing data from Simply");
                    _logger.LogCustomInformation(
                        $"Next scheduled execution at: {myTimer.ScheduleStatus.Next}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogCustomError(ex, "Error occurred while processing data from Simply");
                throw; // Rethrow to let Azure Functions handle failure
            }
        }

        public async Task MasterDataPostAsync()
        {
            _logger.LogMethodStart();
            await _simplyPatientProcessor.ProcessSimplyPatientsAsyc();
        }

        public async Task TransactionPostAsync()
        {
            _logger.LogMethodStart();

            // Process purchase orders
            await _simplyPurchaseOrderProcessor.ProcessSimplyPurchaseTransactionsAsync();

            // Process purchase receipts
            await _simplyPurchaseReceiptProcessor.ProcessSimplyPurchaseTransactionsAsync();

            await _simplySalesProcessor.ProcessSimplySalesTransactionsAsync();

            //SalesCredit.ProcessNewRecords();
            
            await _simplyCashReceiptsProcessor.ProcessSimplyCashReceiptsTransactionsAsync();
        }
    }
}
