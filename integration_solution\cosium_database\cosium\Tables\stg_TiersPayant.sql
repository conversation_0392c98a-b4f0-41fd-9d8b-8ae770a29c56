﻿CREATE TABLE [cosium].[stg_TiersPayant](
	[id] [nvarchar](50) NOT NULL,
	[tauxss] [nvarchar](max) NULL,
	[montantsttc] [nvarchar](max) NULL,
	[montantslpp] [nvarchar](max) NULL,
	[montantsrc] [nvarchar](max) NULL,
	[montantsrc2] [nvarchar](max) NULL,
	[montantsrc3] [nvarchar](max) NULL,
	[montantsro] [nvarchar](max) NULL,
	[haselement] [nvarchar](max) NULL,
	[numeroslpp] [nvarchar](max) NULL,
	[montantpartrc] [nvarchar](max) NULL,
	[refmutuelles] [nvarchar](max) NULL,
	[montantsmutuelles] [nvarchar](max) NULL,
	[reffacture] [nvarchar](max) NULL,
	[optionstp] [nvarchar](max) NULL,
	[natureassurance] [nvarchar](max) NULL,
	[numprisecharge] [nvarchar](max) NULL,
	[dateprisecharge] [nvarchar](max) NULL,
	[dateexport] [nvarchar](max) NULL,
	[dateaccident] [nvarchar](max) NULL,
	[numeroaccident] [nvarchar](max) NULL,
	[datemodif] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO