using WSA.Retail.Integration.Models.Payments;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Simply.Models.Payments;

public static class SimplyPaymentExtensions
{
    public static Payment ToPayment(this SimplyPaymentEntity entity)
    {
        var payment = new Payment
        {
            ExternalReference = entity.DocumentNumber.ToString(),
            DocumentDate = entity.DocumentDate,
            Amount = entity.Amount
        };
        
        // Add patient reference if available
        if (!string.IsNullOrEmpty(entity.PatientNumber))
        {
            payment.Patient = new ExternalReference
            {
                Code = entity.PatientNumber.Trim()
            };
        }

        // Add payor reference if available
        if (!string.IsNullOrEmpty(entity.PayerNumber))
        {
            payment.Payor = new ExternalReference
            {
                Code = entity.PayerNumber.Trim()
            };
        }

        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.ClinicCode))
        {
            payment.Clinic = new ExternalReference
            {
                Code = entity.ClinicCode.Trim()
            };
        }

        payment.PaymentMethod = entity.PaymentMethod?.ToUpper().Trim() ?? "";
        payment.PaymentMethod = payment.PaymentMethod[..Math.Min(payment.PaymentMethod.Length, 10)];
        
        return payment;
    }

    public static Payment ToPayment(this SimplyCashReceiptsEntity entity)
    {
        var payment = new Payment
        {
            ExternalReference = entity.Id.ToString(),
            DocumentDate = entity.PaymentDate,
            //Description = entity.Description?.Trim(),
            Amount = entity.Amount,
            ReferenceNumber = entity.DocumentNumber?.Trim(),
            AppliesTo = entity.AppliesToDocNumber?.Trim()
        };

        // Add patient reference if available
        if (!string.IsNullOrEmpty(entity.BalanceAccountNumber))
        {
            payment.Patient = new ExternalReference
            {
                ExternalCode = entity.BalanceAccountNumber.Trim()
            };
        }

        // Add payor reference if available
        if (!string.IsNullOrEmpty(entity.BalanceAccountNumber))
        {
            payment.Payor = new ExternalReference
            {
                ExternalCode = entity.BalanceAccountNumber.Trim()
            };
        }

        // Add clinic reference if available
        if (!string.IsNullOrEmpty(entity.ClinicCode))
        {
            payment.Clinic = new ExternalReference
            {
                Code = entity.ClinicCode.Trim()
            };
        }

        payment.PaymentMethod = entity.PaymentMethodCode?.ToUpper().Trim() ?? "";
        payment.PaymentMethod = payment.PaymentMethod[..Math.Min(payment.PaymentMethod.Length, 10)];

        return payment;
    }
}