﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class InvoiceEventHubEvent : IIdentifiable
{
    public Guid Id { get; set; }
    public required string Number { get; set; }
    public Guid PatientId { get; set; }
    public Guid SaleId { get; set; }
    public Guid LocationId { get; set; }
    public decimal Amount { get; set; }
    public Guid? FunderId { get; set; }
    public decimal OutstandingPayment { get; set; }
    public string? PayerName { get; set; }
    public List<InvoiceProductEventHubEvent>? Products { get; set; }
}
