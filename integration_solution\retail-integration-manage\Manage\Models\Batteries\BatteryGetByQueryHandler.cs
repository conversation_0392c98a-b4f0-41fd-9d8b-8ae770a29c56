﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Batteries;

namespace WSA.Retail.Integration.Manage.Models.Batteries;

public class BatteryGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IBatteryService entityService)
    : BaseApiGetByQuery<
        Battery,
        IBatteryService>(appSettings, entityService)
{
}