namespace WSA.Integration;

using Microsoft.Sales.Customer;
using WSA.Integration.Customer;

codeunit 50137 "Payor Events"
{
    [EventSubscriber(ObjectType::Table, Database::Customer, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Customer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if not (Rec."WSA Customer Type" = "WSA Customer Type"::Payor) then
            exit;

        if RunTrigger then
            SendPayorToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Customer, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Customer;
        var xRec: Record Customer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if not (Rec."WSA Customer Type" = "WSA Customer Type"::Payor) then
            exit;

        if RunTrigger then
            SendPayorToIntegrationAPI(Rec);
    end;


    local procedure SendPayorToIntegrationAPI(Customer: Record Customer)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Customer."No." = '' then
            exit;

        if Customer.Name = '' then
            exit;

        if IsNullGuid(Customer.SystemId) then
            exit;

        if not (Customer."WSA Customer Type" = "WSA Customer Type"::Payor) then
            exit;

        JObject := Common.PayorToJson(Customer);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'payors', Format(Customer.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}
