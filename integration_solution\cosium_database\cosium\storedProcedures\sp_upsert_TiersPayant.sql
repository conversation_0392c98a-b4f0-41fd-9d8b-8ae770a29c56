﻿CREATE   PROCEDURE [cosium].[sp_upsert_TiersPayant]
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Step 1: Prepare clean data with deduplication
        WITH CleanData AS (
            SELECT 
                id,
                tauxss,
                montantsttc,
                montantslpp,
                montantsrc,
                montantsrc2,
                montantsrc3,
                montantsro,
                haselement,
                numeroslpp,
                montantpartrc,
                refmutuelles,
                montantsmutuelles,
                reffacture,
                optionstp,
                natureassurance,
                numprisecharge,
                dateprisecharge,
                dateexport,
                dateaccident,
                numeroaccident,
                datemodif,
                ROW_NUMBER() OVER (PARTITION BY id ORDER BY 
                    CASE WHEN datemodif IS NULL OR datemodif = '' THEN '1900-01-01' 
                    ELSE datemodif END DESC) AS rn
            FROM [cosium].[stg_TiersPayant]
        )
        
        -- Step 2: Perform high-performance MERGE
        MERGE [cosium].[TiersPayant] AS target
        USING (
            SELECT * FROM CleanData WHERE rn = 1
        ) AS source
        ON target.id = source.id
        
        WHEN MATCHED AND (
            ISNULL(target.tauxss, '') <> ISNULL(source.tauxss, '') OR
            ISNULL(target.montantsttc, '') <> ISNULL(source.montantsttc, '') OR
            ISNULL(target.montantslpp, '') <> ISNULL(source.montantslpp, '') OR
            ISNULL(target.montantsrc, '') <> ISNULL(source.montantsrc, '') OR
            ISNULL(target.montantsrc2, '') <> ISNULL(source.montantsrc2, '') OR
            ISNULL(target.montantsrc3, '') <> ISNULL(source.montantsrc3, '') OR
            ISNULL(target.montantsro, '') <> ISNULL(source.montantsro, '') OR
            ISNULL(target.haselement, '') <> ISNULL(source.haselement, '') OR
            ISNULL(target.numeroslpp, '') <> ISNULL(source.numeroslpp, '') OR
            ISNULL(target.montantpartrc, '') <> ISNULL(source.montantpartrc, '') OR
            ISNULL(target.refmutuelles, '') <> ISNULL(source.refmutuelles, '') OR
            ISNULL(target.montantsmutuelles, '') <> ISNULL(source.montantsmutuelles, '') OR
            ISNULL(target.reffacture, '') <> ISNULL(source.reffacture, '') OR
            ISNULL(target.optionstp, '') <> ISNULL(source.optionstp, '') OR
            ISNULL(target.natureassurance, '') <> ISNULL(source.natureassurance, '') OR
            ISNULL(target.numprisecharge, '') <> ISNULL(source.numprisecharge, '') OR
            ISNULL(target.dateprisecharge, '') <> ISNULL(source.dateprisecharge, '') OR
            ISNULL(target.dateexport, '') <> ISNULL(source.dateexport, '') OR
            ISNULL(target.dateaccident, '') <> ISNULL(source.dateaccident, '') OR
            ISNULL(target.numeroaccident, '') <> ISNULL(source.numeroaccident, '') OR
            ISNULL(target.datemodif, '') <> ISNULL(source.datemodif, '')
        )
        THEN UPDATE SET
            target.tauxss = source.tauxss,
            target.montantsttc = source.montantsttc,
            target.montantslpp = source.montantslpp,
            target.montantsrc = source.montantsrc,
            target.montantsrc2 = source.montantsrc2,
            target.montantsrc3 = source.montantsrc3,
            target.montantsro = source.montantsro,
            target.haselement = source.haselement,
            target.numeroslpp = source.numeroslpp,
            target.montantpartrc = source.montantpartrc,
            target.refmutuelles = source.refmutuelles,
            target.montantsmutuelles = source.montantsmutuelles,
            target.reffacture = source.reffacture,
            target.optionstp = source.optionstp,
            target.natureassurance = source.natureassurance,
            target.numprisecharge = source.numprisecharge,
            target.dateprisecharge = source.dateprisecharge,
            target.dateexport = source.dateexport,
            target.dateaccident = source.dateaccident,
            target.numeroaccident = source.numeroaccident,
            target.datemodif = source.datemodif
            
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (
                id, tauxss, montantsttc, montantslpp, montantsrc, montantsrc2,
                montantsrc3, montantsro, haselement, numeroslpp, montantpartrc,
                refmutuelles, montantsmutuelles, reffacture, optionstp, natureassurance,
                numprisecharge, dateprisecharge, dateexport, dateaccident, numeroaccident,
                datemodif
            )
            VALUES (
                source.id, source.tauxss, source.montantsttc, source.montantslpp, 
                source.montantsrc, source.montantsrc2, source.montantsrc3, 
                source.montantsro, source.haselement, source.numeroslpp, 
                source.montantpartrc, source.refmutuelles, source.montantsmutuelles, 
                source.reffacture, source.optionstp, source.natureassurance, 
                source.numprisecharge, source.dateprisecharge, source.dateexport, 
                source.dateaccident, source.numeroaccident, source.datemodif
            );
        
        -- Get operation statistics
        DECLARE @InsertCount INT = (SELECT COUNT(*) FROM [cosium].[stg_TiersPayant] s
                                   WHERE NOT EXISTS (
                                       SELECT 1 FROM [cosium].[TiersPayant] t 
                                       WHERE t.id = s.id
                                   ));
        DECLARE @UpdateCount INT = @@ROWCOUNT - @InsertCount;
        
        -- Clear staging table (optional)
        TRUNCATE TABLE [cosium].[stg_TiersPayant];
        
        COMMIT TRANSACTION;
        
        -- Return results
        SELECT 
            'Success' AS Status,
            @InsertCount AS RowsInserted,
            @UpdateCount AS RowsUpdated,
            GETDATE() AS ProcessedTime;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Return detailed error
        SELECT 
            'Error' AS Status,
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            ERROR_LINE() AS ErrorLine,
            GETDATE() AS ErrorTime;
    END CATCH
END
GO