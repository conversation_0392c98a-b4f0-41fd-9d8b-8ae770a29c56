﻿CREATE TABLE [bc].[PurchaseOrder_Staging](
	[Id] UNIQUEIDENTIFIER NOT NULL,
	[CompanyId] UNIQUEIDENTIFIER,
	[DocumentNumber] NVARCHAR(20),
	[DocumentDate] DATETIME2(7),
	[PostingDate] DATETIME2(7),
	[VendorNumber] NVARCHAR(20),
	[VendorId] UNIQUEIDENTIFIER,
	[BuyFromVendorNumber] NVARCHAR(20),
	[BuyFromVendorId] UNIQUEIDENTIFIER,
	[ResponsibilityCenter] NVARCHAR(20),
	[ResponsibilityCenterId] UNIQUEIDENTIFIER,
	[CurrencyCode] NVARCHAR(20),
	[CurrencyId] UNIQUEIDENTIFIER,
	[PaymentTermsCode] NVARCHAR(20),
	[PaymentTermsId] UNIQUEIDENTIFIER,
	[DimensionSetId] INT,
	[SystemCreatedAt] DATETIME2(7),
	[SystemModifiedAt] DATETIME2(7),
	[ETL_LoadId] NVARCHAR(40),
	[ETL_CreatedAt] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ETL_Status] NVARCHAR(40)
	)
GO

CREATE INDEX [IX_PurchaseOrder_ETLLoadId_CompanyId_Id]
ON [bc].[PurchaseOrder_Staging] ([ETL_LoadId], [CompanyId], [Id])
GO