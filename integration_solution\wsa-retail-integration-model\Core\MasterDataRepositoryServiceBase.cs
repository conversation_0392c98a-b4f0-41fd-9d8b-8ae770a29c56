﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Core;

public class MasterDataRepositoryServiceBase<
    TModel,
    TRecord>(
    AppSettings appSettings,
    ILogger logger,
    IDbContextFactory<IntegrationContext> dbContextFactory,
    IMasterDataQueryBuilder<TModel, TRecord> queryBuilder,
    IMasterDataMapper<TModel, TRecord> entityDbAdapter) : 
    LoggingBase<TRecord>(logger),
    IMasterDataRepositoryServiceBase<TModel, TRecord>
    where TModel : class, IMasterData
    where TRecord : class, IMasterDataEntity
{
    protected readonly AppSettings _appSettings = appSettings;
    protected readonly IDbContextFactory<IntegrationContext> _dbContextFactory = dbContextFactory;
    protected readonly IMasterDataQueryBuilder<TModel, TRecord> _queryBuilder = queryBuilder;
    protected readonly IMasterDataMapper<TModel, TRecord> _entityDbAdapter = entityDbAdapter;

    public async Task<TModel?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? code = null,
        string? name = null,
        string? externalCode = null)
    {
        LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        return await _queryBuilder
            .BuildQuery(
                context: context, 
                externalSystemCode: externalSystemCode, 
                id: id, 
                code: code, 
                name: name, 
                externalCode: externalCode)
            .FirstOrDefaultAsync();
    }

    public async Task<List<TModel>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? code = null,
        string? name = null,
        string? externalCode = null)
    {
        LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        return await _queryBuilder
            .BuildQuery(
                context: context, 
                externalSystemCode: externalSystemCode, 
                id: id, 
                code: code, 
                name: name, 
                externalCode: externalCode)
            .ToListAsync() ?? [];
    }

    public async Task<bool> InsertAsync(TModel domainEntity)
    {
        LogMethodStart();

        using var context = _dbContextFactory.CreateDbContext();
        var dbEntity = _entityDbAdapter.ToEntity(domainEntity);
        
        try
        {
            context.Set<TRecord>().Add(dbEntity);
            LogCustomInformation($"Before inserting {typeof(TRecord).Name} record.", dbEntity);
            await context.SaveChangesAsync();
            LogCustomInformation($"Successfully inserted {typeof(TRecord).Name} record.", dbEntity);
            domainEntity.Id = dbEntity.Id;
            return true;
        }
        catch (DbUpdateException ex)
        {
            LogCustomError($"Encountered a database error while inserting {typeof(TModel).Name} record: {ex.Message}", dbEntity);
            return false;
        }
        catch (Exception ex)
        {
            LogCustomError(ex, dbEntity);
            return false;
        }
    }

    public async Task<bool> UpdateAsync(TModel domainEntity)
    {
        LogMethodStart();
        var dbEntity = _entityDbAdapter.ToEntity(domainEntity);

        if (domainEntity.Id == Guid.Empty)
        {
            LogCustomWarning($"{typeof(TModel).Name} {domainEntity.Code} cannot be updated because it has a null or missing Id.", dbEntity);
            return false;
        }

        using var context = _dbContextFactory.CreateDbContext();

        // Retrieve the existing db entity
        var existingDbEntity = await context.Set<TRecord>().FindAsync(domainEntity.Id);
        if (existingDbEntity == null)
        {
            LogCustomWarning($"{typeof(TModel).Name} with Id {domainEntity.Id} not found to update.", dbEntity);
            return false;
        }

        // Update db entity fields
        context.Entry(existingDbEntity).CurrentValues.SetValues(dbEntity);
        try
        {
            LogCustomInformation($"Before updating {typeof(TRecord).Name} record.", dbEntity);
            await context.SaveChangesAsync();
            LogCustomInformation($"Successfully updated {typeof(TRecord).Name} record.", dbEntity);
            return true;
        }
        catch (DbUpdateException ex)
        {
            LogCustomError($"A database error while updating {typeof(TModel).Name} record: {ex.Message}", dbEntity);
            return false;
        }
        catch (Exception ex)
        {
            LogCustomError(ex, dbEntity);
            return false;
        }
    }
}
