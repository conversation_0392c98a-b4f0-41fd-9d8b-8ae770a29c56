using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.BundleSets
{
    public class PimBundleSetConfiguration : IEntityTypeConfiguration<PimBundleSetEntity>
    {
        public void Configure(EntityTypeBuilder<PimBundleSetEntity> builder)
        {
            builder.ToTable("BundleSet", "pim");

            builder.<PERSON><PERSON>ey(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.IsDefault)
                .HasColumnName("IsDefault")
                .HasColumnType("bit");

            builder.Property(x => x.IsMandatory)
                .HasColumnName("IsMandatory")
                .HasColumnType("bit");

            builder.Property(x => x.IsMasterData)
                .HasColumnName("IsMasterData")
                .HasColumnType("bit");

            builder.Property(x => x.Ranking)
                .HasColumnName("Ranking")
                .HasColumnType("int");

            builder.Property(x => x.State)
                .HasColumnName("State")
                .HasColumnType("nvarchar(20)");

            builder.Property(x => x.CreatedAt)
                .HasColumnName("CreatedAt")
                .HasColumnType("datetime2(7)");

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("UpdatedAt")
                .HasColumnType("datetime2(7)");
        }
    }
}