﻿<?xml version="1.0" encoding="utf-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 xliff-core-1.2-transitional.xsd">
  <file datatype="xml" source-language="en-US" target-language="en-US" original="Retail Integration Connector">
    <body>
      <group id="body">
        <trans-unit id="Page 805191900 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Property 3446740159" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property EntityCaption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Property 631549417" size-unit="char" translate="yes" xml:space="preserve">
          <source>Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property EntitySetCaption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2683860767 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Entry No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control entryNo - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2686962215 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Id</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control id - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 3743604651 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestBody - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 1009662778 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request Method</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestMethod - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2372889619 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request Type</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestType - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2862218949 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control responseBody - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 998393528 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Status</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control status - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 869913466 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Base URL</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field API Base URL - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 4253087169 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field API Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 2891538263 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Enabled</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Enabled - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 513753450 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Batch</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Item Adjustment Batch - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 3803952390 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Template</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Item Adjustment Template - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 2203546865 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal Batch</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Payment Journal Batch - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1308935589 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal Template</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Payment Journal Template - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1663593181 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Primary Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Primary Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>WSA Integration Request Log</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Method 3333449226 - NamedType 3606899323" size-unit="char" translate="yes" xml:space="preserve">
          <source>Requests to process:  #1####\\Current Request:  #2########\</source>
          <note from="Developer" annotates="general" priority="2">#1#### = Request Count, #2######## = Current Request No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Method ProcessAllHeaders - NamedType DialogMsg</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Method 3333449226 - NamedType 3950098385" size-unit="char" translate="yes" xml:space="preserve">
          <source>%1 %2 (%3%)</source>
          <note from="Developer" annotates="general" priority="2">%1 = interface, %2 = key, %3 = progress</note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Method ProcessAllHeaders - NamedType ProgressLbl</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 3064813023 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Clinic No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Clinic No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 883711285 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Entry No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Entry No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 4132733409 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Method</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Method - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 222620933 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Notes</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Notes - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Patient No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Patient No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 342365012 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Request Content - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Response Content - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 513339096 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Status</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Status - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 1878130204 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Type</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Type - Property Caption</note>
        </trans-unit>
        <trans-unit id="Codeunit 3533182055 - Method 1148061732 - NamedType 3231710450" size-unit="char" translate="yes" xml:space="preserve">
          <source>https://api.businesscentral.dynamics.com/v2.0/%1/%2/api</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Integration Management - Method GetBaseUrl - NamedType BaseUrlTxt</note>
        </trans-unit>
        <trans-unit id="Codeunit 3533182055 - Method 3379293773 - NamedType 3231710450" size-unit="char" translate="yes" xml:space="preserve">
          <source>https://businesscentral.dynamics.com/%1/companies(%2)</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Integration Management - Method GetExternalSystemCode - NamedType BaseUrlTxt</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2445482498 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>General</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control General - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 787935911 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Settings</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Settings - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 869913466 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The base URL of the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Base URL - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 4253087169 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The API key to authenticate with the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Key - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2891538263 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Enable or disable the integration with the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Enabled - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1074715950 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Journals - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3009232573 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Journal</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Item Adjustment Journal - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1513328805 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal batch to use for item adjustment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control AdjustmentBatch - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3091584881 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal template to use for item adjustment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control AdjustmentTemplate - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3196174509 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Payment Journal - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2866533842 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal batch to use for payment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control PaymentBatch - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2639201662 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal template to use for payment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control PaymentTemplate - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Integration Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 883711285 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Entry No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Entry No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 222620933 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Notes field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Notes - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 513339096 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Status field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Status - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 577209412 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedAt field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4000648066 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedBy field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4000648066 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Created By</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedBy - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 2453272523 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedAt field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4085837753 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedBy field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4085837753 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Modified By</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedBy - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 1878130204 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Type field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Type - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 3445273183 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Upload a new json file to the request content.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action AttachFile - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 3445273183 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Upload Request Content</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action AttachFile - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 1972153130 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>ToolTip: </source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processAllHeaders - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 1972153130 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process All Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processAllHeaders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 915599582 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processRequest - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 915599582 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processRequest - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 2553816663 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Set Status Ready</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action setStatusReady - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 2553816663 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Set Status Ready</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action setStatusReady - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturer Card</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2731911828 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Address &amp; Contact</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Address &amp; Contact - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2784646184 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Address</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control AddressDetails - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 3168444338 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the address of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Address - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2306852000 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies additional address information.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Address 2 - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 714791889 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the city of the address.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control City - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 284764054 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the country/region of the address.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Country/Region Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 3660300303 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the postal code.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Post Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2593889414 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Contact</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control ContactDetails - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 1541011875 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the email address of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control E-Mail - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 3986423421 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the phone number of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Phone No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 105213272 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Integration</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control IntegrationDetails - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 3993324108 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the external code of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control External Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2445482498 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>General</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control General - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 3004954119 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the code of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 132212754 - Control 2961552353 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the name of the manufacturer.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer Card - Control Name - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 5351188 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturer List</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Manufacturer List - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2827249175 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 2827249175">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Color - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1994964448 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1994964448">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Customer - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3276313895 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3276313895">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Item - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1487222247 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1487222247">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Item Category - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 3168444338 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Address</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field Address - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 2306852000 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Address 2</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field Address 2 - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 714791889 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>City</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field City - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 284764054 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Country/Region Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field Country/Region Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 499448942 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>County</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field County - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 1541011875 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Email</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field E-Mail - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 3986423421 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Phone No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field Phone No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3647128553 - Field 3660300303 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3647128553">
          <source>Post Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Manufacturer - Field Post Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2072992664 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 2072992664">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Purchase Header - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2247850105 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 2247850105">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Responsibility Center - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1192395785 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1192395785">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Sales Header - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1936001480 - Field 3993324108 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1936001480">
          <source>External Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Vendor - Field External Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1936001480 - Field 2419845103 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1936001480">
          <source>Integrate with POS</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Vendor - Field Integrate with POS - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2400746976 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Adjustment</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue adjustments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1291479322 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Category</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue categories - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3804383803 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Clinic</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue clinics - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3767720386 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Color</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue colors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2563799520 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturer</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue manufacturers - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 971209292 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Patient</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue patients - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 954921427 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue payments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 659163946 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payor</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue payors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Product</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue products - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3213736258 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Order</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseOrders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1165194168 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Receipt</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseReceipts - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1892365954 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Credit</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesCredits - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1312188414 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Invoice</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesInvoices - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1828038775 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Order</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesOrders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2034977870 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Subcategory</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue subcategories - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2860254825 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Vendor</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue vendors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>JSON Viewer</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Choose Query Direction</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Query Direction</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property 62802879" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request,Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property OptionCaption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 593408208 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Download content file</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Download - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 3351914939 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Request - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 3351914939 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Request - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 2278520725 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Response - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 2278520725 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Response - Property Caption</note>
        </trans-unit>
      </group>
    </body>
  </file>
</xliff>