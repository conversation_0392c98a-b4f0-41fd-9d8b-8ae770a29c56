﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Patients;

namespace WSA.Retail.Integration.Manage.Models.Patients;

public class PatientFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PatientFromEventHandler> logger,
    IPatientService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<PatientEventHubEvent, Patient> eventHubEntityAdapter) 
    : GenericFromEventHandler<
        PatientEventHubEvent, 
        IPatientService,
        Patient,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Patient)

{
    private readonly ILogger<PatientFromEventHandler> _logger = logger;

    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }


    protected override Guid GetEventEntityId(PatientEventHubEvent entity)
    {
        return entity.Id;
    }

    protected override string? GetEventEntityName(PatientEventHubEvent entity)
    {
        return entity.FullName;
    }
}