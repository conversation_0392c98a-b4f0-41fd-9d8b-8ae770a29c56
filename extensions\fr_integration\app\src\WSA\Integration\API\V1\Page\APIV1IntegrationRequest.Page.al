namespace WSA.Integration.API.V1;

using WSA.Integration;

page 50184 "API V1 Integration Request"
{
    PageType = API;
    APIGroup = 'integration';
    APIPublisher = 'wsa';
    APIVersion = 'v1.0';
    EntityCaption = 'Request';
    EntitySetCaption = 'Requests';
    EntityName = 'request';
    EntitySetName = 'requests';
    Caption = 'Retail Integration Request';
    ODataKeyFields = SystemId;
    SourceTable = "WSA Integration Request Log";
    SourceTableTemporary = true;
    ChangeTrackingAllowed = false;
    DelayedInsert = true;
    ModifyAllowed = false;
    DeleteAllowed = false;
    Extensible = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(id; Rec.SystemId)
                {
                    Caption = 'Id';
                    Editable = false;
                }

                field(entryNo; entryNo)
                {
                    Caption = 'Entry No.';
                }

                field(status; status)
                {
                    Caption = 'Status';
                }

                field(requestType; Rec."Type")
                {
                    Caption = 'Request Type';
                }

                field(requestMethod; Rec.Method)
                {
                    Caption = 'Request Method';
                }

                field(requestBody; RequestBody)
                {
                    Caption = 'request';

                    trigger OnValidate()
                    var
                        requestStream: OutStream;

                    begin
                        if RequestBody <> '' then begin
                            Rec."Request Content".CreateOutStream(requestStream);
                            requestStream.Write(RequestBody);
                        end;
                    end;
                }

                field(responseBody; ResponseBody)
                {
                    Caption = 'response';
                }
            }
        }
    }


    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    var
        Request: Record "WSA Integration Request Log";

    begin
        Request := Rec;
        Request.Insert(true);
        Request.Get(Request."Entry No.");
        Rec := Request;

        SetCalculatedFields();

        exit(false);
    end;

    trigger OnAfterGetRecord()
    begin
        SetCalculatedFields();
    end;


    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        ClearCalculatedFields();
    end;


    local procedure SetCalculatedFields()
    var
        Request: Record "WSA Integration Request Log";
        requestStream: InStream;
        responseStream: InStream;

    begin
        if Rec."Entry No." <> 0 then begin
            Request.Get(Rec."Entry No.");
            entryNo := Request."Entry No.";
            status := Request.Status;

            Request.CalcFields("Request Content");
            Request."Request Content".CreateInStream(requestStream);
            requestStream.Read(RequestBody);

            Request.CalcFields("Response Content");
            Request."Response Content".CreateInStream(responseStream);
            responseStream.Read(ResponseBody);
        end else begin
            entryNo := Rec."Entry No.";
            status := Rec.Status;
        end;
    end;


    local procedure ClearCalculatedFields()
    begin
        Clear(entryNo);
        Clear(status);
        Clear(RequestBody);
    end;


    var
        RequestBody: Text;
        ResponseBody: Text;
        entryNo: Integer;
        status: Enum "WSA Request Status";
}
