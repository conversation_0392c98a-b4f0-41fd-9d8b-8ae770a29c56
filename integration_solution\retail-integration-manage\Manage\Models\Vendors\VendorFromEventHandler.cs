﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Models.Countries;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<VendorFromEventHandler> logger,
    IVendorService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<VendorEventHubEvent, Vendor> eventHubEntityAdapter,
    IInventoryCountryService inventoryCountryService)
    : GenericFromEventHandler<
        VendorEventHubEvent,
        IVendorService,
        Vendor,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Vendor)

{
    private readonly ILogger<VendorFromEventHandler> _logger = logger;

    private readonly IInventoryCountryService _inventoryCountryService = inventoryCountryService;


    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }



    protected override Guid GetEventEntityId(VendorEventHubEvent entity)
    {
        return entity.Id ?? Guid.Empty;
    }

    protected override string? GetEventEntityName(VendorEventHubEvent entity)
    {
        return entity.Name;
    }

    protected override async Task AfterConvertToEntity(Vendor entity)
    {
        if (entity.Country != null && entity.Country.Length > 2)
        {
            var inventoryCountry = await _inventoryCountryService.GetCountryByNameAsync(entity.Country);
            if (inventoryCountry != null)
            {
                entity.Country = inventoryCountry.Code;
            }
        }
    }
}