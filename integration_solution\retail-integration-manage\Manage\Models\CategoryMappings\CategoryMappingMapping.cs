﻿namespace WSA.Retail.Integration.Manage.Models.CategoryMappings;

public static class CategoryMappingMapping
{
    public static CategoryMappingEntity ToEntity(this CategoryMapping model)
    {
        return new CategoryMappingEntity
        {
            Id = model.Id,
            CategoryId = model.CategoryId,
            IsHearingAid = model.IsHearingAid,
            HearingAidTypeId = model.HearingAidTypeId,
            CreatedOn = model.CreatedOn,
            ModifiedOn = model.ModifiedOn
        };
    }

    public static CategoryMapping ToModel(this CategoryMappingEntity entity)
    {
        return new CategoryMapping
        {
            Id = entity.Id,
            CategoryId = entity.CategoryId,
            IsHearingAid = entity.IsHearingAid,
            HearingAidTypeId = entity.HearingAidTypeId,
            CreatedOn = entity.CreatedOn,
            ModifiedOn = entity.ModifiedOn
        };
    }
}