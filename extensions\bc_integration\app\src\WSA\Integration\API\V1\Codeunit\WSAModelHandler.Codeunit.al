namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using WSA.Integration;


codeunit 50132 "WSA Model Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Model Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        Model: Record "Model";

    begin
        if TryHandlePost(Request, Model) then
            Common.SetCreatedResponse(Request, Common.ModelToJson(Model))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        Model: Record "Model";

    begin
        if not TryHandleGet(Request, Model) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Model: Record "Model")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Model.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Model.Init();
            Model.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Model.Insert(false);
        end;

        recRef.GetTable(Model);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Model.FieldNo(Name), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Model);
        Model.Modify(true);
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Model: Record "Model")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Model.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.ModelToJson(Model));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Model.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Model.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ModelToJson(Model));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Model.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if Model.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ModelToJson(Model));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
