﻿namespace WSA.Retail.Integration.Core;

public interface ISalesDocumentRepository<TModel, TLineModel, TEntity, TLineEntity>
    : IDocumentRepositoryBase<TModel, TLineModel, TEntity, TLineEntity>
    where TModel : class, ISalesDocument<TLineModel>
    where TLineModel : class, ISalesDocumentLine
    where TEntity : class, ISalesDocumentEntity<TLineEntity>
    where TLineEntity : class, ISalesDocumentLineEntity
{
}
