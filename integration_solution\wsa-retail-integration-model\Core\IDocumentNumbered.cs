﻿using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Core;

public interface IDocumentNumbered
{
    public string? DocumentNumber { get; set; }
    public string? AlternateNumber { get; set; }

    public bool HasChanges(IDocumentNumbered? oldEntity)
    {
        if (oldEntity == null) return true;
        if (!Common.AreEqual(DocumentNumber, oldEntity.DocumentNumber)) return true;
        if (!Common.AreEqual(AlternateNumber, oldEntity.AlternateNumber)) return true;
        return false;
    }
}
