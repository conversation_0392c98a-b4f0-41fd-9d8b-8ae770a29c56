﻿using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoiceLine : ISalesInvoiceLine
{
    [JsonPropertyName("id")] public Guid Id { get; set; } = Guid.Empty;
    [JsonPropertyName("sequence")] public int? Sequence { get; set; }
    [JsonPropertyName("externalReference")] public string? ExternalReference { get; set; }
    [JsonPropertyName("salesOrderLine")] public ExternalDocumentLineReference? SalesOrderLine { get; set; }
    [JsonPropertyName("product")] public ExternalReference? Product { get; set; }
    [JsonPropertyName("description")] public string? Description { get; set; }
    [JsonPropertyName("quantity")] public decimal? Quantity { get; set; }
    [JsonPropertyName("serialNumber")] public string? SerialNumber { get; set; }
    [JsonPropertyName("unitPrice")] public decimal? UnitPrice { get; set; }
    [JsonPropertyName("grossAmount")] public decimal? GrossAmount { get; set; }
    [JsonPropertyName("discountAmount")] public decimal? DiscountAmount { get; set; }
    [JsonPropertyName("amountExclTax")] public decimal? AmountExclTax { get; set;}
    [JsonPropertyName("taxAmount")] public decimal? TaxAmount { get; set; }
    [JsonPropertyName("amountInclTax")] public decimal? AmountInclTax { get; set; }
    [JsonPropertyName("createdOn")] public DateTime CreatedOn { get; set; }
    [JsonPropertyName("modifiedOn")] public DateTime ModifiedOn { get; set; }

    public bool HasChanges(SalesInvoiceLine? oldSalesInvoiceLine)
    {
        if (oldSalesInvoiceLine == null) return true;
        if (((IDocumentLine)this).HasChanges(oldSalesInvoiceLine)) return true;
        if (((IProductAssociated)this).HasChanges(oldSalesInvoiceLine)) return true;
        if (((IDescribable)this).HasChanges(oldSalesInvoiceLine)) return true;
        if (((IQuantifiable)this).HasChanges(oldSalesInvoiceLine)) return true;
        if (((ISerializable)this).HasChanges(oldSalesInvoiceLine)) return true;
        if (((IPriced)this).HasChanges(oldSalesInvoiceLine)) return true;
        return false;
    }

    public static ExternalDocumentLineReference? GetExternalDocumentLineReferenceFromJson(JsonNode json)
    {
        if ((Guid?)json.AsObject()["salesInvoiceLine"]?["id"] != null)
        {
            return new ExternalDocumentLineReference()
            {
                Id = (Guid?)json.AsObject()["salesInvoiceLine"]?["id"],
                Sequence = (int?)json.AsObject()["salesInvoiceLine"]?["sequence"]
            };
        }
        return null;
    }
}