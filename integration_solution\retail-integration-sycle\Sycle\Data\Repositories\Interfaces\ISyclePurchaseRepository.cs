using WSA.Retail.Integration.Sycle.Models.Purchases;

namespace WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;

public interface ISyclePurchaseRepository
{
    Task<List<string>> GetIntegrationRecordsAsync();

    Task<List<SyclePurchaseEntity>> GetLinesByTransactionNo(string transactionNo);

    Task<SyclePurchase?> GetAsync(int id);
    Task<bool> UpdateIntegrationStatusAsync(int id, bool isIntegrated = false);
}