# Core Architecture Class Diagram

This diagram illustrates the core architecture of the WSA Retail Integration Model project.

```mermaid
classDiagram
    class IMasterDataEntityService~TModel, TDto~ {
        <<interface>>
        +GetAsync(externalSystemCode, id, code, externalCode) Task~TModel?~
        +GetListAsync(externalSystemCode, id, code, name, externalCode) Task~List~TModel~~
        +GetExternalReferenceAsync(externalSystemCode, id, code, externalCode) Task~ExternalReference?~
        +UpsertAsync(dto) Task~TModel?~
    }
    
    class ITransactionEntityService~TModel, TDto~ {
        <<interface>>
        +GetAsync(externalSystemCode, id, documentNumber, externalReference) Task~TModel?~
        +GetListAsync(externalSystemCode, id, documentNumber, externalReference) Task~List~TModel~~
        +GetExternalDocumentReferenceAsync(externalSystemCode, externalReference) Task~ExternalDocumentReference?~
        +UpsertAsync(dto) Task~TModel?~
    }
    
    class BaseRepositoryService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #IDbContextFactory~IntegrationContext~ _dbContextFactory
        +BaseRepositoryService(options, logger, dbContextFactory)
    }
    
    class BaseEntityService~T~ {
        #IEventGridPublisher _eventGridPublisher
        +BaseEntityService(options, logger, dbContextFactory, eventGridPublisher)
    }
    
    class IntegrationContext {
        +DbSet~BatteryEntity~ BatteryEntity
        +DbSet~CategoryEntity~ CategoryEntity
        +DbSet~ClaimEntity~ ClaimEntity
        +DbSet~ClinicEntity~ ClinicEntity
        ... (other entities)
        +OnModelCreating(modelBuilder) void
        +OnConfiguring(optionsBuilder) void
    }
    
    class AppSettings {
        +string AppName
        +string SqlConnectionString
        +string EventGridEndpoint
        +string EventGridAccessKey
        +string StorageQueueConnectionString
        +string FromStorageQueueName
        +string ToStorageQueueName
        +string ExternalSystemCode
    }
    
    class ServiceCollectionExtensions {
        <<static>>
        +AddCoreServices(services, configuration) IServiceCollection
    }
    
    BaseRepositoryService <|-- BaseEntityService
    BaseEntityService ..|> IMasterDataEntityService : implements
    BaseEntityService ..|> ITransactionEntityService : implements
    ServiceCollectionExtensions --> IntegrationContext : configures
    ServiceCollectionExtensions --> AppSettings : configures
```

This diagram shows the core architecture of the model project with its key interfaces and base classes. The `IMasterDataEntityService` and `ITransactionEntityService` interfaces define the contract for master data (like customers, products) and transaction data (like orders, invoices) services. The `BaseEntityService` inherits from `BaseRepositoryService` and implements these interfaces, providing common functionality for all entity services.