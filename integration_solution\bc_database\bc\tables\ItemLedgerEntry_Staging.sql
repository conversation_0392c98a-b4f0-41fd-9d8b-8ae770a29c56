﻿CREATE TABLE [bc].[ItemLedgerEntry_Staging](
	[Id] UNIQUEIDENTIFIER NOT NULL,
	[CompanyId] UNIQUEIDENTIFIER,
	[EntryNo] INT,
	[JournalBatchName] NVARCHAR(20),
	[EntryType] NVARCHAR(50),
	[ItemId] UNIQUEIDENTIFIER,
	[ItemNo] NVARCHAR(20),
	[LocationId] UNIQUEIDENTIFIER,
	[LocationCode] NVARCHAR(20),
	[PostingDate] DATETIME2(7),
	[DocumentDate] DATETIME2(7),
	[DocumentType] NVARCHAR(50),
	[DocumentNo] NVARCHAR(20),
	[ExternalDocumentNo] NVARCHAR(50),
	[Description] NVARCHAR(100),
	[Open] BIT,
	[Quantity] DECIMAL(18,5),
	[SerialNo] NVARCHAR(50),
	[DimensionSetId] INT,
	[SystemCreatedAt] DATETIME2(7),
	[SystemModifiedAt] DATETIME2(7),
	[ETL_LoadId] NVARCHAR(40),
	[ETL_CreatedAt] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ETL_Status] NVARCHAR(40)
	)
GO

CREATE INDEX [IX_ItemLedgerEntryStaging_ETLLoadId_CompanyId_Id]
ON [bc].[ItemLedgerEntry_Staging] ([ETL_LoadId], [CompanyId], [Id])
GO