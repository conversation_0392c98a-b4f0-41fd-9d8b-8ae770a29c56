﻿CREATE PROCEDURE [cosium].[UpsertStagedArticleFacture]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.articlefacture
          SET [reffacture] = Source.[reffacture],
              [refarticle] = Source.[refarticle],
              [qtearticle] = Source.[qtearticle],
              [qtearticle_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[qtearticle]), 0.0000),
              [prixunitht] = Source.[prixunitht],
              [prixunitht_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[prixunitht]), 0.0000),
              [prixunitttc] = Source.[prixunitttc],
              [prixunitttc_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[prixunitttc]), 0.0000),
              [remise] = Source.[remise],
              [remise_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[remise]), 0.0000),
              [typeremise] = Source.[typeremise],
              [totalarticlettc] = Source.[totalarticlettc],
              [totalarticlettc_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[totalarticlettc]), 0.0000),
              [tauxtva] = Source.[tauxtva],
              [tauxtva_dec] = ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[tauxtva]), 0.0000),
              [codeproduit] = Source.[codeproduit],
              [libelle] = Source.[libelle],
              [codecomptable] = Source.[codecomptable],
              [refarticlefactureorigine] = Source.[refarticlefactureorigine],
              [numserieaf] = Source.[numserieaf],
              [refproduit] = Source.[refproduit],
              [cote] = Source.[cote],
              [refvendeur] = Source.[refvendeur],
              [commentaireaf] = Source.[commentaireaf],
              [typeremisemajoration] = Source.[typeremisemajoration],
              [htouttc] = Source.[htouttc],
              [soldemanuellement] = Source.[soldemanuellement],
              [displaylpp] = Source.[displaylpp],
              [libelleremise] = Source.[libelleremise],
              [reffamille] = Source.[reffamille],
              [aftaxedeee] = Source.[aftaxedeee],
              [afcouleur] = Source.[afcouleur],
              [datemodif] = Source.[datemodif],
              [rang] = Source.[rang],
              [origincarttype] = Source.[origincarttype],
              [supplierorderforminvoiceditem_id] = Source.[supplierorderforminvoiceditem_id],
              [supplierdeliverynoteinvoiceditem_id] = Source.[supplierdeliverynoteinvoiceditem_id],
              [purchaseinvoiceditem_id] = Source.[purchaseinvoiceditem_id]

         FROM cosium.articlefacture

              INNER JOIN cosium.staging_articlefacture AS Source
                      ON articlefacture.id = Source.id

        WHERE articlefacture.datemodif <> Source.datemodif;

       INSERT INTO cosium.articlefacture (
              [id],
              [reffacture],
              [refarticle],
              [qtearticle],
              [qtearticle_dec],
              [prixunitht],
              [prixunitht_dec],
              [prixunitttc],
              [prixunitttc_dec],
              [remise],
              [remise_dec],
              [typeremise],
              [totalarticlettc],
              [totalarticlettc_dec],
              [tauxtva],
              [tauxtva_dec],
              [codeproduit],
              [libelle],
              [codecomptable],
              [refarticlefactureorigine],
              [numserieaf],
              [refproduit],
              [cote],
              [refvendeur],
              [commentaireaf],
              [typeremisemajoration],
              [htouttc],
              [soldemanuellement],
              [displaylpp],
              [libelleremise],
              [reffamille],
              [aftaxedeee],
              [afcouleur],
              [datemodif],
              [rang],
              [origincarttype],
              [supplierorderforminvoiceditem_id],
              [supplierdeliverynoteinvoiceditem_id],
              [purchaseinvoiceditem_id]
              )

       SELECT Source.[id],
              Source.[reffacture],
              Source.[refarticle],
              Source.[qtearticle],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[qtearticle]), 0.0000) AS [qtearticle_dec],
              Source.[prixunitht],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[prixunitht]), 0.0000) AS [prixunitht_dec],
              Source.[prixunitttc],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[prixunitttc]), 0.0000) AS [prixunitttc_dec],
              Source.[remise],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[remise]), 0.0000) AS [remise_dec],
              [typeremise] = Source.[typeremise],
              Source.[totalarticlettc],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[totalarticlettc]), 0.0000) AS [totalarticlettc_dec],
              Source.[tauxtva],
              ISNULL(TRY_CONVERT(DECIMAL(18,4), Source.[tauxtva]), 0.0000) AS [tauxtva_dec],
              Source.[codeproduit],
              Source.[libelle],
              Source.[codecomptable],
              Source.[refarticlefactureorigine],
              Source.[numserieaf],
              Source.[refproduit],
              Source.[cote],
              Source.[refvendeur],
              Source.[commentaireaf],
              Source.[typeremisemajoration],
              Source.[htouttc],
              Source.[soldemanuellement],
              Source.[displaylpp],
              Source.[libelleremise],
              Source.[reffamille],
              Source.[aftaxedeee],
              Source.[afcouleur],
              Source.[datemodif],
              Source.[rang],
              Source.[origincarttype],
              Source.[supplierorderforminvoiceditem_id],
              Source.[supplierdeliverynoteinvoiceditem_id],
              Source.[purchaseinvoiceditem_id]

         FROM cosium.staging_articlefacture AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.articlefacture WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_articlefacture
       
       COMMIT TRANSACTION;