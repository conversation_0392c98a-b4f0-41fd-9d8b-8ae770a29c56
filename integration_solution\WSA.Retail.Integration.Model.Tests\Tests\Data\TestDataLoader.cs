﻿using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Tests.Data;

public static class TestDataLoader
{
    public static IEnumerable<BatteryEntity> Batteries()
    {
        return JsonTestDataReader.ReadArray<BatteryEntity>("Batteries.json", "batteries");
    }

    public static IEnumerable<CouplingEntity> BatteryCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Batteries.json", "couplings");
    }

    public static IEnumerable<CategoryEntity> Categories()
    {
        return JsonTestDataReader.ReadArray<CategoryEntity>("Categories.json", "categories");
    }

    public static IEnumerable<CouplingEntity> CategoryCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Categories.json", "couplings");
    }

    public static IEnumerable<ClinicEntity> Clinics()
    {
        return JsonTestDataReader.ReadArray<ClinicEntity>("Clinics.json", "clinics");
    }

    public static IEnumerable<CouplingEntity> ClinicCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Clinics.json", "couplings");
    }

    public static IEnumerable<ColorEntity> Colors()
    {
        return JsonTestDataReader.ReadArray<ColorEntity>("Colors.json", "colors");
    }

    public static IEnumerable<CouplingEntity> ColorCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Colors.json", "couplings");
    }

    public static IEnumerable<EntityEntity> Entities()
    {
        return JsonTestDataReader.ReadArray<EntityEntity>("Entities.json", "entities");
    }

    public static IEnumerable<EntitySubscriberEntity> EntitySubscribers()
    {
        return JsonTestDataReader.ReadArray<EntitySubscriberEntity>("EntitySubscribers.json", "entitySubscribers");
    }

    public static IEnumerable<ExternalSystemEntity> ExternalSystems()
    {
        return JsonTestDataReader.ReadArray<ExternalSystemEntity>("ExternalSystems.json", "externalSystems");
    }

    public static IEnumerable<ManufacturerEntity> Manufacturers()
    {
        return JsonTestDataReader.ReadArray<ManufacturerEntity>("Manufacturers.json", "manufacturers");
    }

    public static IEnumerable<CouplingEntity> ManufacturerCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Manufacturers.json", "couplings");
    }

    public static IEnumerable<PatientEntity> Patients()
    {
        return JsonTestDataReader.ReadArray<PatientEntity>("Patients.json", "patients");
    }

    public static IEnumerable<CouplingEntity> PatientCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Patients.json", "couplings");
    }
    public static IEnumerable<ProductEntity> Products()
    {
        return JsonTestDataReader.ReadArray<ProductEntity>("Products.json", "products");
    }

    public static IEnumerable<CouplingEntity> ProductCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Products.json", "couplings");
    }

    public static IEnumerable<VendorEntity> Vendors()
    {
        return JsonTestDataReader.ReadArray<VendorEntity>("Vendors.json", "vendors");
    }

    public static IEnumerable<CouplingEntity> VendorCouplings()
    {
        return JsonTestDataReader.ReadArray<CouplingEntity>("Vendors.json", "couplings");
    }
}
