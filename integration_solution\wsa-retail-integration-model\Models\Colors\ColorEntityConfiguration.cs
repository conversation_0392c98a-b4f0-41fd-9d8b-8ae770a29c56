using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Colors
{
    public class ColorEntityConfiguration : IEntityTypeConfiguration<ColorEntity>
    {
        public void Configure(EntityTypeBuilder<ColorEntity> builder)
        {
            builder.ToTable("Color", "dbo");

            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(c => c.HexCode)
                .HasColumnName("HexCode")
                .HasColumnType("nvarchar(20)")
                .HasMaxLength(20);
        }
    }
}