﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IVendorService entityService)
    : BaseApiGetByQuery<
        Vendor,
        IVendorService>(appSettings, entityService)
{
}