using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductParentConfiguration : IEntityTypeConfiguration<PimProductParentEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductParentEntity> builder)
        {
            builder.ToTable("ProductParent", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.Property(x => x.ParentId)
                .HasColumnName("ParentId")
                .HasColumnType("uniqueidentifier")
                .IsRequired();

            builder.HasOne(x => x.PimChildProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ProductId);

            builder.HasOne(x => x.PimParentProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ParentId);
        }
    }
}