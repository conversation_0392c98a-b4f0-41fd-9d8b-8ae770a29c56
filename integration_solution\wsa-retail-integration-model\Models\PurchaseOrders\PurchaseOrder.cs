﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Models.PurchaseOrders;

public class PurchaseOrder : IPurchaseOrder
{
    public Guid Id { get; set; } = Guid.Empty;
    public string? ExternalSystemCode { get; set; }
    public string? DocumentNumber { get; set; } = string.Empty;
    public string? ExternalReference { get; set; } = string.Empty;
    public string? AlternateNumber { get; set; } = string.Empty;
    public ExternalReference? Vendor { get; set; }
    public ExternalReference? Clinic { get; set; }
    public DateTime? DocumentDate { get; set; }
    [JsonPropertyName("purchaseOrderLines")] public List<PurchaseOrderLine> Lines { get; set; } = [];
    public DateTime? CreatedOn { get; set; }
    public DateTime? ModifiedOn { get; set; }

    public bool HasChanges(IDocument<IDocumentLine>? oldEntity)
    {
        if (oldEntity is not PurchaseOrder oldPurchaseOrder) return true;
        return HasChanges(oldPurchaseOrder);
    }

    public bool HasChanges(PurchaseOrder? oldPurchaseOrder)
    {
        if (oldPurchaseOrder == null) return true;
        if (!Common.AreEqual(DocumentNumber, oldPurchaseOrder.DocumentNumber)) return true;
        if (!Common.AreEqual(ExternalReference, oldPurchaseOrder.ExternalReference)) return true;
        if (!Common.AreEqual(AlternateNumber, oldPurchaseOrder.AlternateNumber)) return true;
        if (!Common.AreEqual(Vendor, oldPurchaseOrder.Vendor)) return true;
        if (!Common.AreEqual(Clinic, oldPurchaseOrder.Clinic)) return true;
        if (!Common.AreEqual(DocumentDate, oldPurchaseOrder.DocumentDate)) return true;
        if (Lines.Count() != oldPurchaseOrder.Lines.Count())
            return true;

        foreach (var line in Lines)
        {
            var matchingOldLine = oldPurchaseOrder.Lines.FirstOrDefault(l => l.Sequence == line.Sequence);
            if (matchingOldLine == null || line.HasChanges(matchingOldLine))
                return true;
        }

        foreach (var oldLine in oldPurchaseOrder.Lines)
        {
            if (!Lines.Any(l => l.Sequence == oldLine.Sequence))
                return true;
        }
        return false;
    }

    public static ExternalDocumentReference GetExternalDocumentReferenceFromJson(JsonNode json)
    {
        return new ExternalDocumentReference()
        {
            Id = (Guid?)json.AsObject()["purchaseOrder"]?["id"],
            DocumentNumber = (string?)json.AsObject()["purchaseOrder"]?["documentNumber"],
            ExternalReference = (string?)json.AsObject()["purchaseOrder"]?["externalReference"]
        };
    }

    public void AddTelemetryProperties(MetricTelemetry metricTelemetry)
    {
        metricTelemetry.Properties.Add("PurchaseOrderId", Id.ToString());
        metricTelemetry.Properties.Add("PurchaseOrderNumber", DocumentNumber);
        if (DocumentDate != null)
            metricTelemetry.Properties.Add("PurchaseOrderDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            metricTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            metricTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Vendor?.Id != null)
            metricTelemetry.Properties.Add("VendorId", Vendor.Id.ToString());
        if (Vendor?.Code != null)
            metricTelemetry.Properties.Add("VendorCode", Vendor.Code);
    }

    public void AddEventProperties(EventTelemetry eventTelemetry)
    {
        eventTelemetry.Properties.Add("PurchaseOrderId", Id.ToString());
        eventTelemetry.Properties.Add("PurchaseOrderNumber", DocumentNumber);
        if (DocumentDate != null)
            eventTelemetry.Properties.Add("PurchaseOrderDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (Clinic?.Id != null)
            eventTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            eventTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Vendor?.Id != null)
            eventTelemetry.Properties.Add("VendorId", Vendor.Id.ToString());
        if (Vendor?.Code != null)
            eventTelemetry.Properties.Add("VendorCode", Vendor.Code);
    }
}