﻿using WSA.Retail.Integration.PIM.Models.Categories;

namespace WSA.Retail.Integration.PIM.Models.Products;

public class PimProductCategoryEntity : IPimProductCategoryEntity, WSA.Retail.Integration.Core.IIdentifiable
{
    public Guid Id { get; set; } = Guid.Empty;
    public Guid ProductId { get; set; }
    public Guid CategoryId { get; set; }
    public DateTime SystemCreatedOn { get; set; }
    public DateTime SystemModifiedOn { get; set; }

    public virtual PimProductEntity ProductEntity { get; set; } = null!;
    public virtual PimCategoryEntity CategoryEntity { get; set; } = null!;
}