﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Utilities;
using WSA.Retail.Integration.Manage.EventProcessing;
using Microsoft.Azure.Functions.Worker.Builder;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductEventHandler(
    ProductFromEventHandler fromEventHandler,
    ProductToEventHandler toEventHandler,
    ProductGetByQueryHandler queryHandler) : IEventHandler
{
    private readonly ProductFromEventHandler _fromEventHandler = fromEventHandler;
    private readonly ProductToEventHandler _toEventHandler = toEventHandler;
    private readonly ProductGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await _toEventHandler.HandleToQueueAsync(ev);
    }

    [Function("ProductGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "products")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}