# Transaction Data Operation Sequence Diagram

This diagram illustrates the sequence of operations for a transaction entity (like SalesOrder, SalesInvoice, etc.) in the WSA Retail Integration Model project.

```mermaid
sequenceDiagram
    participant Client
    participant TransactionService as Transaction Service
    participant Repository as Transaction Repository
    participant ReferenceServices as Reference Services
    participant DB as Integration Context
    participant EventGrid as Event Grid Publisher
    
    %% Get operation
    Client->>TransactionService: GetAsync(externalSystemCode, id, documentNumber, externalReference)
    TransactionService->>Repository: GetAsync(id) or GetAsync(documentNumber)
    Repository->>DB: Query Entity with related entities
    DB-->>Repository: Return Entity with relations
    Repository-->>TransactionService: Return Entity
    TransactionService-->>Client: Return Mapped Model
    
    %% Upsert operation (more complex than master data)
    Client->>TransactionService: UpsertAsync(transactionDto)
    
    %% Check if transaction exists
    TransactionService->>Repository: GetAsync(externalReference) or GetAsync(documentNumber)
    Repository->>DB: Query Transaction
    DB-->>Repository: Return Transaction (or null)
    Repository-->>TransactionService: Return Transaction (or null)
    
    %% Resolve references (like clinic, patient, products)
    TransactionService->>ReferenceServices: GetAsync for each reference (clinic, patient, etc.)
    ReferenceServices->>DB: Query Reference Entities
    DB-->>ReferenceServices: Return Reference Entities
    ReferenceServices-->>TransactionService: Return Reference Entities
    
    alt Transaction exists
        TransactionService->>TransactionService: Map DTO to existing Transaction
    else Transaction doesn't exist
        TransactionService->>TransactionService: Create new Transaction from DTO
    end
    
    %% For each line item, resolve product references
    loop For each line item
        TransactionService->>ReferenceServices: GetAsync for product
        ReferenceServices->>DB: Query Product
        DB-->>ReferenceServices: Return Product
        ReferenceServices-->>TransactionService: Return Product
        TransactionService->>TransactionService: Add/Update line item with product
    end
    
    TransactionService->>Repository: UpsertAsync(transaction)
    Repository->>DB: SaveChanges()
    DB-->>Repository: Return Updated Transaction
    Repository-->>TransactionService: Return Updated Transaction
    
    TransactionService->>EventGrid: RaiseEventAsync(settings, "TransactionUpdated", subject, transaction)
    EventGrid-->>TransactionService: Event Published
    
    TransactionService-->>Client: Return Mapped Model
```

This sequence diagram shows the flow of operations for a transaction entity (like SalesOrder, SalesInvoice, etc.):

1. **Get Operation**: Similar to master data entities, but transaction entities often include related entities (like line items).

2. **Upsert Operation**: More complex than master data because transactions have relationships with multiple entities:
   - First, check if the transaction exists
   - Resolve references to related entities (like clinic, patient)
   - For each line item, resolve product references
   - Save the transaction with all its related entities
   - Publish an event to Event Grid
   - Return the mapped model to the client

This pattern ensures that all references are properly established before saving the transaction.