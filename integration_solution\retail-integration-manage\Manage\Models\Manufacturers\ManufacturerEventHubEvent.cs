﻿namespace WSA.Retail.Integration.Manage.Models.Manufacturers;

public class ManufacturerEventHubEvent
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDeleted { get; set; }
    public DateTime? ModifiedOn { get; set; }
    public string? Website { get; set; }
    public string? EmailAddress { get; set; }
    public string? PhoneNumber { get; set; }
    public string? FaxNumber { get; set; }
    public string? Address1 { get; set; }
    public string? Address2 { get; set; }
    public Guid? CountryId { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public ContactDto? SalesContact { get; set; }
    public ContactDto? AccountReceivableContact { get; set; }
}