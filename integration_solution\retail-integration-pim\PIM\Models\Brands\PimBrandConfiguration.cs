using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Brands
{
    public class PimBrandConfiguration : IEntityTypeConfiguration<PimBrandEntity>
    {
        public void Configure(EntityTypeBuilder<PimBrandEntity> builder)
        {
            builder.ToTable("Brand", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureCodeIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();
        }
    }
}