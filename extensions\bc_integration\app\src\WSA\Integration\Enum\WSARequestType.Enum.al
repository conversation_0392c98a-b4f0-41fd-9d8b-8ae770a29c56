namespace WSA.Integration;

using WSA.Integration.API.V1;

enum 50100 "WSA Request Type" implements "WSA Integration Request"
{
    Extensible = true;


    value(1; clinics)
    {
        Caption = 'Clinic';
        Implementation = "WSA Integration Request" = "WSA Clinic Handler";
    }

    value(2; patients)
    {
        Caption = 'Patient';
        Implementation = "WSA Integration Request" = "WSA Patient Handler";
    }

    value(3; payors)
    {
        Caption = 'Payor';
        Implementation = "WSA Integration Request" = "WSA Payor Handler";
    }

    value(4; vendors)
    {
        Caption = 'Vendor';
        Implementation = "WSA Integration Request" = "WSA Vendor Handler";
    }

    value(5; manufacturers)
    {
        Caption = 'Manufacturer';
        Implementation = "WSA Integration Request" = "WSA Manufacturer Handler";
    }

    value(6; colors)
    {
        Caption = 'Color';
        Implementation = "WSA Integration Request" = "WSA Color Handler";
    }

    value(7; categories)
    {
        Caption = 'Category';
        Implementation = "WSA Integration Request" = "WSA Category Handler";
    }

    value(8; subcategories)
    {
        Caption = 'Subcategory';
        Implementation = "WSA Integration Request" = "WSA Subcategory Handler";
    }

    value(9; products)
    {
        Caption = 'Product';
        Implementation = "WSA Integration Request" = "WSA Product Handler";
    }

    value(10; models)
    {
        Caption = 'Model';
        Implementation = "WSA Integration Request" = "WSA Model Handler";
    }

    value(11; batteries)
    {
        Caption = 'Battery';
        Implementation = "WSA Integration Request" = "WSA Battery Handler";
    }

    value(20; purchaseOrders)
    {
        Caption = 'Purchase Order';
        Implementation = "WSA Integration Request" = "WSA Purchase Order Handler";
    }

    value(21; purchaseReceipts)
    {
        Caption = 'Purchase Receipt';
        Implementation = "WSA Integration Request" = "WSA Purchase Receipts Handler";
    }

    value(22; purchaseReturns)
    {
        Caption = 'Purchase Return';
        Implementation = "WSA Integration Request" = "WSA Purchase Return Handler";
    }

    value(23; purchaseShipments)
    {
        Caption = 'Purchase Shipment';
        Implementation = "WSA Integration Request" = "WSA Purchase Shipment Handler";
    }

    value(30; salesOrders)
    {
        Caption = 'Sales Order';
        Implementation = "WSA Integration Request" = "WSA Sales Order Handler";
    }

    value(31; salesInvoices)
    {
        Caption = 'Sales Invoice';
        Implementation = "WSA Integration Request" = "WSA Sales Invoice Handler";
    }

    value(32; salesCredits)
    {
        Caption = 'Sales Credit';
        Implementation = "WSA Integration Request" = "WSA Sales Credit Handler";
    }

    value(33; payments)
    {
        Caption = 'Payment';
        Implementation = "WSA Integration Request" = "WSA Payment Handler";
    }

    value(34; claims)
    {
        Caption = 'Claim';
        Implementation = "WSA Integration Request" = "WSA Claim Handler";
    }

    value(40; adjustments)
    {
        Caption = 'Adjustment';
        Implementation = "WSA Integration Request" = "WSA Adjustment Handler";
    }
}
