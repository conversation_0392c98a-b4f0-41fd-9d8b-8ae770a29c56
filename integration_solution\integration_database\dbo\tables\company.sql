﻿CREATE TABLE [dbo].[Company]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Company_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NOT NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [Address]           NVARCHAR(100) NULL,
    [Address2]          NVARCHAR(50) NULL,
    [City]              NVARCHAR(30) NULL,
    [Region]            NVARCHAR(30) NULL,
    [Country]           NVARCHAR(10) NULL,
    [PostalCode]        NVARCHAR(20) NULL,
    [ExternalSystemId]  UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Company_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Company_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Company_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_Ccompany_ExternalSystem] FOREIGN KEY (ExternalSystemId) REFERENCES [dbo].[ExternalSystem](Id)
)
GO

CREATE UNIQUE INDEX IX_Company_Code
ON [dbo].[Company] ([Code])
GO

CREATE INDEX IX_Company_Name
ON [dbo].[Company] ([Name])
GO