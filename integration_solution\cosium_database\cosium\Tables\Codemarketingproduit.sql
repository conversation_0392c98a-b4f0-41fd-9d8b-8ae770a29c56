﻿CREATE TABLE [cosium].[codemarketingproduit](
	[id] [int] NOT NULL,
	[refproduit] [int] NULL,
	[codemailing] [varchar](50) NULL,
	[datemodif] [varchar](50) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
	[guid_id] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_codemarketingproduit] PRIMARY KEY CLUSTERED 
(
	[guid_id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [cosium].[codemarketingproduit] ADD  CONSTRAINT [DF_codemarketingproduit_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[codemarketingproduit] ADD  CONSTRAINT [DF_codemarketingproduit_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO

ALTER TABLE [cosium].[codemarketingproduit] ADD  DEFAULT (newid()) FOR [guid_id]
GO
