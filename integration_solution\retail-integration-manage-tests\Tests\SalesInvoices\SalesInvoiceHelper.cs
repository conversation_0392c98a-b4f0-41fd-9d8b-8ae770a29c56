﻿using System.Text.Json;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;
using WSA.Retail.Integration.Manage.Tests.Utilities;

namespace WSA.Retail.Integration.Manage.Tests.SalesInvoices;

public static class SalesInvoiceHelper
{
    public static string DeliveryNoteCreatedEvent(
        Guid? deliveryNoteId,
        Guid? saleId)
    {
        var fileName = "AUDeliveryNoteCreated.json";
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "Tests", "SalesInvoices", "TestData", fileName);
        var jsonTemplate = File.ReadAllText(filePath);

        var values = new Dictionary<string, object?>
        {
            ["messageId"] = Guid.NewGuid(),
            ["deliveryNoteId"] = deliveryNoteId ?? Guid.NewGuid(),
            ["saleId"] = saleId ?? Guid.NewGuid(),
            ["timestamp"] = DateTime.UtcNow
        };

        var response = JsonHelper.ReplacePlaceholders(jsonTemplate, values);
        return response;
    }

    public static Dictionary<string, object?> CreateOrderLine(
        Guid productId,
        int? quantity,
        Guid? colorId,
        Guid? batteryTypeId,
        string? sku)
    {
        var response = new Dictionary<string, object?>
        {
            ["Id"] = Guid.NewGuid(),
            ["ProductId"] = productId,
            ["Quantity"] = quantity ?? 1,
            ["UnitCost"] = 0.01m,
            ["ColorId"] = colorId,
            ["BatteryTypeId"] = batteryTypeId,
            ["Sku"] = sku,
            ["Attributes"] = new List<object>()
        };

        return response;
    }

    public static List<Dictionary<string, object?>> CreateSingleOrderLine(Guid productId, string? sku = null)
    {
        var line = CreateOrderLine(productId, 1, null, null, sku);
        return [line];
    }

    public static PurchaseOrderEventHubEvent PurchaseOrderEventHubEventFromEventHubMessage(EventHubMessage eventMessage)
    {
        ArgumentNullException.ThrowIfNull(eventMessage);
        var msg = eventMessage.Message.ToString();
        ArgumentNullException.ThrowIfNull(msg);
        var response = JsonSerializer.Deserialize<PurchaseOrderEventHubEvent>(msg);
        ArgumentNullException.ThrowIfNull(response);
        return response;
    }

    public static string CreateOrdersGet2Resonse(
        Guid orderId,
        string orderNumber,
        Guid locationId,
        Guid supplierId,
        List<Dictionary<string, object?>> lineItems)
    {
        var fileName = "OrdersGET2Response.json";
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "Tests", "PurchaseOrders", "TestData", fileName);
        var jsonTemplate = File.ReadAllText(filePath);

        var values = new Dictionary<string, object?>
        {
            ["orderId"] = orderId,
            ["orderNumber"] = orderNumber,
            ["locationId"] = locationId,
            ["supplierId"] = supplierId,
            ["lineItems"] = new RawJson(JsonSerializer.Serialize(lineItems, GetJsonSerializerOptions()))
        };

        var response = JsonHelper.ReplacePlaceholders(jsonTemplate, values);
        return response;
    }

    public static Dictionary<string, object?> CreateOrdersGET2ResponseLine(
    Guid productId,
    int? quantity,
    Guid? colorId,
    Guid? batteryTypeId,
    string? sku)
    {
        var response = new Dictionary<string, object?>
        {
            ["id"] = Guid.NewGuid(),
            ["product"] = new Dictionary<string, object?>
            {
                ["id"] = productId,
                ["name"] = "Product Name"
            },
            ["sku"] = sku,
            ["stockProductId"] = Guid.Empty,
            ["quantity"] = quantity ?? 1,
            ["unitCost"] = 0m,
            ["status"] = "InProgress",
            ["comment"] = null,
            ["total"] = 0m,
            ["color"] = colorId != null ? new Dictionary<string, object?>
            {
                ["id"] = colorId,
                ["name"] = "Color Name"
            } : null,
            ["batteryType"] = batteryTypeId != null ? new Dictionary<string, object?>
            {
                ["id"] = batteryTypeId,
                ["name"] = "Battery Type Name"
            } : null,
            ["attributes"] = new List<object>(),
            ["serialNumbers"] = new List<object>()
        };

        return response;
    }

    public static string? CreateOrdersGet2ResonseJson(
        EventHubMessage eventMessage, 
        Guid locationId,
        Guid supplierId)
    {
        var ordersEvent = PurchaseOrderEventHubEventFromEventHubMessage(eventMessage);
        if (ordersEvent == null)
            return default;

        var lineItems = new List<Dictionary<string, object?>>();
        foreach (var eventLine in ordersEvent.LineItems)
        {
            var lineItem = CreateOrdersGET2ResponseLine(
                productId: eventLine.ProductId,
                quantity: eventLine.Quantity,
                colorId: eventLine.ColorId,
                batteryTypeId: eventLine.BatteryTypeId,
                sku: eventLine.Sku);

            lineItems.Add(lineItem);
        }

        var response = CreateOrdersGet2Resonse(
            orderId: ordersEvent.Id,
            orderNumber: ordersEvent.Number,
            locationId: locationId,
            supplierId: supplierId,
            lineItems: lineItems);

        return response;
    }

    public static OrderResponse CreateOrder2GetResponse(
        EventHubMessage eventMessage,
        Guid locationId,
        Guid supplierId)
    {
        var responseJson = CreateOrdersGet2ResonseJson(
            eventMessage: eventMessage,
            locationId: Guid.NewGuid(),
            supplierId: Guid.NewGuid());
        ArgumentNullException.ThrowIfNull(responseJson);
        var response = JsonSerializer.Deserialize<OrderResponse>(responseJson, GetJsonSerializerOptions());
        ArgumentNullException.ThrowIfNull(response);
        return response;
    }

    private static JsonSerializerOptions GetJsonSerializerOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }
}
