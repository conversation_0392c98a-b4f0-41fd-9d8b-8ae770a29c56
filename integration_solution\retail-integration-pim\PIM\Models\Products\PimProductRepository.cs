﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.GraphQL;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products;

public class PimProductRepository(
    IOptions<AppSettings> appSettings,
    ILogger<PimProductRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory)
    : IPimProductRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<PimProductRepository> _logger = logger;
    private readonly IDbContextFactory<PimDbContext> _dbContextFactory = dbContextFactory;

    public async Task<bool> UpsertListAsync(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpsertListAsync));

        var products = pimProducts
            .Select(p => new PimProductUpsertTableType
            {
                Id = p.ProductId,
                Name = p.Name,
                ColorCode = p.Color?.AttributeValueCode,
                IsAvailable = p.IsAvailable,
                IsPhasedOut = p.IsPhasedOut,
                ListPrice = p.ListPrice ?? 0,
                ProductSource = p.ProductSource.ToString(),
                ProductType = p.ProductType.ToString(),
                Ranking = p.Ranking,
                ReleaseDate = p.ReleaseDate ?? DateTime.MinValue,
                Sku = p.Sku,
                State = p.State.ToString(),
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            })
            .ToList();

        if (products?.Count > 0)
        {
            await UperstProductsFromListAsync(products);
        }

        return true;
    }

    public async Task<bool> UperstProductsFromListAsync(List<PimProductUpsertTableType> tableTypes)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UperstProductsFromListAsync));
        using var context = _dbContextFactory.CreateDbContext();
        var connection = context.Database.GetDbConnection();
        using var command = connection.CreateCommand();

        command.CommandType = CommandType.StoredProcedure;
        command.CommandText = "pim.ProductUpsert";
        command.CommandTimeout = 300;

        // Create and prepare the data table parameter
        var productTableType = new DataTable();
        productTableType.Columns.Add("Id", typeof(string));
        productTableType.Columns.Add("Name", typeof(string));
        productTableType.Columns.Add("ColorCode", typeof(string));
        productTableType.Columns.Add("IsAvailable", typeof(string));
        productTableType.Columns.Add("IsPhasedOut", typeof(string));
        productTableType.Columns.Add("ListPrice", typeof(string));
        productTableType.Columns.Add("ProductSource", typeof(string));
        productTableType.Columns.Add("ProductType", typeof(string));
        productTableType.Columns.Add("Ranking", typeof(string));
        productTableType.Columns.Add("ReleaseDate", typeof(string));
        productTableType.Columns.Add("Sku", typeof(string));
        productTableType.Columns.Add("State", typeof(string));
        productTableType.Columns.Add("CreatedAt", typeof(string));
        productTableType.Columns.Add("UpdatedAt", typeof(string));

        foreach (var tableType in tableTypes)
        {
            productTableType.Rows.Add(
                tableType.Id,
                tableType.Name,
                tableType.ColorCode,
                tableType.IsAvailable,
                tableType.IsPhasedOut,
                tableType.ListPrice,
                tableType.ProductSource,
                tableType.ProductType,
                tableType.Ranking,
                tableType.ReleaseDate,
                tableType.Sku,
                tableType.State,
                tableType.CreatedAt,
                tableType.UpdatedAt
            );
        }

        command.Parameters.Add(new SqlParameter
        {
            ParameterName = "@source",
            SqlDbType = SqlDbType.Structured,
            TypeName = "pim.ProductUpsertTableType",
            Value = productTableType
        });
        var rowsReceivedParam = new SqlParameter
        {
            ParameterName = "@rowsReceived",
            SqlDbType = SqlDbType.Int,
            Direction = ParameterDirection.Output
        };
        var diagnosticParam = new SqlParameter
        {
            ParameterName = "@diagnosticMessage",
            SqlDbType = SqlDbType.NVarChar,
            Size = -1, // MAX
            Direction = ParameterDirection.Output
        };
        command.Parameters.Add(diagnosticParam);
        command.Parameters.Add(rowsReceivedParam);

        try
        {
            if (connection.State != ConnectionState.Open)
            {
                await connection.OpenAsync();
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            _logger.LogCustomInformation(_appSettings.AppName, nameof(UperstProductsFromListAsync),
                $"Stored procedure recieved {rowsReceivedParam.Value} records.");
            _logger.LogCustomInformation(_appSettings.AppName, nameof(UperstProductsFromListAsync),
                $"Stored procedure diagnostics: \r\n{diagnosticParam.Value}");

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex, _appSettings.AppName, nameof(UperstProductsFromListAsync));
            return false;
        }
        finally
        {
            if (connection.State == ConnectionState.Open)
            {
                await connection.CloseAsync();
            }
        }
    }

    public async Task<List<PimProductEntity>> GetNotIntegratedProducts(int batchSize)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpsertListAsync));

        var context = _dbContextFactory.CreateDbContext();
        var products = await context.PimProducts
            .Where(p => p.IntegrationRequired == true)
            //.Where(p => p.Sku == "10949935")
            .Include(p => p.PimColorEntity)
            .Include(p => p.Attributes)
                .ThenInclude(a => a.PimAttributeEntity)
            .Include(p => p.Images)
            .Include(p => p.Brands)
            .Include(p => p.Categories)
            .Include(p => p.BundleSets)
            .Include(p => p.ParentProducts)
            .OrderByDescending(p => p.SystemModifiedOn)
            .Take(batchSize)
            .ToListAsync();

        return products;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(PimProductEntity product)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpdateIntegrationStatusAsync));
        using var context = _dbContextFactory.CreateDbContext();

        try
        {
            var existingProduct = await context.PimProducts.FindAsync(product.Id);
            if (existingProduct != null)
            {
                existingProduct.IntegrationRequired = false;
                existingProduct.IntegratedOn = DateTime.UtcNow;
                existingProduct.SystemModifiedOn = DateTime.UtcNow;
                await context.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex, _appSettings.AppName, nameof(UpdateIntegrationStatusAsync));
            return false;
        }
        return false;
    }
}