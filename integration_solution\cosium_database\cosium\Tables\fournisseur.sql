﻿CREATE TABLE [cosium].[fournisseur] (
    [id]                     NVARCHAR (50)  NOT NULL,
    [nomfournisseur]         NVARCHAR (50)  NULL,
    [specialite]             NVARCHAR (50)  NULL,
    [adresse]                NVARCHAR (50)  NULL,
    [adresse2]               NVARCHAR (100) NULL,
    [codepostal]             NVARCHAR (50)  NULL,
    [ville]                  NVARCHAR (50)  NULL,
    [pays]                   NVARCHAR (50)  NULL,
    [tel]                    NVARCHAR (50)  NULL,
    [telp]                   NVARCHAR (50)  NULL,
    [fax]                    NVARCHAR (50)  NULL,
    [cache]                  NVARCHAR (50)  NULL,
    [commentairefournisseur] NVARCHAR (MAX) NULL,
    [url]                    NVARCHAR (50)  NULL,
    [email]                  NVARCHAR (100) NULL,
    [codefournisseur]        NVARCHAR (50)  NULL,
    [datemodif]              NVARCHAR (50)  NULL,
    [libelleinterne]         NVARCHAR (50)  NULL,
    [CreatedOn]              DATETIME2 CONSTRAINT [DF_fournisseur_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]             DATETIME2 CONSTRAINT [DF_fournisseur_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [IntegrationRequired]   BIT DEFAULT 0 NOT NULL,
    [IntegrationDate]       DATETIME2(7) NULL
    CONSTRAINT [PK_fournisseur] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_fournisseur_codefournisseur
ON [cosium].[fournisseur] (codefournisseur)
GO

CREATE INDEX IX_fournisseur_ModifiedOn
ON [cosium].[fournisseur] ([ModifiedOn])
GO

CREATE INDEX IX_fournisseu_IntegrationRequired
ON [cosium].[fournisseur] ([IntegrationRequired], [id])
GO

CREATE TRIGGER [cosium].[fournisseur_UpdateModified]
ON [cosium].[fournisseur]
AFTER UPDATE 
AS
   UPDATE [cosium].[fournisseur]
      SET [ModifiedOn] = sysutcdatetime(),
          [IntegrationRequired] = IIF(ISNULL(d.[datemodif], '1900-01-01') < i.[datemodif], 1, [fournisseur].[IntegrationRequired])
     FROM Inserted AS i
          LEFT JOIN deleted AS d
            ON i.id = d.id
    WHERE [fournisseur].[id] = i.[id]
GO