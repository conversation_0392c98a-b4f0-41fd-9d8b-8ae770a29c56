﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Brands;

namespace WSA.Retail.Integration.PIM.Models.Countries;

public interface IPimCountryBrandEntity : IIdentifiable, IAuditInfo
{
    public Guid CountryId { get; set; }
    public Guid BrandId { get; set; }

    public abstract PimCountryEntity CountryEntity { get; set; }
    public abstract PimBrandEntity BrandEntity { get; set; }
}