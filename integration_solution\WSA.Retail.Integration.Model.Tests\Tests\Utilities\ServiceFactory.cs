﻿using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data.Repositories;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.ProductModels;
using WSA.Retail.Integration.Models.TaxGroups;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.Claims;
using WSA.Retail.Integration.Models.Payors;

namespace WSA.Retail.Integration.Tests.Utilities;

public class ServiceFactory
{
    private readonly string _dbName;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<IDbContextFactory<IntegrationContext>> _dbContextFactoryMock;
    private readonly Mock<IEventGridPublisher> _eventGridPublisherMock;

    public string DbName => _dbName;
    public AppSettings AppSettings => _optionsMock.Object.Value;
    public Mock<IEventGridPublisher> EventGridPublisherMock => _eventGridPublisherMock;

    public ServiceFactory()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());
        _eventGridPublisherMock = new Mock<IEventGridPublisher>();

        _dbName = $"TestDb_{Guid.NewGuid()}";
        _dbContextFactoryMock = new Mock<IDbContextFactory<IntegrationContext>>();
        _dbContextFactoryMock
            .Setup(f => f.CreateDbContext())
            .Returns(() => TestDbContextHelper.GetInMemoryDbContext(_dbName));
    }

    public CouplingService CreateCouplingService()
    {
        var repository = new CouplingRepository(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<CouplingRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object);

        return new CouplingService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<CouplingService>>().Object,
            repository: repository);
    }

    public EntitySubscriberService CreateEntitySubscriberService()
    {
        var repository = new EntitySubscriberRepository(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<EntitySubscriberRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object);

        return new EntitySubscriberService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<EntitySubscriberService>>().Object,
            repository: repository);
    }

    public BatteryService CreateBatteryService()
    {
        var repository = new BatteryRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<BatteryRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new BatteryQueryBuilder());

        return new BatteryService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<BatteryService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public CategoryService CreateCategoryService()
    {
        var repository = new CategoryRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<CategoryRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new CategoryQueryBuilder());

        return new CategoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<CategoryService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public ClaimRepository CreateClaimRepository()
    {
        var repository = new ClaimRepository(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ClaimRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object);

        return repository;
    }

    public ClaimService CreateClaimService()
    {
        return new ClaimService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<ClaimService>>().Object,
            eventGridPublisher: _eventGridPublisherMock.Object,
            repository: CreateClaimRepository(),
            couplingService: CreateCouplingService(),
            entitySubscriberService: CreateEntitySubscriberService(),
            clinicService: CreateClinicService(),
            patientService: CreatePatientService(),
            payorService: CreatePayorService());
    }

    public ClinicService CreateClinicService()
    {
        var repository = new ClinicRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ClinicRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new ClinicQueryBuilder());

        return new ClinicService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ClinicService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public ColorService CreateColorService()
    {
        var repository = new ColorRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ColorRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new ColorQueryBuilder());

        return new ColorService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<ColorService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public ManufacturerService CreateManufacturerService()
    {
        var repository = new ManufacturerRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ManufacturerRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new ManufacturerQueryBuilder());

        return new ManufacturerService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<ManufacturerService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public PatientService CreatePatientService()
    {
        var repository = new PatientRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PatientRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new PatientQueryBuilder());

        return new PatientService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PatientService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public PayorRepositoryService CreatePayorRepository()
    {
        var repository = new PayorRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PayorRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new PayorQueryBuilder());

        return repository;
    }

    public PayorService CreatePayorService()
    {
        return new PayorService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PayorService>>().Object,
            repositoryService: CreatePayorRepository(),
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public PurchaseOrderRepository CreatePurchaseOrderRepository()
    {
        var repository = new PurchaseOrderRepository(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PurchaseOrderRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object);

        return repository;
    }

    public PurchaseOrderService CreatePurchaseOrderService()
    {
        return new PurchaseOrderService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<PurchaseOrderService>>().Object,
            eventGridPublisher: _eventGridPublisherMock.Object,
            repository: CreatePurchaseOrderRepository(),
            couplingService: CreateCouplingService(),
            entitySubscriberService: CreateEntitySubscriberService(),
            purchaseReceiptService: CreatePurchaseReceiptRepository(),
            clinicService: CreateClinicService(),
            vendorService: CreateVendorService(),
            productService: CreateProductService());
    }

    public PurchaseReceiptRepository CreatePurchaseReceiptRepository()
    {
        var repository = new PurchaseReceiptRepository(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<PurchaseReceiptRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object);

        return repository;
    }

    public PurchaseReceiptService CreatePurchaseReceiptService()
    {
        return new PurchaseReceiptService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<PurchaseReceiptService>>().Object,
            eventGridPublisher: _eventGridPublisherMock.Object,
            repository: CreatePurchaseReceiptRepository(),
            couplingService: CreateCouplingService(),
            entitySubscriberService: CreateEntitySubscriberService());
    }

    public ProductService CreateProductService()
    {
        var repository = new ProductRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ProductRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new ProductQueryBuilder());

        return new ProductService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ProductService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            categoryService: CreateCategoryService(),
            vendorService: CreateVendorService(),
            manufacturerService: CreateManufacturerService(),
            colorService: CreateColorService(),
            batteryService: CreateBatteryService(),
            productModelService: CreateProductModelService(),
            taxGroupService: CreateTaxGroupService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public ProductModelService CreateProductModelService()
    {
        var repository = new ProductModelRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ProductModelRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new ProductModelQueryBuilder());

        return new ProductModelService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<ProductModelService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public SalesInvoiceRepository CreateSalesInvoiceRepository()
    {
        var repository = new SalesInvoiceRepository(
            logger: new Mock<ILogger<SalesInvoiceRepository>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new SalesInvoiceQueryBuilder(),
            mapper: new SalesInvoiceMapper());

        return repository;
    }

    public SalesInvoiceService CreateSalesInvoiceService()
    {
        return new SalesInvoiceService(
            options: _optionsMock.Object,
            logger: new Mock<ILogger<SalesInvoiceService>>().Object,
            eventGridPublisher: _eventGridPublisherMock.Object,
            repository: CreateSalesInvoiceRepository(),
            couplingService: CreateCouplingService(),
            entitySubscriberService: CreateEntitySubscriberService(),
            clinicService: CreateClinicService(),
            patientService: CreatePatientService(),
            productService: CreateProductService(),
            claimService: CreateClaimService());
    }

    public TaxGroupService CreateTaxGroupService()
    {
        var repository = new TaxGroupRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<TaxGroupRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new TaxGroupQueryBuilder());

        return new TaxGroupService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<TaxGroupService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }

    public VendorService CreateVendorService()
    {
        var repository = new VendorRepositoryService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<VendorRepositoryService>>().Object,
            dbContextFactory: _dbContextFactoryMock.Object,
            queryBuilder: new VendorQueryBuilder());

        return new VendorService(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<VendorService>>().Object,
            repositoryService: repository,
            entitySubscriberService: CreateEntitySubscriberService(),
            couplingService: CreateCouplingService(),
            eventGridPublisher: _eventGridPublisherMock.Object);
    }
}
