﻿using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Vendors;

public class VendorTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly VendorService _vendorService;

    public VendorTests()
    {
        _serviceFactory = new ServiceFactory();
        _vendorService = _serviceFactory.CreateVendorService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnVendor()
    {
        // Act
        var result = await _vendorService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "VDR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("A4B111B1-9BB8-430B-9264-1C693F106FD5"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnVendor()
    {
        // Act
        var result = await _vendorService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-VDR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("A4B111B1-9BB8-430B-9264-1C693F106FD5"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _vendorService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "VDR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("A4B111B1-9BB8-430B-9264-1C693F106FD5"), result.Id);
    }
}

