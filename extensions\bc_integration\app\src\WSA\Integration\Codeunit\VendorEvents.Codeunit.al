namespace WSA.Integration;

using Microsoft.Purchases.Vendor;

codeunit 50136 "Vendor Events"
{
    TableNo = "Vendor";

    trigger OnRun()
    begin
        RaiseEvent(Rec);
    end;

    [EventSubscriber(ObjectType::Table, Database::Vendor, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(var Rec: Record Vendor; RunTrigger: Boolean)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if not RunTrigger then
            exit;

        if RetailIntegrationSetup."Handle Events Asyncronously" then
            RaiseEventAsync(Rec)
        else
            RaiseEvent(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Vendor, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Vendor;
        var xRec: Record Vendor;
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if not RunTrigger then
            exit;

        if not HasChanged(Rec, xRec) then
            exit;

        if RetailIntegrationSetup."Handle Events Asyncronously" then
            RaiseEventAsync(Rec)
        else
            RaiseEvent(Rec);
    end;

    local procedure RaiseEventAsync(Vendor: Record Vendor)
    var
        SessionId: Integer;

    begin
        Session.StartSession(SessionId, Codeunit::"Vendor Events", CompanyName, Vendor);
    end;

    local procedure RaiseEvent(Vendor: Record Vendor)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Vendor."No." = '' then
            exit;

        if Vendor.Name = '' then
            exit;

        if IsNullGuid(Vendor.SystemId) then
            exit;

        JObject := Common.VendorToJson(Vendor);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'vendors', Format(Vendor.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;


    local procedure HasChanged(
         Rec: Record "Vendor";
         xRec: Record "Vendor"): Boolean

    begin
        if xRec."No." = '' then
            exit(false);

        if Rec.Name <> xRec.Name then
            exit(true);

        if Rec.Address <> xRec.Address then
            exit(true);

        if Rec."Address 2" <> xRec."Address 2" then
            exit(true);

        if Rec."Country/Region Code" <> xRec."Country/Region Code" then
            exit(true);

        if Rec.County <> xRec.County then
            exit(true);

        if Rec."Post Code" <> xRec."Post Code" then
            exit(true);

        if Rec."Phone No." <> xRec."Phone No." then
            exit(true);

        if Rec."E-Mail" <> xRec."E-Mail" then
            exit(true);

        if Rec."Our Account No." <> xRec."Our Account No." then
            exit(true);

        if Rec."Integrate with POS" <> xRec."Integrate with POS" then
            exit(true);
    end;
}
