using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Countries;

public class PimCountryBrandConfiguration : IEntityTypeConfiguration<PimCountryBrandEntity>
{
    public void Configure(EntityTypeBuilder<PimCountryBrandEntity> builder)
    {
        builder.ToTable("CountryBrand", "pim");

        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureIdentifiableFields();
        builder.ConfigureAuditInfoFields();

        builder.Property(x => x.CountryId)
            .HasColumnName("CountryId")
            .HasColumnType("uniqueidentifier")
            .IsRequired();

        builder.Property(x => x.BrandId)
            .HasColumnName("BrandId")
            .HasColumnType("uniqueidentifier")
            .IsRequired();

        builder.HasOne(x => x.CountryEntity)
            .WithMany()
            .HasForeignKey(x => x.CountryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.BrandEntity)
            .WithMany()
            .HasForeignKey(x => x.BrandId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}