namespace WSA.Integration;

using Microsoft.Inventory.Item.Catalog;

pageextension 50115 "Manufacturers" extends Manufacturers
{

    layout
    {
        addlast(Control1)
        {
            field(address; Rec.Address)
            {
                ApplicationArea = All;
                Visible = false;
            }

            field(address2; Rec."Address 2")
            {
                ApplicationArea = All;
                Visible = false;
            }

            field(city; Rec.City)
            {
                ApplicationArea = All;
            }

            field(County; Rec.County)
            {
                ApplicationArea = All;
            }

            field("Country/Region Code"; Rec."Country/Region Code")
            {
                ApplicationArea = All;
                Visible = false;
            }

            field(postCode; Rec."Post Code")
            {
                ApplicationArea = All;
                Visible = false;
            }

            field("Phone No."; Rec."Phone No.")
            {
                ApplicationArea = All;
                Visible = false;
            }

            field("E-Mail"; Rec."E-Mail")
            {
                ApplicationArea = All;
                Visible = false;
            }

            field(externalCode; Rec."External Code")
            {
                ApplicationArea = All;
                Visible = false;
                Editable = false;
            }
        }
    }
}
