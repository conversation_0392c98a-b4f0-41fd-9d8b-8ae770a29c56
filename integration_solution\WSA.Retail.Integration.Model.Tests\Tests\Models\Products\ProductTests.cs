﻿using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Products;

public class ProductTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly ProductService _productService;

    public ProductTests()
    {
        _serviceFactory = new ServiceFactory();
        _productService = _serviceFactory.CreateProductService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnProduct()
    {
        // Act
        var result = await _productService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "PROD1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8501A296-E71E-4A79-9320-7D255206B914"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnPatient()
    {
        // Act
        var result = await _productService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-PROD1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8501A296-E71E-4A79-9320-7D255206B914"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _productService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "PROD1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8501A296-E71E-4A79-9320-7D255206B914"), result.Id);
    }
}

