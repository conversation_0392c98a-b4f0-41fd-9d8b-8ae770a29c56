﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;

namespace WSA.Retail.Integration.Data.Repositories;

public class CouplingRepository(
    IOptions<AppSettings> appSettings,
    ILogger<CouplingRepository> logger,
    IDbContextFactory<IntegrationContext> dbContextFactory) 
    : ICouplingRepository
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly ILogger<CouplingRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    protected readonly IDbContextFactory<IntegrationContext> _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));

    public async Task<Coupling?> GetAsync(
        string externalSystemCode,
        string entityCode,
        Guid? recordId = null,
        string? externalCode = null)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        using var context = _dbContextFactory.CreateDbContext();
        return await BuildQuery(
            context, 
            externalSystemCode, 
            entityCode, 
            recordId, 
            externalCode).FirstOrDefaultAsync();
    }

    private static IQueryable<Coupling> BuildQuery(
        IntegrationContext context,
        string externalSystemCode,
        string entityCode,
        Guid? recordId = null,
        string? externalCode = null)
    {
        var query = from e in context.CouplingEntity
                    join ex in context.ExternalSystemEntity on e.ExternalSystemId equals ex.Id
                    join en in context.EntityEntity on e.EntityId equals en.Id
                    where (ex.Code == externalSystemCode)
                    where (en.Code == entityCode)
                    where (recordId == null || e.RecordId == recordId)
                    where (string.IsNullOrWhiteSpace(externalCode) || e.ExternalRecordId == externalCode)

                    select new Coupling
                    {
                        Id = e.Id,
                        ExternalSystem = new()
                        {
                            Id = ex.Id,
                            Code = ex.Code,
                            Name = ex.Name
                        },
                        Entity = new()
                        {
                            Id = en.Id,
                            Code = en.Code,
                            Name = en.Name
                        },
                        RecordId = e.RecordId,
                        ExternalCode = e.ExternalRecordId,
                        CreatedOn = e.CreatedOn,
                        ModifiedOn = e.ModifiedOn
                    };

        return query.AsNoTracking();
    }

    public async Task<bool> InsertAsync(Coupling coupling)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        using var context = _dbContextFactory.CreateDbContext();

        if (coupling.ExternalSystem?.Id == null && coupling.ExternalSystem?.Code != null)
        {
            var externalSystemId = await context.ExternalSystemEntity
                .Where(x => x.Code == coupling.ExternalSystem.Code)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (externalSystemId != null)
            {
                coupling.ExternalSystem.Id = externalSystemId;
            }
        }

        if (coupling.Entity?.Id == null && coupling.Entity?.Code != null)
        {
            var entityId = await context.EntityEntity
                .Where(x => x.Code == coupling.Entity.Code)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (entityId != Guid.Empty)
            {
                coupling.Entity.Id = entityId;
            }
        }

        try
        {
            var entity = coupling.ToEntity();
            context.CouplingEntity.Add(entity);
            await context.SaveChangesAsync();
            coupling.Id = entity.Id;
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex, $"Encountered a database error while inserting Coupling record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }

    public async Task<bool> UpdateAsync(Coupling coupling)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (coupling.Id == Guid.Empty)
        {
            _logger.LogCustomWarning(_appSettings.AppName,
                $"Coupling {coupling.RecordId}, {coupling.ExternalCode} cannot be updated because it has a null or missing Id.");
            return false;
        }

        using var context = _dbContextFactory.CreateDbContext();

        // Retrieve the existing Coupling
        var existingCoupling = await context.CouplingEntity
            .FirstOrDefaultAsync(x => x.Id == coupling.Id);

        if (existingCoupling == null)
        {
            _logger.LogWarning("Coupling with ID {Id} not found for update.", coupling.Id);
            return false;
        }

        context.Entry(existingCoupling).CurrentValues.SetValues(coupling.ToEntity());

        try
        {
            await context.SaveChangesAsync();
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex, $"A database error while updating Coupling record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex, _appSettings.AppName);
            return false;
        }
    }
}