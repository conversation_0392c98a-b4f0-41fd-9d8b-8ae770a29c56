using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductConfiguration : IEntityTypeConfiguration<PimProductEntity>
    {
        public void Configure(EntityTypeBuilder<PimProductEntity> builder)
        {
            builder.ToTable("Product", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();
            builder.ConfigureIntegrationFields();

            builder.Property(x => x.ColorId)
                .HasColumnName("ColorId")
                .HasColumnType("uniqueidentifier");

            builder.Property(x => x.IsAvailable)
                .HasColumnName("IsAvailable")
                .HasColumnType("bit");

            builder.Property(x => x.IsPhasedOut)
                .HasColumnName("IsPhasedOut")
                .HasColumnType("bit");

            builder.Property(x => x.ListPrice)
                .HasColumnName("ListPrice")
                .HasColumnType("decimal(18,4)");

            builder.Property(x => x.ProductSource)
                .HasColumnName("ProductSource")
                .HasColumnType("nvarchar(20)");

            builder.Property(x => x.ProductType)
                .HasColumnName("ProductType")
                .HasColumnType("nvarchar(20)");

            builder.Property(x => x.Ranking)
                .HasColumnName("Ranking")
                .HasColumnType("int");

            builder.Property(x => x.ReleaseDate)
                .HasColumnName("ReleaseDate")
                .HasColumnType("datetime2(7)");

            builder.Property(x => x.Sku)
                .HasColumnName("Sku")
                .HasColumnType("nvarchar(50)");

            builder.Property(x => x.State)
                .HasColumnName("State")
                .HasColumnType("nvarchar(20)");

            builder.Property(x => x.CreatedAt)
                .HasColumnName("CreatedAt")
                .HasColumnType("datetime2(7)");

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("UpdatedAt")
                .HasColumnType("datetime2(7)");

            builder.HasOne(x => x.PimColorEntity)
                .WithMany()
                .HasForeignKey(x => x.ColorId);

            builder
                .HasMany(p => p.ChildProducts)
                .WithMany(p => p.ParentProducts)
                .UsingEntity<PimProductParentEntity>(
                    j => j
                        .HasOne(pp => pp.PimChildProductEntity)
                        .WithMany()
                        .HasForeignKey(pp => pp.ProductId),
                    j => j
                        .HasOne(pp => pp.PimParentProductEntity)
                        .WithMany()
                        .HasForeignKey(pp => pp.ParentId),
                    j =>
                    {
                        j.ToTable("ProductParent", "pim");
                        j.HasKey(pp => pp.Id);
                    }
                );

            builder
                .HasMany(b => b.Brands)
                .WithMany(b => b.Products)
                .UsingEntity<PimProductBrandEntity>(
                    j => j
                        .HasOne(pb => pb.BrandEntity)
                        .WithMany()
                        .HasForeignKey(pb => pb.BrandId),
                    j => j
                        .HasOne(pb => pb.ProductEntity)
                        .WithMany()
                        .HasForeignKey(pb => pb.ProductId),
                    j =>
                    {
                        j.HasKey(pb => pb.Id);
                        j.ToTable("ProductBrand", "pim");
                    }
                );

            builder
                .HasMany(c => c.Categories)
                .WithMany(c => c.Products)
                .UsingEntity<PimProductCategoryEntity>(
                    j => j
                        .HasOne(pc => pc.CategoryEntity)
                        .WithMany()
                        .HasForeignKey(pc => pc.CategoryId),
                    j => j
                        .HasOne(pc => pc.ProductEntity)
                        .WithMany()
                        .HasForeignKey(pc => pc.ProductId),
                    j =>
                    {
                        j.HasKey(pc => pc.Id);
                        j.ToTable("ProductCategory", "pim");
                    }
                );

            builder
                .HasMany(p => p.BundleSets)
                .WithMany(b => b.Products)
                .UsingEntity<PimProductBundleSetEntity>(
                    j => j
                        .HasOne(pb => pb.BundleSetEntity)
                        .WithMany()
                        .HasForeignKey(pb => pb.BundleSetId),
                    j => j
                        .HasOne(pb => pb.ProductEntity)
                        .WithMany()
                        .HasForeignKey(pb => pb.ProductId),
                    j =>
                    {
                        j.HasKey(pb => pb.Id);
                        j.ToTable("ProductBundleSet", "pim");
                    }
                );

            builder.HasMany(x => x.Images)
                .WithOne(y => y.ProductEntity)
                .HasForeignKey(y => y.ProductId);
        }
    }
}