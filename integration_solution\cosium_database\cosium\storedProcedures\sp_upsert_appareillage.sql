﻿CREATE   PROCEDURE [cosium].[sp_upsert_appareillage]
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Step 1: Handle date conversions and deduplication
        WITH CleanData AS (
            SELECT 
                id,
                refappareil,
                hstatut,
                NULLIF(datedebut, '') AS datedebut,
                NULLIF(datefin, '') AS datefin,
                numpiece,
                hcommentaire,
                hrefclient,
                hcote,
                NULLIF(fingarantie, '') AS fingarantie,
                NULLIF(finextgarantie, '') AS finextgarantie,
                prescripteur,
                refuser,
                NULLIF(dateprescription, '') AS dateprescription,
                NULLIF(dateappareillage, '') AS dateappareillage,
                NULLIF(prixvendu, '') AS prixvendu,
                origine,
                NULLIF(datemodif, '') AS datemodif,
                ROW_NUMBER() OVER (PARTITION BY id ORDER BY 
                    CASE WHEN datemodif IS NULL OR datemodif = '' THEN '1900-01-01' 
                    ELSE datemodif END DESC) AS rn
            FROM [cosium].[stg_appareillage]
        )
        
        -- Step 2: Perform MERGE operation
        MERGE [cosium].[appareillage] AS target
        USING (
            SELECT * FROM CleanData WHERE rn = 1
        ) AS source
        ON target.id = source.id
        
        WHEN MATCHED AND (
            ISNULL(target.refappareil, '') <> ISNULL(source.refappareil, '') OR
            ISNULL(target.hstatut, '') <> ISNULL(source.hstatut, '') OR
            ISNULL(target.datedebut, '') <> ISNULL(source.datedebut, '') OR
            ISNULL(target.datefin, '') <> ISNULL(source.datefin, '') OR
            ISNULL(target.numpiece, '') <> ISNULL(source.numpiece, '') OR
            ISNULL(target.hcommentaire, '') <> ISNULL(source.hcommentaire, '') OR
            ISNULL(target.hrefclient, '') <> ISNULL(source.hrefclient, '') OR
            ISNULL(target.hcote, '') <> ISNULL(source.hcote, '') OR
            ISNULL(target.fingarantie, '') <> ISNULL(source.fingarantie, '') OR
            ISNULL(target.finextgarantie, '') <> ISNULL(source.finextgarantie, '') OR
            ISNULL(target.prescripteur, '') <> ISNULL(source.prescripteur, '') OR
            ISNULL(target.refuser, '') <> ISNULL(source.refuser, '') OR
            ISNULL(target.dateprescription, '') <> ISNULL(source.dateprescription, '') OR
            ISNULL(target.dateappareillage, '') <> ISNULL(source.dateappareillage, '') OR
            ISNULL(target.prixvendu, '') <> ISNULL(source.prixvendu, '') OR
            ISNULL(target.origine, '') <> ISNULL(source.origine, '') OR
            ISNULL(target.datemodif, '') <> ISNULL(source.datemodif, '')
        )
        THEN UPDATE SET
            target.refappareil = source.refappareil,
            target.hstatut = source.hstatut,
            target.datedebut = source.datedebut,
            target.datefin = source.datefin,
            target.numpiece = source.numpiece,
            target.hcommentaire = source.hcommentaire,
            target.hrefclient = source.hrefclient,
            target.hcote = source.hcote,
            target.fingarantie = source.fingarantie,
            target.finextgarantie = source.finextgarantie,
            target.prescripteur = source.prescripteur,
            target.refuser = source.refuser,
            target.dateprescription = source.dateprescription,
            target.dateappareillage = source.dateappareillage,
            target.prixvendu = source.prixvendu,
            target.origine = source.origine,
            target.datemodif = source.datemodif,
            target.ModifiedOn = SYSUTCDATETIME()
            
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (
                id, refappareil, hstatut, datedebut, datefin, numpiece, 
                hcommentaire, hrefclient, hcote, fingarantie, finextgarantie,
                prescripteur, refuser, dateprescription, dateappareillage,
                prixvendu, origine, datemodif, CreatedOn, ModifiedOn
            )
            VALUES (
                source.id, source.refappareil, source.hstatut, source.datedebut, 
                source.datefin, source.numpiece, source.hcommentaire, 
                source.hrefclient, source.hcote, source.fingarantie, 
                source.finextgarantie, source.prescripteur, source.refuser, 
                source.dateprescription, source.dateappareillage, source.prixvendu, 
                source.origine, source.datemodif, SYSUTCDATETIME(), SYSUTCDATETIME()
            );
        
        -- Step 3: Log results
        DECLARE @RowsInserted INT = (SELECT COUNT(*) FROM [cosium].[stg_appareillage] s
                                    WHERE NOT EXISTS (
                                        SELECT 1 FROM [cosium].[appareillage] t 
                                        WHERE t.id = s.id
                                    ));
        DECLARE @RowsUpdated INT = @@ROWCOUNT - @RowsInserted;
        
        -- Optional: Clear staging table
        TRUNCATE TABLE [cosium].[stg_appareillage];
        
        COMMIT TRANSACTION;
        
        -- Return summary
        SELECT 
            'Success' AS Status,
            @RowsInserted AS RowsInserted,
            @RowsUpdated AS RowsUpdated,
            GETDATE() AS ProcessedTime;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Return error details
        SELECT 
            'Error' AS Status,
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            ERROR_LINE() AS ErrorLine,
            GETDATE() AS ErrorTime;
    END CATCH
END
GO