﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoiceQueryBuilder : 
    IDocumentQueryBuilder<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>
{
    public IQueryable<SalesInvoice> BuildQuery(
        IntegrationContext context,
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        var latestCouplings = context.CouplingEntity
            .Where(c => context.ExternalSystemEntity
                .Any(es => es.Id == c.ExternalSystemId && es.Code == externalSystemCode));

        var query = from x in context.SalesInvoiceEntity
            .Include(x => x.PatientEntity)
            .Include(x => x.ClinicEntity)
            .Include(x => x.Lines)
                .ThenInclude(line => line == null ? null : line.ProductEntity)

                    where (id == null || x.Id == id)
                    where (documentNumber == null || x.DocumentNumber == documentNumber)
                    where (documentDate == null || x.DocumentDate == documentDate)
                    where (string.IsNullOrWhiteSpace(externalReference) ||
                            latestCouplings.Any(c => c.RecordId == x.Id && c.ExternalRecordId == externalReference))

                    let latestCoup = latestCouplings
                        .Where(c => c.RecordId == x.Id)
                        .OrderByDescending(c => c.ModifiedOn)
                        .Select(c => c.ExternalRecordId)
                        .FirstOrDefault()

                    let latestSoCoup = latestCouplings
                        .Where(c => c.RecordId == x.SalesOrderId)
                        .OrderByDescending(c => c.ModifiedOn)
                        .Select(c => c.ExternalRecordId)
                        .FirstOrDefault()

                    let latestPatCoup = latestCouplings
                        .Where(c => c.RecordId == x.PatientId)
                        .OrderByDescending(c => c.ModifiedOn)
                        .Select(c => c.ExternalRecordId)
                        .FirstOrDefault()

                    let latestCliCoup = latestCouplings
                        .Where(c => c.RecordId == x.ClinicId)
                        .OrderByDescending(c => c.ModifiedOn)
                        .Select(c => c.ExternalRecordId)
                        .FirstOrDefault()

                    orderby (x.DocumentNumber)

                    select new SalesInvoice
                    {
                        Id = x.Id,
                        ExternalSystemCode = externalSystemCode,
                        DocumentNumber = x.DocumentNumber,
                        ExternalReference = latestCoup,
                        AlternateNumber = x.AlternateNumber,
                        Patient = x.PatientEntity != null ? new()
                        {
                            Id = x.PatientId,
                            Code = x.PatientEntity != null ? x.PatientEntity!.Code : null,
                            ExternalCode = latestPatCoup,
                            Name = x.PatientEntity != null ? x.PatientEntity!.Name : null
                        } : null,
                        Clinic = x.ClinicEntity != null ? new()
                        {
                            Id = x.ClinicId,
                            Code = x.ClinicEntity != null ? x.ClinicEntity!.Code : null,
                            ExternalCode = latestCliCoup,
                            Name = x.ClinicEntity != null ? x.ClinicEntity!.Name : null
                        } : null,
                        DocumentDate = x.DocumentDate,
                        Lines = x.Lines
                        .Select(line => new SalesInvoiceLine
                        {
                            Id = line.Id,
                            ExternalReference = latestCouplings
                                .Where(c => c.RecordId == line.Id)
                                .OrderByDescending(c => c.ModifiedOn)
                                .Select(c => c.ExternalRecordId)
                                .FirstOrDefault(),
                            Sequence = line.Sequence,
                            Product = line.ProductEntity != null ? new ExternalReference()
                            {
                                Id = line.ProductId,
                                Code = line.ProductEntity != null ? line.ProductEntity!.Code : null,
                                Name = line.ProductEntity != null ? line.ProductEntity!.Name : null,
                                ExternalCode = line.ProductId.HasValue ? latestCouplings
                                    .Where(c => c.RecordId == line.ProductId)
                                    .OrderByDescending(c => c.ModifiedOn)
                                    .Select(c => c.ExternalRecordId)
                                    .FirstOrDefault() : null
                            } : null,
                            Description = line.Description,
                            Quantity = line.Quantity,
                            SerialNumber = line.SerialNumber,
                            UnitPrice = line.UnitPrice,
                            GrossAmount = line.GrossAmount,
                            DiscountAmount = line.DiscountAmount,
                            AmountExclTax = line.AmountExclTax,
                            TaxAmount = line.TaxAmount,
                            AmountInclTax = line.AmountInclTax,
                            CreatedOn = line.CreatedOn,
                            ModifiedOn = line.ModifiedOn
                        }).ToList(),
                        CreatedOn = x.CreatedOn,
                        ModifiedOn = x.ModifiedOn
                    };

        return query.AsNoTracking();
    }
}
