# Purchase Transaction Entities Diagram

This diagram shows the purchase transaction entities and their relationships in the integration database.

```mermaid
erDiagram
    PurchaseOrder ||--o{ PurchaseOrderLine : "contains"
    PurchaseOrder ||--o{ PurchaseReceipt : "received as"
    
    PurchaseReceipt ||--o{ PurchaseReceiptLine : "contains"
    
    PurchaseReturn ||--o{ PurchaseReturnLine : "contains"
    
    Vendor ||--o{ PurchaseOrder : "receives"
    Clinic ||--o{ PurchaseOrder : "places"
    
    Product ||--o{ PurchaseOrderLine : "ordered in"
    Product ||--o{ PurchaseReceiptLine : "received in"
    Product ||--o{ PurchaseReturnLine : "returned in"
    
    PurchaseShipment ||--o{ PurchaseShipmentLine : "contains"
    
    PurchaseOrder {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID VendorId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    PurchaseOrderLine {
        UUID Id PK
        UUID PurchaseOrderId FK
        int Sequence
        UUID ProductId FK
        string Description
        decimal Quantity
        decimal UnitCost
        decimal LineAmount
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    PurchaseReceipt {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID PurchaseOrderId FK
        UUID VendorId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    PurchaseReceiptLine {
        UUID Id PK
        UUID PurchaseReceiptId FK
        int Sequence
        UUID PurchaseOrderLineId FK
        UUID ProductId FK
        string Description
        decimal Quantity
        string SerialNumber
        decimal UnitCost
        decimal LineAmount
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    PurchaseReturn {
        UUID Id PK
        string DocumentNumber
        string AlternateNumber
        UUID PurchaseReceiptId FK
        UUID VendorId FK
        UUID ClinicId FK
        date DocumentDate
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    PurchaseReturnLine {
        UUID Id PK
        UUID PurchaseReturnId FK
        int Sequence
        UUID PurchaseReceiptLineId FK
        UUID ProductId FK
        string Description
        decimal Quantity
        string SerialNumber
        decimal UnitCost
        decimal LineAmount
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
```