﻿using WSA.Retail.Integration.PIM.Models.Brands;

namespace WSA.Retail.Integration.PIM.Models.Countries;

public class PimCountryBrandEntity : IPimCountryBrandEntity
{
    public required Guid Id { get; set; } = Guid.Empty;
    public required Guid CountryId { get; set; }
    public required Guid BrandId { get; set; }
    public required DateTime SystemCreatedOn { get; set; }
    public required DateTime SystemModifiedOn { get; set; }

    public required virtual PimCountryEntity CountryEntity { get; set; } = null!;
    public required virtual PimBrandEntity BrandEntity { get; set; } = null!;
}