using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReturns;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPurchaseOrderProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPurchaseOrderProcessor> logger,
    ISimplyPOHeaderRepository simplyPOHeaderRepository,
    IPurchaseOrderService purchaseOrderService,
    IPurchaseReturnService purchaseReturnService,
    IVendorService vendorService)
    : ISimplyPurchaseOrderProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPurchaseOrderProcessor> _logger = logger;
    private readonly ISimplyPOHeaderRepository _simplyPOHeaderRepository = simplyPOHeaderRepository;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IPurchaseReturnService _purchaseReturnService = purchaseReturnService;
    private readonly IVendorService _vendorService = vendorService;
    public enum PurchaseOrderType
    {
        PO,
        RO
    }

    public async Task ProcessSimplyPurchaseTransactionsAsync()
    {
        _logger.LogMethodStart();

        var records = await _simplyPOHeaderRepository.GetIntegrationRecordsAsync();
        if (records == null || records.Count == 0)
        {
            _logger.LogCustomInformation("No purchase order records found for integration");
            return ;
        }

        _logger.LogCustomInformation($"Found {records.Count} purchase order records for integration");
        await Parallel.ForEachAsync(
            records,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (record, cancellationToken) =>
            {
                await ProcessSimplyPurchaseTransactionAsync(record);
            });

    }

    public async Task<bool> ProcessSimplyPurchaseTransactionAsync(SimplyPOHeaderEntity record)
    {
        _logger.LogMethodStart();
        if (record.POType == PurchaseOrderType.PO.ToString())
        {
            return await ProcessSimplyPurchaseOrderAsync(record);
        }

        if (record.POType == PurchaseOrderType.RO.ToString())
        {
            return await ProcessSimplyPurchaseReturnAsync(record);
        }

        return false;
    }

    public async Task<bool> ProcessSimplyPurchaseOrderAsync(SimplyPOHeaderEntity record)
    {
        _logger.LogMethodStart();
        var purchaseOrder = record.ToPurchaseOrder();
        purchaseOrder.ExternalSystemCode = _appSettings.ExternalSystemCode;
                    
        // Set document number
        purchaseOrder.DocumentNumber = GetDocumentNumber(PurchaseOrderType.PO, record.PONumber);
                    
        // Add detail lines
        if (record.Details != null)
        {
            foreach (var detail in record.Details)
            {
                var line = detail.ToPurchaseLine();
                purchaseOrder.Lines.Add(line);
            }
        }
                    
        // Resolve external references
        await _purchaseOrderService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, purchaseOrder);
                    
        // Verify required data
        bool isValid = VerifyRequiredData(purchaseOrder);
        if (!isValid)
        {
            _logger.LogCustomError($"Purchase order {record.PONumber} has missing required data");
            return false;
        }
                    
        // Upsert the purchase order
        var result = await _purchaseOrderService.UpsertAsync(purchaseOrder);
        if (result != null)
        {
            // Update integration status
            await _simplyPOHeaderRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed purchase order {record.PONumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process purchase order {record.PONumber}");
            return false;
        }
    }

    public async Task<bool> ProcessSimplyPurchaseReturnAsync(SimplyPOHeaderEntity record)
    {
        _logger.LogMethodStart();
        var purchaseReturn = record.ToPurchaseReturn();
        purchaseReturn.ExternalSystemCode = _appSettings.ExternalSystemCode;

        // Set document number
        purchaseReturn.DocumentNumber = GetDocumentNumber(PurchaseOrderType.RO, record.PONumber);

        // Add detail lines
        if (record.Details != null)
        {
            foreach (var detail in record.Details)
            {
                var line = detail.ToPurchaseReturnLine();
                purchaseReturn.PurchaseReturnLines.Add(line);
            }
        }

        // Resolve external references
        await _purchaseReturnService.ValidateExternalReferencesAsync(_appSettings.ExternalSystemCode, purchaseReturn);

        // Verify required data
        bool isValid = VerifyRequiredData(purchaseReturn);
        if (!isValid)
        {
            _logger.LogCustomError($"Purchase return {record.PONumber} has missing required data");
            return false;
        }

        // Upsert the purchase order
        var result = await _purchaseReturnService.UpsertAsync(purchaseReturn);
        if (result != null)
        {
            // Update integration status
            await _simplyPOHeaderRepository.UpdateIntegrationStatusAsync(record.Id);
            _logger.LogCustomInformation($"Successfully processed purchase return {record.PONumber}");
            return true;
        }
        else
        {
            _logger.LogCustomError($"Failed to process purchase return {record.PONumber}");
            return false;
        }

    }

    private string GetDocumentNumber(PurchaseOrderType type, int poNumber)
    {
        string prefix = type == PurchaseOrderType.PO ? "PORD-" : "PRO-";
        return $"{prefix}{poNumber.ToString().PadLeft(10, '0')}";
    }
    
    private bool VerifyRequiredData(PurchaseOrder purchaseOrder)
    {
        if (string.IsNullOrEmpty(purchaseOrder.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }
        
        if (purchaseOrder.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }
        
        if (purchaseOrder.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }
        
        if (purchaseOrder.Vendor?.Code == null)
        {
            _logger.LogCustomError("Vendor.Code is required");
            return false;
        }
        
        if (purchaseOrder.Lines == null || purchaseOrder.Lines.Count == 0)
        {
            _logger.LogCustomError("At least one purchase order line is required");
            return false;
        }
        
        foreach (var line in purchaseOrder.Lines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }
            
            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }
        
        return true;
    }

    private bool VerifyRequiredData(PurchaseReturn purchaseReturn)
    {
        if (string.IsNullOrEmpty(purchaseReturn.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }

        if (purchaseReturn.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }

        if (purchaseReturn.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (purchaseReturn.Vendor?.Code == null)
        {
            _logger.LogCustomError("Vendor.Code is required");
            return false;
        }

        if (purchaseReturn.PurchaseReturnLines == null || purchaseReturn.PurchaseReturnLines.Count == 0)
        {
            _logger.LogCustomError("At least one purchase return line is required");
            return false;
        }

        foreach (var line in purchaseReturn.PurchaseReturnLines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }

            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }

        return true;
    }
}