﻿using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Core;

public interface IPriced
{
    public decimal? UnitPrice { get; set; }
    public decimal? GrossAmount { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal? AmountExclTax { get; set; }
    public decimal? TaxAmount { get; set; }
    public decimal? AmountInclTax { get; set; }

    public bool HasChanges(IPriced? oldEntity)
    {
        if (oldEntity == null) return true;
        if (!Common.AreEqual(UnitPrice, oldEntity.UnitPrice)) return true;
        if (!Common.AreEqual(GrossAmount, oldEntity.GrossAmount)) return true;
        if (!Common.AreEqual(DiscountAmount, oldEntity.DiscountAmount)) return true;
        if (!Common.AreEqual(AmountExclTax, oldEntity.AmountExclTax)) return true;
        if (!Common.AreEqual(TaxAmount, oldEntity.TaxAmount)) return true;
        if (!Common.AreEqual(AmountInclTax, oldEntity.AmountInclTax)) return true;
        return false;
    }
}
