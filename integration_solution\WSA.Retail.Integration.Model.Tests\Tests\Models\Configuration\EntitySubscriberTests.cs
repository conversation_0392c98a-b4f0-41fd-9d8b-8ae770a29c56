﻿using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Configuration;

public class EntitySubscriberRepositoryTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly EntitySubscriberService _entitySubscriberService;

    public EntitySubscriberRepositoryTests()
    {
        _serviceFactory = new ServiceFactory();
        _entitySubscriberService = _serviceFactory.CreateEntitySubscriberService();
    }

    [Fact]
    public async Task GetAsync_ShouldReturnSubscriber()
    {
        // Act
        var result = await _entitySubscriberService.GetAsync(
            externalSystemCode: "TEST1",
            entityCode: EntityType.Patient.GetEntityCode());

        // Assert
        Assert.NotNull(result);
    }
}