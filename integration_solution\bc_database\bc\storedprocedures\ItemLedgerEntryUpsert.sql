﻿CREATE PROCEDURE [bc].[ItemLedgerEntryUpsert]
WITH EXECUTE AS OWNER
AS
BEGIN  
    BEGIN TRY
    SET XACT_ABORT ON
    BEGIN TRANSACTION

    UPDATE dest
     
       SET CompanyId = IIF(dest.ExternalCompanyId IS DISTINCT FROM source.CompanyId, NULL, dest.CompanyId),
           EntryNumber = source.EntryNo,
           JournalBatchName = source.JournalBatchName,
           EntryType = REPLACE(REPLACE(source.EntryType, '_x0020_', ' '), '_x002E_', '.'),
           ItemId = IIF(dest.ExternalItemId IS DISTINCT FROM source.ItemId, NULL, dest.ItemId),
           ExternalItemId = source.ItemId,
           ItemNumber = source.ItemNo,
           LocationId = IIF(dest.ExternalLocationId IS DISTINCT FROM source.LocationId, NULL, dest.LocationId),
           ExternalLocationId = source.LocationId,
           LocationCode = source.LocationCode,
           PostingDate = source.PostingDate,
           DocumentDate = source.DocumentDate,
           DocumentType = REPLACE(REPLACE(source.DocumentType, '_x0020_', ' '), '_x002E_', '.'),
           DocumentNumber = source.DocumentNo,
           ExternalDocumentNumber = source.ExternalDocumentNo,
           Description = source.Description,
           [Open] = source.[Open],
           Quantity = source.Quantity,
           SerialNumber = source.SerialNo,
           DimensionSetId = IIF(dest.ExternalDimensionSetNumber IS DISTINCT FROM source.DimensionSetId, NULL, dest.DimensionSetId),
           ExternalDimensionSetNumber = source.DimensionSetId,
           ExternalCreatedOn = source.SystemCreatedAt,
           ExternalModifiedOn = source.SystemModifiedAt,
           ModifiedOn = SYSUTCDATETIME()

      FROM bc.ItemLedgerEntry AS dest

     INNER JOIN bc.ItemLedgerEntry_Staging AS source
        ON dest.ExternalCompanyId = source.CompanyId
       AND dest.ExternalId = source.Id

     WHERE dest.ExternalModifiedOn < source.SystemModifiedAt
        OR dest.ExternalModifiedOn IS NULL;

    INSERT INTO bc.ItemLedgerEntry (
           ExternalCompanyId,
           ExternalId,
           EntryNumber,
           JournalBatchName,
           EntryType,
           ExternalItemId,
           ItemNumber,
           ExternalLocationId,
           LocationCode,
           PostingDate,
           DocumentDate,
           DocumentType,
           DocumentNumber,
           ExternalDocumentNumber,
           Description,
           [Open],
           Quantity,
           SerialNumber,
           ExternalDimensionSetNumber,
           ExternalCreatedOn,
           ExternalModifiedOn)

    SELECT source.CompanyId AS ExternalCompanyId,
           source.Id AS ExternalId,
           source.EntryNo AS EntryNumber,
           source.JournalBatchName AS JournalBatchName,
           REPLACE(REPLACE(source.EntryType, '_x0020_', ' '), '_x002E_', '.'),
           source.ItemId AS ExternalItemId,
           source.ItemNo AS ItemNumber,
           source.LocationId AS ExternalLocationId,
           source.LocationCode,
           source.PostingDate,
           source.DocumentDate,
           REPLACE(REPLACE(source.DocumentType, '_x0020_', ' '), '_x002E_', '.'),
           source.DocumentNo AS DocumentNumber,
           source.ExternalDocumentNo AS ExternalDocumentNumber,
           source.Description,
           source.[Open],
           source.Quantity,
           source.SerialNo AS SerialNumber,
           source.DimensionSetId AS ExternalDimensionSetNumber,
           source.SystemCreatedAt AS ExternalCreatedOn,
           source.SystemModifiedAt AS ExternalModifiedOn

      FROM bc.ItemLedgerEntry_Staging AS source

      WHERE NOT EXISTS (
            SELECT 1
            FROM bc.ItemLedgerEntry AS dest
            WHERE dest.ExternalCompanyId = source.CompanyId
              AND dest.ExternalId = source.Id
    )
           
    TRUNCATE TABLE bc.ItemLedgerEntry_Staging;
    COMMIT TRANSACTION;
                
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END