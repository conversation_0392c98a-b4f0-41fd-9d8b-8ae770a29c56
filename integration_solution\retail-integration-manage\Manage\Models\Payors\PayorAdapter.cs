﻿using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Payors;

namespace WSA.Retail.Integration.Manage.Models.Payors;

public class PayorAdapter : IEventHubEntityAdapter<PayorEventHubEvent, Payor>
{
    public Payor ToDomainModel(
        PayorEventHubEvent source,
        string externalSystemCode)
    {
        if (string.IsNullOrWhiteSpace(source.PayorCode))
        {
            throw new ArgumentNullException(nameof(source),
                $"{source.GetType().Name} {nameof(source.Id)}: {source.Id} " +
                $"contains a null or empty {nameof(source.PayorCode)}.");
        }

        return new Payor()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id.ToString(),
            AlternateCode = source.Code,
            Code = source.PayorCode,
            Name = source.Name,
            Address = source.Address?.Address1,
            Address2 = source.Address?.Address2,
            City = source.Address?.City,
            Region = source.Address?.State,
            Country = source.Address?.Country,
            PostalCode = source.Address?.PostCode,
            Phone = source.Address?.PhoneNumber,
            Email = source.Address?.Email
        };
    }
}