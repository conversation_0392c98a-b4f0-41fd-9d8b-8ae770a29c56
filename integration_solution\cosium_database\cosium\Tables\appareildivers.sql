﻿CREATE TABLE [cosium].[appareildivers] (
    [id]                       NVARCHAR (50) NOT NULL,
    [description]              NVARCHAR (50) NULL,
    [typeobjet]                NVARCHAR (50) NULL,
    [type<PERSON><PERSON>e]              NVARCHAR (50) NULL,
    [codecomptable]            NVARCHAR (50) NULL,
    [codecomptableachat]       NVARCHAR (50) NULL,
    [codecomptableremise]      NVARCHAR (50) NULL,
    [codecomptableachatremise] NVARCHAR (50) NULL,
    [codecomptabletva]         NVARCHAR (50) NULL,
    [codecomptabletvaachat]    NVARCHAR (50) NULL,
    [codecomplementaire]       NVARCHAR (50) NULL,
    [datemodif]                NVARCHAR (50) NULL,
    [code]                     NVARCHAR(50) NULL,
    [CreatedOn]                DATETIME2 CONSTRAINT [DF_appareildivers_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]               DATETIME2 CONSTRAINT [DF_appareildivers_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].appareildivers_UpdateModified
ON [cosium].[appareildivers]
AFTER UPDATE 
AS
   UPDATE [cosium].[appareildivers]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[appareildivers].[id] = i.[id]
GO