using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Vendors
{
    public class VendorEntityConfiguration : IEntityTypeConfiguration<VendorEntity>
    {
        public void Configure(EntityTypeBuilder<VendorEntity> builder)
        {
            builder.ToTable("Vendor", "dbo");

            builder.<PERSON><PERSON><PERSON>(v => v.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAddressFields();
            builder.ConfigureContactFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(v => v.AccountNo)
                .HasColumnName("AccountNo")
                .HasColumnType("nvarchar(20)")
                .HasMaxLength(20);

            builder.Property(v => v.IntegrateWithPOS)
                .HasColumnName("IntegrateWithPOS")
                .HasColumnType("bit");
        }
    }
}