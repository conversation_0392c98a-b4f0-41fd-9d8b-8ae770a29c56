﻿using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.PurchaseReceipts;

public class OrderLineItemAcceptedEventEntity : IOrderLineItemAcceptedEventEntity
{
    public Guid Id { get; set; }
    public required string EventType { get; set; }
    public Guid MessageId { get; set; }
    public Guid OrderId { get; set; }
    public Guid LineItemId { get; set; }
    public string? ExternalNumber { get; set; }
    public DateTime Timestamp { get; set; }
    public DateTime LocalTimestamp { get; set; }
    public required string BlobPath { get; set; }
    public EventProcessingStatus Status { get; set; }
    public DateTime? ProcessedOn { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
}