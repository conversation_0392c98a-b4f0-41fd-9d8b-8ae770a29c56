﻿CREATE PROCEDURE [bc].[SalesCreditUpsert]
(
    @loadId NVARCHAR(40)
)
WITH EXECUTE AS OWNER
AS
BEGIN  
    BEGIN TRY
    SET XACT_ABORT ON
    BEGIN TRANSACTION

    UPDATE dest
     
       SET CompanyId = IIF(dest.ExternalCompanyId IS DISTINCT FROM source.CompanyId, NULL, dest.CompanyId),
           DocumentNumber = source.DocumentNumber,
           ExternalDocumentNumber = source.ExternalDocumentNumber,
           DocumentDate = source.DocumentDate,
           PostingDate = source.PostingDate,
           CustomerId = IIF(dest.ExternalCustomerId IS DISTINCT FROM source.CustomerId, NULL, dest.CustomerId),
           ExternalCustomerId = source.CustomerId,
           SellToCustomerId = IIF(dest.ExternalSellToCustomerId IS DISTINCT FROM source.SellToCustomerId, NULL, dest.SellToCustomerId),
           ExternalSellToCustomerId = source.SellToCustomerId,
           ResponsibilityCenterId = IIF(dest.ExternalResponsibilityCenterId IS DISTINCT FROM source.ResponsibilityCenterId, NULL, dest.ResponsibilityCenterId),
           ExternalResponsibilityCenterId = source.ResponsibilityCenterId,
           CurrencyId = IIF(dest.ExternalCurrencyId IS DISTINCT FROM source.CurrencyId, NULL, dest.CurrencyId),
           ExternalCurrencyId = source.CurrencyId,
           PaymentTermsId = IIF(dest.ExternalPaymentTermsId IS DISTINCT FROM source.PaymentTermsId, NULL, dest.PaymentTermsId),
           ExternalPaymentTermsId = source.PaymentTermsId,
           DimensionSetId = IIF(dest.ExternalDimensionSetNumber IS DISTINCT FROM source.DimensionSetId, NULL, dest.DimensionSetId),
           ExternalDimensionSetNumber = source.DimensionSetId,
           AmountExcludingTax = source.AmountExcludingTax,
           AmountIncludingTax = source.AmountIncludingTax,
           ExternalCreatedOn = source.SystemCreatedAt,
           ExternalModifiedOn = source.SystemModifiedAt,
           ModifiedOn = SYSUTCDATETIME()

      FROM bc.SalesCredit AS dest

     INNER JOIN bc.SalesCredit_Staging AS source
        ON dest.ExternalCompanyId = source.CompanyId
       AND dest.ExternalId = source.Id
       AND source.ETL_LoadId = @loadId

     WHERE dest.ExternalModifiedOn < source.SystemModifiedAt
        OR dest.ExternalModifiedOn IS NULL;

    INSERT INTO bc.SalesCredit(
           ExternalCompanyId,
           ExternalId,
           DocumentNumber,
           ExternalDocumentNumber,
           DocumentDate,
           PostingDate,
           ExternalCustomerId,
           ExternalSellToCustomerId,
           ExternalResponsibilityCenterId,
           ExternalCurrencyId,
           ExternalPaymentTermsId,
           ExternalDimensionSetNumber,
           AmountExcludingTax,
           AmountIncludingTax,
           ExternalCreatedOn,
           ExternalModifiedOn)

    SELECT source.CompanyId AS ExternalCompanyId,
           source.Id AS ExternalId,
           source.DocumentNumber,
           source.ExternalDocumentNumber,
           source.DocumentDate,
           source.PostingDate,
           source.CustomerId AS ExternalCustomerId,
           source.SellToCustomerId AS ExternalSellToCustomerId,
           source.ResponsibilityCenterId AS ExternalResponsibilityCenterId,
           source.CurrencyId AS ExternalCurrencyId,
           source.PaymentTermsId AS ExternalPaymentTermsId,
           source.DimensionSetId AS ExternalDimensionSetNumber,
           source.AmountExcludingTax,
           source.AmountIncludingTax,
           source.SystemCreatedAt AS ExternalCreatedOn,
           source.SystemModifiedAt AS ExternalModifiedOn

      FROM bc.SalesCredit_Staging AS source

      WHERE source.ETL_LoadId = @loadId
        AND NOT EXISTS (
            SELECT 1
            FROM bc.SalesCredit AS dest
            WHERE dest.ExternalCompanyId = source.CompanyId
              AND dest.ExternalId = source.Id
    )
           
    TRUNCATE TABLE bc.SalesCredit_Staging;
    COMMIT TRANSACTION;
           
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH

END