{"profiles": {"we-dev": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-wedev.westeurope-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "yyIEHEjpWLFkOXXbGzoajWxWBH64Htuo+vdyGaa+uE4=", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-we-dev.database.windows.net,1433;Initial Catalog=we-dev;Persist Security Info=False;User ID=manage;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationwedev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}, "au_uat": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-auuat.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "CB2Rch114kqTx4i63EgRWtpl7qOoxr1PbGhkq8mqLZv1vyWqtpInJQQJ99BCACL93NaXJ3w3AAABAZEGYr5G", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au-uat;Persist Security Info=False;User ID=manage;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauuat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}, "au_uat3": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-auuat3.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "yzP3yZvnxSJsFkBA2oUSeErxn0aXbHkrBaLyQAwsn0MhdTGnBfHBJQQJ99BCACL93NaXJ3w3AAABAZEGxNjk", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au-uat3;Persist Security Info=False;User ID=pim;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauuat3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}, "au_prod": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-auprod.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "pEues3BSCszYLvhOCdK4JBcxwDfTPvrYIdiIqRh18uIHbzK1YbK6JQQJ99BEACL93NaXJ3w3AAABAZEG26x4", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au_prod;Persist Security Info=False;User ID=pim;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}, "nz_uat3": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-nzuat3.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "EOedOSOyPRhqLw4LQpvlQSaeWjvqmOKSS48lTWntCoLy8wjGDUdtJQQJ99BCACL93NaXJ3w3AAABAZEG5rPS", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-nz.database.windows.net,1433;Initial Catalog=nz-uat3;Persist Security Info=False;User ID=pim;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationnzuat3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}, "nz_prod": {"commandName": "Project", "commandLineArgs": "--port 7171", "launchBrowser": false, "environmentVariables": {"AppName": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-nzprod.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "F9RO3UxqCzLobmDJZHxYM6kBp2rqZgfepEytOqTVgIo2gtZMsaHXJQQJ99BEACL93NaXJ3w3AAABAZEGCFhg", "ExternalSystemCode": "PIM", "FromStorageQueueName": "from-pim", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-nz.database.windows.net,1433;Initial Catalog=nz_prod;Persist Security Info=False;User ID=pim;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationnzprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-pim", "EnableDetailedDbLogging": "false", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************"}}}}