# Simply Integration Project - Architecture Documentation

This documentation provides an overview of the architecture and design of the retail-integration-simply project.

## Purpose

The Simply Integration project is responsible for synchronizing data between the Simply retail system and the central integration platform. It handles both master data (patients/customers) and transaction data (sales invoices, purchase orders, payments).

## Architecture Diagrams

### Class Diagrams

1. [Core Architecture Diagram](Simply_Core_Architecture_Diagram.md) - Overview of the core components and their relationships
2. [Database Entity Diagram](Simply_Database_Entity_Diagram.md) - Database entities used in the integration
3. [Integration Flow Diagram](Simply_Integration_Flow_Diagram.md) - How data flows through the integration components

### Sequence Diagrams

1. [Patient Integration Sequence](Simply_Patient_Integration_Sequence.md) - The process of integrating patient data
2. [Transaction Integration Sequence](Simply_Transaction_Integration_Sequence.md) - The process of integrating transaction data

## Key Components

### Event Processor

The `FromSimplyEventProcessor` is the entry point for the integration. It is triggered by a timer and orchestrates the integration process for both master data and transactions.

### Handlers

Handlers like `PatientHandler` process specific entity types. They contain the business logic for data validation, mapping, and integration.

### Repositories

Repositories provide data access to the Simply database. They encapsulate the queries and updates needed for the integration process.

### Database Entities

The project uses Entity Framework Core to model and access the Simply database entities. These entities include the data to be integrated as well as tracking fields for the integration process.

## Integration Flow

The integration follows a unidirectional flow from Simply to the central system:

1. **Extraction**: Data is extracted from the Simply database with `IntegrationRequired = true`
2. **Transformation**: Data is mapped to the central system's model
3. **Reference Resolution**: External references (e.g., patients, clinics, products) are resolved
4. **Loading**: Data is sent to the central system through appropriate services
5. **Status Update**: Integration status is updated in the Simply database

This pattern ensures that data flows efficiently and maintains integrity between the systems.