﻿namespace WSA.Retail.Integration.Core;

public interface IDocumentLine :
    IIdentifiable,
    ISequenced,
    IAuditInfo
{
    public bool HasChanges(IDocumentLine? oldEntity)
    {
        if (((ISequenced)this).HasChanges(oldEntity)) return true;
        return false;
    }

    public IEnumerable<string> ValidateRequiredProperties()
    {
        var errors = new List<string>();
        if (Sequence == null || Sequence == 0)
            errors.Add("Sequence is required.");

        return errors;
    }
}
