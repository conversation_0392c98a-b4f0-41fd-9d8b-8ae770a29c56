namespace WSA.Integration;

using Microsoft.Inventory.Item.Catalog;

page 50177 "WSA Manufacturer List"
{
    ApplicationArea = All;
    Caption = 'Manufacturer List';
    PageType = List;
    SourceTable = Manufacturer;
    UsageCategory = Lists;
    Editable = false;
    CardPageId = 50178;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Code; Rec.Code)
                {
                }

                field(address; Rec.Address)
                {
                }

                field(address2; Rec."Address 2")
                {
                    Visible = false;
                }

                field(city; Rec.City)
                {
                }

                field(County; Rec.County)
                {
                }

                field("Country/Region Code"; Rec."Country/Region Code")
                {
                    Visible = false;
                }

                field(postCode; Rec."Post Code")
                {
                }

                field("Phone No."; Rec."Phone No.")
                {
                    Visible = false;
                }

                field("E-Mail"; Rec."E-Mail")
                {
                    Visible = false;
                }

                field(externalCode; Rec."External Code")
                {
                    Visible = false;
                    Editable = false;
                }
            }
        }
    }
}
