using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.PurchaseReceipts;

public class SimplyPurchaseReceiptConfiguration : IEntityTypeConfiguration<SimplyPurchaseReceiptEntity>
{
    public void Configure(EntityTypeBuilder<SimplyPurchaseReceiptEntity> builder)
    {
        builder.ToTable("PurchaseReceipt");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.TotalQuantity)
            .HasColumnName("TotalQuantity")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.UnitCost)
            .HasColumnName("UnitCost")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.ItemNumber)
            .HasColumnName("ItemNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.LineNumber)
            .HasColumnName("LineNumber")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.DocNumber)
            .HasColumnName("DocNumber")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.OrderType)
            .HasColumnName("OrderType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(false);

        builder.Property(x => x.TransactionDate)
            .HasColumnName("TransactionDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        builder.Property(x => x.SerialNumber)
            .HasColumnName("SerialNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(225)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(false)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(false)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true);

        builder.Property(x => x.IntegrationDate)
            .HasColumnName("IntegrationDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);
    }
}