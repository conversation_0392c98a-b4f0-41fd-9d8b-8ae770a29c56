﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Manage.Core;

namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class SalesConfiguration : IEntityTypeConfiguration<SalesEntity>
{
    public void Configure(EntityTypeBuilder<SalesEntity> builder)
    {
        builder.ToTable("Sales");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureManageIdField();
        builder.ConfigureManageInvoiceIdentifersFields();
        builder.ConfigureAuditInfoFields();
    }
}
