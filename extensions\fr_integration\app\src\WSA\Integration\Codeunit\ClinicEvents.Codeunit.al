namespace WSA.Integration.API.V1;

using Microsoft.Inventory.Location;
using WSA.Integration;

codeunit 50106 "Clinic Events"
{
    [EventSubscriber(ObjectType::Table, Database::"Responsibility Center", OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record "Responsibility Center";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendClinicToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::"Responsibility Center", OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record "Responsibility Center";
        var xRec: Record "Responsibility Center";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            if HasChanged(Rec, xRec) then
                SendClinicToIntegrationAPI(Rec);
    end;



    local procedure SendClinicToIntegrationAPI(ResponsibilityCenter: Record "Responsibility Center")
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if ResponsibilityCenter.Code = '' then
            exit;

        if ResponsibilityCenter.Name = '' then
            exit;

        if IsNullGuid(ResponsibilityCenter.SystemId) then
            exit;

        JObject.Add('code', ResponsibilityCenter.Code);
        JObject.Add('externalCode', Format(ResponsibilityCenter.SystemId).TrimStart('{').TrimEnd('}'));
        JObject.Add('name', ResponsibilityCenter.Name);
        JObject.Add('address', ResponsibilityCenter.Address);
        JObject.Add('address2', ResponsibilityCenter."Address 2");
        JObject.Add('city', ResponsibilityCenter.City);
        JObject.Add('region', ResponsibilityCenter.County);
        JObject.Add('country', ResponsibilityCenter."Country/Region Code");
        JObject.Add('postalCode', ResponsibilityCenter."Post Code");
        JObject.Add('phone', ResponsibilityCenter."Phone No.");

        if not IntegrationManagement.TrySendToApi(JObject, 'clinics') then
            exit;
    end;


    local procedure HasChanged(
        Rec: Record "Responsibility Center";
        xRec: Record "Responsibility Center"): Boolean

    begin
        if xRec.Code = '' then
            exit(false);

        if Rec.Name <> xRec.Name then
            exit(true);

        if Rec.Address <> xRec.Address then
            exit(true);

        if Rec."Address 2" <> xRec."Address 2" then
            exit(true);

        if Rec."Country/Region Code" <> xRec."Country/Region Code" then
            exit(true);

        if Rec.County <> xRec.County then
            exit(true);

        if Rec."Post Code" <> xRec."Post Code" then
            exit(true);
    end;
}