﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisBundleSetToProductResponseOutputType
{
    [JsonPropertyName("bundleSetId")] public Guid BundleSetId { get; set; } = Guid.Empty;
    [JsonPropertyName("createdAt")] public DateTime CreatedAt { get; set; }
    [JsonPropertyName("isAvailable")] public bool IsAvailable { get; set; }
    [JsonPropertyName("isDefault")] public bool IsDefault { get; set; }
    [JsonPropertyName("isPhasedOut")] public bool IsPhasedOut { get; set; }
    [JsonPropertyName("productId")] public Guid ProductId { get; set; }
    [JsonPropertyName("ranking")] public int Ranking { get; set; }
    [JsonPropertyName("sku")] public string? Sku { get; set; }
    [JsonPropertyName("state")] public StateOutputType? State { get; set; }
    [JsonPropertyName("updatedAt")] public DateTime UpdatedAt { get; set; }
}