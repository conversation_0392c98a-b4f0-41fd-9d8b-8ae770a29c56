﻿using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Categories;

public class PimCategoryEntity : IPimCategoryEntity
{
    public required Guid Id { get; set; } = Guid.Empty;
    public string? Name { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public DateTime SystemCreatedOn { get; set; }
    public DateTime SystemModifiedOn { get; set; }

    public virtual PimCategoryEntity? ParentCategory { get; set; }
    public virtual ICollection<PimProductEntity> Products { get; set; } = [];
}
