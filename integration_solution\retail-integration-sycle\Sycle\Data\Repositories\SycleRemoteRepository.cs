using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Sycle.Models.Remotes;

namespace WSA.Retail.Integration.Sycle.Data.Repositories;

public class SycleRemoteRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SycleRemoteRepository> logger,
    IDbContextFactory<SycleContext> dbContextFactory) : ISycleRemoteRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SycleRemoteRepository> _logger = logger;
    private readonly IDbContextFactory<SycleContext> _dbContextFactory = dbContextFactory;
       

    public async Task<List<SycleRemote>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetIntegrationRecordsAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entities = await context.SycleRemoteEntity
            .Where(r => r.IntegrationRequired)
            .ToListAsync();

        var remotes = entities.Select(e => new SycleRemote
        {
            RemoteId = e.RemoteId,
            RemoteUuid = e.RemoteUuid,
            ClinicId = e.ClinicId,
            Manufacturer = e.Manufacturer,
            Model = e.Model,
            Type = e.Type,
            WarrantyLength = e.WarrantyLength,
            BasePrice = e.BasePrice,
            ProviderCost = e.ProviderCost,
            ActualCost = e.ActualCost,
            SalesTax = e.SalesTax,
            CptCode = e.CptCode,
            LastUpdate = e.LastUpdate
        }).ToList();

        return remotes ?? [];
    }

    public async Task<SycleRemote?> GetAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleRemoteEntity
            .FirstOrDefaultAsync(r => r.RemoteId == id);

        if (entity == null)
        {
            return null;
        }

        var remote = new SycleRemote
        {
            RemoteId = entity.RemoteId,
            RemoteUuid = entity.RemoteUuid,
            ClinicId = entity.ClinicId,
            Manufacturer = entity.Manufacturer,
            Model = entity.Model,
            Type = entity.Type,
            WarrantyLength = entity.WarrantyLength,
            BasePrice = entity.BasePrice,
            ProviderCost = entity.ProviderCost,
            ActualCost = entity.ActualCost,
            SalesTax = entity.SalesTax,
            CptCode = entity.CptCode,
            LastUpdate = entity.LastUpdate
        };

        return remote;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpdateIntegrationStatusAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleRemoteEntity
            .FirstOrDefaultAsync(r => r.RemoteId == id);

        if (entity == null)
        {
            return false;
        }

        entity.IntegrationRequired = false;
        entity.IntegratedOn = DateTime.UtcNow;
        entity.ModifiedOn = DateTime.UtcNow;

        await context.SaveChangesAsync();

        return true;
    }
}