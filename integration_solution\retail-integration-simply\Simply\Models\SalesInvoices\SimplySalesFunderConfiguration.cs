using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesFunderConfiguration : IEntityTypeConfiguration<SimplySalesFunderEntity>
{
    public void Configure(EntityTypeBuilder<SimplySalesFunderEntity> builder)
    {
        builder.ToTable("SalesFunder");

        builder.Has<PERSON>ey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.SalesInvoiceId)
            .HasColumnName("SalesInvoiceId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.SalesType)
            .HasColumnName("SalesType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(true);

        builder.Property(x => x.InvoiceNumber)
            .HasColumnName("InvoiceNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.Line)
            .HasColumnName("Line")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.FunderID)
            .HasColumnName("FunderID")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.Amount)
            .HasColumnName("Amount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true);

        builder.Property(x => x.IntegrationDate)
            .HasColumnName("IntegrationDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        // Set up foreign key relationship
        builder.HasOne(x => x.Header)
            .WithMany(h => h.Funders)
            .HasForeignKey(x => x.SalesInvoiceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}