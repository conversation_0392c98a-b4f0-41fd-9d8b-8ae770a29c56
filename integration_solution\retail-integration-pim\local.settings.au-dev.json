{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "SqlConnectionString": "Server=tcp:retail-integration-au-dev.database.windows.net,1433;Initial Catalog=au-dev;Persist Security Info=False;User ID=simply;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "Schedule1": "0 0 0 * * Sun", "Schedule2": "0 0 1 * * Sun", "Schedule3": "0 0 1 * * Sun", "ExternalSystemCode": "PIM", "EventGridEndpoint": "https://wsa-retail-integration-audev.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "6EzW3t4fUBCrLK1C4MMvEx0KgqiZUsTSqpRZmcnSYsVuDE5kIg7ZJQQJ99ALACL93NaXJ3w3AAABAZEGWHg7", "PimBaseUrl": "https://app-pis-prod-westeurope-api-prod.azurewebsites.net/graphql", "PimClientId": "8ca10926-18bd-4f8e-91c2-560da7c8d941", "PimClientSecret": "****************************************", "PageSize": "100", "PimCountryCode": "AU", "PimBrandCode": "0014"}}