﻿namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductEventHubEvent
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? CategoryName { get; set; }
    public Guid? CategoryId { get; set; }
    public string? CategoryCode { get; set; }
    public Guid? ManufacturerId { get; set; }
    public Guid? HearingAidTypeId { get; set; }
    public string? HearingAidTypeName { get; set; }
    public bool? IsSerialized { get; set; }
    public bool? AutoDelivery { get; set; }
    public bool? PriceChangesAllowed { get; set; }
    public string? Warranty { get; set; }
    public string? LdWarranty { get; set; }
    public bool? IsSellable { get; set; }
    public bool? IsActive { get; set; }
    public decimal? RetailPrice { get; set; }
    public bool? IsHsp { get; set; }
    public string? Hsp { get; set; }
    public List<Guid>? BatteryTypes { get; set; }
    public List<Guid>? Colors { get; set; }
    public List<Guid>? Attributes { get; set; }
}