﻿CREATE TABLE [cosium].[codemailing](
	[id] [int] NOT NULL,
	[codemailing] [varchar](50) NULL,
	[datemodif] [varchar](50) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
	[type] [varchar](50) NULL,
	[description] [varchar](max) NULL,
 CONSTRAINT [PK_codemailing] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [cosium].[codemailing] ADD  CONSTRAINT [DF_codemailing_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[codemailing] ADD  CONSTRAINT [DF_codemailing_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO
