﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Runtime.CompilerServices;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorManageIntegrator(
    IOptions<AppSettings> appSettings,
    ILogger<VendorManageIntegrator> logger,
    IVendorService vendorService,
    IEntitySubscriberService subscriberService,
    IManageRequestAdapter<AuSupplierRequest, Vendor> requestAdapter,
    IManageResponseAdapter<AuSupplierResponse, Vendor> responseAdapter,
    ManageAPI manageAPI) : 
        GenericManageIntegrator<
            IVendorService, 
            Vendor, 
            AuSupplierRequest, 
            AuSupplierResponse, 
            SupplierResponsePagingResponse, 
            IEntitySubscriberService>(
                appSettings: appSettings,
                domainService: vendorService,
                requestAdapter: requestAdapter,
                responseAdapter: responseAdapter,
                entitySubscriberService: subscriberService,
                manageAPI: manageAPI,
                entityType: EntityType.Vendor),
        IVendorManageIntegrator
{
    private readonly ILogger<VendorManageIntegrator> _logger = logger;


    protected override void LogMethodStart([CallerMemberName] string? callingMethod = null)
    {
        _logger.LogMethodStart(
            methodName: callingMethod!);
    }
    protected override void LogCustomInformation(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomInformation(
            message: message, 
            methodName: callingMethod!);
    }
    protected override void LogCustomWarning(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomWarning(
            message: message, 
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex, 
            message: message, 
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex, 
            methodName: callingMethod!);
    }


    protected override async Task<AuSupplierResponse?> GetEntity(Guid id)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.SuppliersGET2Async(id);
    }

    protected override async Task<SupplierResponsePagingResponse?> GetEntityList()
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.SuppliersGETAsync(1, 1000);
    }

    protected override async Task<CreatedResponse?> PostEntity(AuSupplierRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.SuppliersPOSTAsync(request);
    }

    protected override async Task PutEntity(Guid id, AuSupplierRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        await _manageApi.SuppliersPUTAsync(id, request);
    }

    protected override ICollection<AuSupplierResponse> GetEntitiesFromEntityList(SupplierResponsePagingResponse responseList)
    {
        return responseList.Data;
    }

    protected override Task AfterConvertDomainEntityToPostRequest(AuSupplierRequest request, Vendor entity)
    {
        if (string.IsNullOrEmpty(request.PhoneNumber))
        {
            request.PhoneNumber = "unspecified";
        }

        return Task.CompletedTask;
    }

    protected override Task AfterConvertDomainEntityToPutRequest(AuSupplierRequest request, AuSupplierResponse existingManageEntity, Vendor entity)
    {
        request.Website = existingManageEntity.Website;
        request.SalesContact = existingManageEntity.SalesContact;
        request.AccountReceivableContact = existingManageEntity.AccountReceivableContact;
        request.IsActive = existingManageEntity.IsActive;
                              
        if ((request.Country ?? "") == "")
        {
            request.Country = Environment.GetEnvironmentVariable("DefaultCountry");
        }

        if (string.IsNullOrEmpty(request.PhoneNumber)) // Manage requires this
        {
            request.PhoneNumber = existingManageEntity.PhoneNumber;

            if (string.IsNullOrEmpty(request.PhoneNumber))
            {
                request.PhoneNumber = "unspecified";
            }
        }

        return Task.CompletedTask;
    }
}
