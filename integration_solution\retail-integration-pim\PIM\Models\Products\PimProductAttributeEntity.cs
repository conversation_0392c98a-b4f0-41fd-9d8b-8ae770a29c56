﻿using WSA.Retail.Integration.PIM.Models.Attributes;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductAttributeEntity : IPimProductAttributeEntity, WSA.Retail.Integration.Core.IIdentifiable
    {
        public Guid Id { get; set; } = Guid.Empty;
        public required Guid ProductId { get; set; }
        public required Guid AttributeId { get; set; }
        public string? Value { get; set; }
        public DateTime SystemCreatedOn { get; set; }
        public DateTime SystemModifiedOn { get; set; }

        public virtual PimProductEntity? PimProductEntity { get; set; }
        public virtual PimAttributeEntity? PimAttributeEntity { get; set; }
    }
}
