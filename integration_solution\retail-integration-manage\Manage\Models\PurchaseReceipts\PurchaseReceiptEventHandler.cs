﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Data;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.PurchaseReceipts;

public class PurchaseReceiptEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PurchaseReceiptEventHandler> logger,
    BlobServiceClient blobServiceClient,
    Dictionary<string, QueueClient> queueClients,
    ManageAPI manageApi,
    IDbContextFactory<ManageDbContext> contextFactory,
    IPurchaseReceiptService purchaseReceiptService,
    IPurchaseOrderService purchaseOrderService,
    IEntityService entityService) : 
        EventHandlerBase<OrderLineItemAcceptedEventHubEvent, PurchaseReceipt, OrderLineItemAcceptedEventEntity>(
            appSettings,
            logger,
            blobServiceClient,
            queueClients),
        IPurchaseReceiptEventHandler
{
    private readonly ManageAPI _manageApi = manageApi;
    private readonly IDbContextFactory<ManageDbContext> _contextFactory = contextFactory;
    private readonly IPurchaseReceiptService _purchaseReceiptService = purchaseReceiptService;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IEntityService _entityService = entityService;


    protected override async Task<PurchaseReceipt?> OnAfterGetModelDataFromManageApiAsync(OrderLineItemAcceptedEventHubEvent? eventData, PurchaseReceipt? modelData)
    {
        LogMethodStart();
        
        if (eventData == null)
        {
            LogCustomError("Event data is null");
            return null;
        }

        // Get order from the Manage API
        var apiOrder = await _manageApi.OrdersGET2Async(eventData.Order.Id);
        if (apiOrder == null)
        {
            LogCustomError($"Order with Id '{eventData.Order.Id}' not found in Manage API");
            return null;
        }

        // Get the purchase order domain model
        var purchaseOrder = await _purchaseOrderService.GetAsync(
            externalSystemCode: _appSettings.ExternalSystemCode,
            externalReference: eventData.Order.Id.ToString());

        if (purchaseOrder == null)
        {
            LogCustomError($"Purchase Order with external reference '{eventData.Order.Id}' not found");
            return null;
        }

        // Create a PurchaseReceipt from the OrderLineItemAccepted event
        var purchaseReceipt = eventData.ToPurchaseReceipt(_appSettings.ExternalSystemCode);
        if (purchaseReceipt == null)
        {
            LogCustomError("Failed to convert OrderLineItemAcceptedEventHubEvent to PurchaseReceipt");
            return null;
        }

        // Create external references
        purchaseReceipt.PurchaseOrder = new ExternalDocumentReference
        {
            Id = purchaseOrder.Id,
            ExternalReference = purchaseOrder.ExternalReference,
            DocumentNumber = purchaseOrder.DocumentNumber
        };

        purchaseReceipt.Clinic = purchaseOrder.Clinic;
        if (purchaseReceipt.Clinic?.ExternalCode == null && apiOrder.Location?.Id != null)
        {
            purchaseReceipt.Clinic = new ExternalReference
            {
                ExternalCode = apiOrder.Location.Id.ToString(),
            };
        }

        purchaseReceipt.Vendor = purchaseOrder.Vendor;
        if (purchaseReceipt.Vendor?.ExternalCode == null && apiOrder.Supplier?.Id != null)
        {
            purchaseReceipt.Vendor = new ExternalReference
            {
                ExternalCode = apiOrder.Supplier.Id.ToString(),
            };
        }
       
        // Find the matching purchase order line
        var poLine = purchaseOrder.Lines.FirstOrDefault(l => l.ExternalReference == eventData.Id.ToString());
        if (poLine == null)
        {
            var indexInApi = apiOrder.LineItems.ToList().FindIndex(l => l.Id == eventData.Id);
            if (indexInApi >= 0)
            {
                poLine = purchaseOrder.Lines.FirstOrDefault(l => l.Sequence == indexInApi + 1);
            }
        }

        if (poLine == null)
        { 
            LogCustomError($"Purchase Order line with external reference '{eventData.Id}' not found in PO '{purchaseOrder.DocumentNumber}'");
            return null;
        }
        
        // Create the purchase receipt line
        var receiptLine = new PurchaseReceiptLine
        {
            Sequence = 1,
            PurchaseOrderLine = new ExternalDocumentLineReference
            {
                Id = poLine.Id,
                Sequence = poLine.Sequence
            },
            Product = poLine.Product,
            Quantity = (decimal)(eventData.Quantity ?? 1),
            SerialNumber = eventData.SerialNumbers?.FirstOrDefault()
        };
        
        // Add the line to the receipt
        purchaseReceipt.PurchaseReceiptLines.Add(receiptLine);
        
        return await Task.FromResult(purchaseReceipt);
    }

    protected override async Task<PurchaseReceipt?> GetFromRepositoryAsync(PurchaseReceipt modelData)
    {
        LogMethodStart();
        try
        {
            PurchaseReceipt? existingPurchaseReceipt = null;
            
            if (modelData.ExternalReference != null)
            {
                existingPurchaseReceipt = await _purchaseReceiptService.GetAsync(
                    externalSystemCode: _appSettings.ExternalSystemCode,
                    externalReference: modelData.ExternalReference);
            }

            // Set document number based on existing receipt or generate new one
            if (existingPurchaseReceipt != null)
            {
                modelData.DocumentNumber = existingPurchaseReceipt.DocumentNumber;
            }
            else
            {
                modelData.DocumentNumber = await _entityService.GetNextNumberAsync(EntityType.PurchaseReceipt.GetEntityCode());
            }

            return existingPurchaseReceipt;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }


    protected override async Task<PurchaseReceipt?> VerifyRequiredFieldsAsync(PurchaseReceipt modelData)
    {
        LogMethodStart();

        try
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.DocumentNumber, nameof(modelData.DocumentNumber));
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.PurchaseOrder?.DocumentNumber, $"{nameof(modelData.PurchaseOrder)}.{nameof(modelData.PurchaseOrder.DocumentNumber)}");
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Clinic?.Code, $"{nameof(modelData.Clinic)}.{nameof(modelData.Clinic.Code)}");
            ArgumentException.ThrowIfNullOrWhiteSpace(modelData.Vendor?.Code, $"{nameof(modelData.Vendor)}.{nameof(modelData.Vendor.Code)}");
            
            foreach (var line in modelData.PurchaseReceiptLines)
            {
                ArgumentNullException.ThrowIfNull(line.Quantity, nameof(line.Quantity));
                ArgumentException.ThrowIfNullOrWhiteSpace(line.Product?.Code, $"{nameof(line.Product)}.{nameof(line.Product.Code)}");
            }

            return await Task.FromResult(modelData);
        }
        catch(Exception ex)
        {
            LogCustomError(ex.Message);
            return null;
        }
    }

    protected override async Task<PurchaseReceipt?> UpsertAsync(PurchaseReceipt modelData)
    {
        LogMethodStart();
        try
        {
            var purchaseReceipt = await _purchaseReceiptService.UpsertAsync(modelData);
            if (purchaseReceipt == null)
            {
                LogCustomError($"Failed to upsert PurchaseReceipt {modelData.DocumentNumber}");
                return null;
            }
            else
            {
                return purchaseReceipt;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return null;
        }
    }

    protected override async Task<OrderLineItemAcceptedEventEntity?> LogMessageMetadataAsync(EventHubMessage message)
    {
        LogMethodStart();

        var msg = JsonSerializer.Serialize(message.Message, Common.GetJsonOptions());
        var orderLineItemAcceptedEvent = JsonSerializer.Deserialize<OrderLineItemAcceptedEventHubEvent>(msg!, Common.GetJsonOptions());
        if (orderLineItemAcceptedEvent == null)
        {
            LogCustomWarning("OrderLineItemAcceptedEventHubEvent deserialization failed.");
            return null;
        }

        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.OrderLineItemAcceptedEventEntity.Where(x => x.MessageId == message.MessageId).FirstOrDefaultAsync();
        if (eventEntity != null)
        {
            bool isDirty = false;

            if (eventEntity.OrderId != orderLineItemAcceptedEvent.Order.Id)
            {
                eventEntity.OrderId = orderLineItemAcceptedEvent.Order.Id;
                isDirty = true;
            }

            if (eventEntity.LineItemId != orderLineItemAcceptedEvent.Id)
            {
                eventEntity.LineItemId = orderLineItemAcceptedEvent.Id;
                isDirty = true;
            }

            if (eventEntity.Timestamp != message.Timestamp)
            {
                eventEntity.Timestamp = message.Timestamp;
                isDirty = true;
            }

            var timeZoneId = _appSettings.LocalTimeZone;
            var localTimestamp = TimeZoneHelper.ConvertToTimeZone(message.Timestamp, timeZoneId);
            if (eventEntity.LocalTimestamp != localTimestamp)
            {
                eventEntity.LocalTimestamp = localTimestamp;
                isDirty = true;
            }

            if (!string.IsNullOrWhiteSpace(message.BlobPath) && eventEntity.BlobPath != message.BlobPath)
            {
                eventEntity.BlobPath = message.BlobPath;
                isDirty = true;
            }

            if (isDirty)
            {
                eventEntity.ModifiedOn = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();
                return eventEntity;
            }
        }
        else
        {
            eventEntity = new OrderLineItemAcceptedEventEntity
            {
                Id = Guid.NewGuid(),
                EventType = message.Event,
                MessageId = message.MessageId,
                OrderId = orderLineItemAcceptedEvent.Order.Id,
                LineItemId = orderLineItemAcceptedEvent.Id,
                Timestamp = message.Timestamp,
                LocalTimestamp = message.Timestamp,
                BlobPath = message.BlobPath ?? string.Empty,
                Status = EventProcessingStatus.New
            };
            dbContext.OrderLineItemAcceptedEventEntity.Add(eventEntity);
            await dbContext.SaveChangesAsync();
            return eventEntity;
        }
        return null;
    }

    protected override async Task<OrderLineItemAcceptedEventEntity?> UpdateMessageLogStatusAsync(OrderLineItemAcceptedEventEntity log, EventProcessingStatus status)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.OrderLineItemAcceptedEventEntity
            .FirstOrDefaultAsync(x => x.Id == log.Id);
        if (eventEntity != null)
        {
            eventEntity.Status = status;
            eventEntity.ProcessedOn = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
            return eventEntity;
        }

        return default;
    }

    protected override async Task<OrderLineItemAcceptedEventEntity?> GetMetadataByMessageId(Guid messageId)
    {
        LogMethodStart();
        var dbContext = await _contextFactory.CreateDbContextAsync();
        var eventEntity = await dbContext.OrderLineItemAcceptedEventEntity
            .FirstOrDefaultAsync(x => x.Id == messageId);
        if (eventEntity != null)
        {
            return eventEntity;
        }

        return default;
    }

    [Function("RequeueOrderLineItemAcceptedEvent")]
    public async Task<IActionResult> RequeueOrderLineItemAcceptedEvent([HttpTrigger(AuthorizationLevel.Function, "post", Route = "requeueOrderLineItemAcceptedEvent/{id}")] HttpRequest req,
        Guid id)
    {
        LogMethodStart();

        if (id == Guid.Empty)
        {
            LogCustomError("Invalid ID provided for requeueing order event.");
            return new BadRequestObjectResult("Invalid ID provided.");
        }

        try
        {
            await RetryFromLogAsync(id);
            return new OkObjectResult($"Order event with ID {id} has been requeued successfully.");
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return new BadRequestObjectResult($"Failed to requeue order event: {ex.Message}");
        }
    }
}