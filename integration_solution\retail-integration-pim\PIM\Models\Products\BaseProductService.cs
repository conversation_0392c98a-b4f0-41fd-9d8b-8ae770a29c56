﻿using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.PIM.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.ProductModels;
using WSA.Retail.Integration.PIM.GraphQL;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Models.Couplings;

namespace WSA.Retail.Integration.PIM.Models.Products;

public class BaseProductService(
    IOptions<AppSettings> appSettings,
    ILogger<BaseProductService> logger,
    IPimProductRepository pimProductRepository,
    IBatteryService batteryService,
    ICategoryService categoryService,
    IColorService colorService,
    IManufacturerService manufacturerService,
    IProductModelService productModelService,
    IProductService productService,
    IVendorService vendorService,
    ICouplingService couplingService

    ) : IBaseProductService
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<BaseProductService> _logger = logger;
    private readonly IPimProductRepository _pimProductRepository = pimProductRepository;
    private readonly IBatteryService _batteryService = batteryService;
    private readonly ICategoryService _categoryService = categoryService;
    private readonly IManufacturerService _manufacturerService = manufacturerService;
    private readonly IVendorService _vendorService = vendorService;
    private readonly IColorService _colorService = colorService;
    private readonly IProductModelService _productModelService = productModelService;
    private readonly IProductService _productService = productService;
    private readonly ICouplingService _couplingService = couplingService;

    public async Task ProcessNewRecords()
    {
        _logger.LogMethodStart();

        var pimProducts = await _pimProductRepository.GetNotIntegratedProducts(500);
        foreach (var pimProduct in pimProducts)
        {
            await ProcessSingleRecord(pimProduct);
        }
    }

    public async Task ProcessSingleRecord(PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();
        
        // Don't updated products inactive in ProCAT
        if (pimProduct.State == StateOutputType.DELETED.ToString())
        {
            await _pimProductRepository.UpdateIntegrationStatusAsync(pimProduct);
            return;
        }
        // Don't update bundle products (just real orderable products).
        if (pimProduct.ProductType != ProductTypeOutputType.PRODUCT_VARIANT.ToString())
        {
            await _pimProductRepository.UpdateIntegrationStatusAsync(pimProduct);
            return;
        }

        ArgumentNullException.ThrowIfNull(pimProduct.Sku);
        Product product = new()
        {
            Code = pimProduct.Sku
        };

        product.ExternalSystemCode = _appSettings.ExternalSystemCode;
        product.ExternalCode = pimProduct.Id.ToString();
        product.Name = pimProduct.Name;
        product.VendorItemNo = pimProduct.Sku;
        product.GTIN = pimProduct.GetAttributeValue("ean_code");
        product.PimProductId = pimProduct.GetAttributeValue("plm_product_id");

        var pimImage = pimProduct.Images.Where(x => x.IsDefault == true).FirstOrDefault();
        if (pimImage != null && pimImage.CDNUrl != null)
        {
            product.ImageUrl = pimImage.CDNUrl;
        }

        await HandleCategoryAsync(product, pimProduct);
        await HandleManufacturer(product, pimProduct);
        await HandleVendor(product, pimProduct);
        await HandleColor(product, pimProduct);
        await HandleProductModel(product, pimProduct);
        await HandleBattery(product, pimProduct);

        ArgumentNullException.ThrowIfNullOrEmpty(product.Code);
        ArgumentNullException.ThrowIfNullOrEmpty(product.Name);
        if (product.Category == null)
            ArgumentNullException.ThrowIfNull(product.Category);
        if (product.Category.Code == null)
            ArgumentNullException.ThrowIfNullOrEmpty(product.Category.Code);
        
        var updatedProduct = await _productService.UpsertAsync(product);
        if (updatedProduct != null)
        {
            await _pimProductRepository.UpdateIntegrationStatusAsync(pimProduct);
        }
    }

    private async Task HandleCategoryAsync(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        var pimProductType = pimProduct.GetAttributeValue("pim_product_type");
        if (pimProductType != null)
        {
            var category = await _categoryService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimProductType);
            if (category != null)
            {
                product.Category = category;
                if (category.Code!.StartsWith("11") || category.Code!.StartsWith("12"))
                {
                    product.IsSerialized = true;
                    product.IsInventory = true;
                }
                if (category.Code!.StartsWith("13"))
                {
                    product.IsSerialized = false;
                    product.IsInventory = true;
                }
                if (category.Code!.StartsWith("14") || category.Code!.StartsWith('2'))
                {
                    product.IsSerialized = false;
                    product.IsInventory = false;
                }
            }
            else
            {
                ArgumentNullException.ThrowIfNull(product.Category!.Code);
            }

        }
    }

    private async Task HandleManufacturer(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        var pimManufacturer = pimProduct.GetAttributeValue("manufacturer");
        if (pimManufacturer != null)
        {
            var manufacturer = await _manufacturerService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimManufacturer);
            if (manufacturer != null)
            {
                product.Manufacturer = manufacturer;
            }
            else
            {
                throw new Exception($"Manufacturer {pimManufacturer} not found in external system.");
            }
        }
    }

    private async Task HandleVendor(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        var pimVendor = pimProduct.Brands.OrderBy(x => x.Code).FirstOrDefault();
        if (pimVendor != null)
        {
            var vendor = await _vendorService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimVendor.Code);

            if (vendor != null)
            {
                product.Vendor = vendor;
            }
            else
            {
                throw new Exception($"Vendor {pimVendor.Code} not found in external system.");
            }
        }
    }

    private async Task HandleColor(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        var pimColor = pimProduct.PimColorEntity?.Code;
        if (pimColor != null)
        {
            var color = await _colorService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimProduct.PimColorEntity!.Code);

            if (color == null)
            {
                var newColor = new Color
                {
                    ExternalSystemCode = _appSettings.ExternalSystemCode,
                    Code = pimProduct.PimColorEntity.Code,
                    ExternalCode = pimProduct.PimColorEntity.Code,
                    Name = pimProduct.PimColorEntity.Name,
                    HexCode = pimProduct.PimColorEntity.HexCode
                };
                await _colorService.UpsertAsync(newColor);

                color = await _colorService.GetExternalReferenceAsync(
                    _appSettings.ExternalSystemCode,
                    externalCode: pimProduct.PimColorEntity!.Code);
            }

            if (color != null)
            {
                product.Color = color;
            }
            else
            {
                throw new Exception($"Color {pimProduct.PimColorEntity.Code} not found in external system.");
            }
        }
    }

    private async Task HandleProductModel(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        if (!product.Category!.Code!.StartsWith('1')) return;
        if (pimProduct.ParentProducts.Count == 0) return;

        var pimParent = pimProduct.ParentProducts.Where( x => x.ProductType == ProductTypeOutputType.BUNDLE.ToString()).FirstOrDefault();
        if (pimParent != null)
        {
            var pimProductId = pimProduct.GetAttributeValue("plm_product_id");
            if (string.IsNullOrWhiteSpace(pimProductId))
            {
                return;
            }

            var productModel = await _productModelService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimParent.Id.ToString());

            if (productModel == null)
            {
                var newProductModel = new ProductModel
                {
                    ExternalSystemCode = _appSettings.ExternalSystemCode,
                    Code = pimProductId!.Length > 20 ? pimProductId[0..20] : pimProductId,
                    ExternalCode = pimParent.Id.ToString(),
                    Name = pimParent.Name
                };

                var result = await _productModelService.UpsertAsync(newProductModel) ?? throw new Exception($"Failed to Upsert ProductModel {pimParent.Id}.");
                
                productModel = await _productModelService.GetExternalReferenceAsync(
                    _appSettings.ExternalSystemCode,
                    externalCode: pimParent.Id.ToString());

                if (result != null && productModel == null)
                {
                    var Coupling = new Coupling()
                    {
                        ExternalSystem = new() { Code = _appSettings.ExternalSystemCode },
                        Entity = new() { Code = "ProductModel" },
                        RecordId = result.Id,
                        ExternalCode = result.ExternalCode
                    };
                    var upsertedCoupling = await _couplingService.UpsertAsync(Coupling);

                    productModel = await _productModelService.GetExternalReferenceAsync(
                        _appSettings.ExternalSystemCode,
                        externalCode: pimParent.Id.ToString());
                }
            }

            if (productModel != null)
            {
                product.ProductModel = productModel;
            }
            else
            {
                throw new Exception($"ProductModel {pimParent.Id} not found in external system.");
            }
        }
    }

    private async Task HandleBattery(Product product, PimProductEntity pimProduct)
    {
        _logger.LogMethodStart();

        if (!product.Category!.Code!.StartsWith('1')) return;
        var pimBattery = pimProduct.GetAttributeValue("plm_battery_size");
        if (pimBattery != null)
        {
            var batteryRef = await _batteryService.GetExternalReferenceAsync(
                _appSettings.ExternalSystemCode,
                externalCode: pimBattery);

            if (batteryRef != null)
            {
                product.Battery = batteryRef;
            }
            else
            {
                var battery = new Battery()
                {
                    ExternalSystemCode = _appSettings.AppName,
                    Code = pimBattery,
                    ExternalCode = pimBattery,
                    Name = pimBattery
                };
                var upsertedBattery = await _batteryService.UpsertAsync(battery);
                if (upsertedBattery != null)
                {
                    batteryRef = new()
                    {
                        Id = upsertedBattery.Id,
                        Code = upsertedBattery.Code,
                        ExternalCode = upsertedBattery.ExternalCode,
                        Name = upsertedBattery.Name
                    };
                    product.Battery = batteryRef;
                }
                else
                {
                    throw new Exception($"Battery {pimBattery} not found in external system.");
                }
            }
        }
    }
}
