using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.Payments
{
    public class SimplyCashReceiptsConfiguration : IEntityTypeConfiguration<SimplyCashReceiptsEntity>
    {
        public void Configure(EntityTypeBuilder<SimplyCashReceiptsEntity> builder)
        {
            builder.ToTable("CashReceipts");

            builder.<PERSON><PERSON>ey(x => x.Id);

            builder.Property(x => x.Id)
                .HasColumnName("Id")
                .HasColumnType("uniqueidentifier")
                .IsRequired(true);

            builder.Property(x => x.PaymentMethodCode)
                .HasColumnName("PaymentMethodCode")
                .HasColumnType("nvarchar(50)")
                .IsRequired(false);

            builder.Property(x => x.PaymentDate)
                .HasColumnName("PaymentDate")
                .HasColumnType("datetime2(7)");

            builder.Property(x => x.DocumentNumber)
                .HasColumnName("DocumentNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.ClinicCode)
                .HasColumnName("ClinicCode")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.Amount)
                .HasColumnName("Amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(x => x.Description)
                .HasColumnName("Description")
                .HasColumnType("nvarchar(255)")
                .IsRequired(false);

            builder.Property(x => x.BankName)
                .HasColumnName("BankName")
                .HasColumnType("nvarchar(50)")
                .IsRequired(false);

            builder.Property(x => x.BalanceAccountNumber)
                .HasColumnName("BalanceAccountNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.AppliesToDocNumber)
                .HasColumnName("AppliesToDocNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.FileName)
                .HasColumnName("FileName")
                .HasColumnType("nvarchar(255)")
                .IsRequired(false);

            builder.Property(x => x.CreatedOn)
                .HasColumnName("CreatedOn")
                .HasColumnType("datetime2(7)")
                .IsRequired(true)
                .ValueGeneratedOnAdd();

            builder.Property(x => x.ModifiedOn)
                .HasColumnName("ModifiedOn")
                .HasColumnType("datetime2(7)")
                .IsRequired(true)
                .ValueGeneratedOnAdd();

            builder.Property(x => x.IntegrationRequired)
                .HasColumnName("IntegrationRequired")
                .HasColumnType("bit")
                .IsRequired(true);

            builder.Property(x => x.IntegrationDate)
                .HasColumnName("IntegrationDate")
                .HasColumnType("datetime2(7)")
                .IsRequired(false);
        }
    }
}