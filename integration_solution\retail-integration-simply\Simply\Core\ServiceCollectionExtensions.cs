﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Simply.Models.Patients;
using WSA.Retail.Integration.Simply.Models.PurchaseOrders;
using WSA.Retail.Integration.Simply.Models.SalesInvoices;
using WSA.Retail.Integration.Simply.Models.PurchaseReceipts;
using WSA.Retail.Integration.Simply.Models.Payments;

namespace WSA.Retail.Integration.Simply.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSimplyServices(
        this IServiceCollection services, 
        AppSettings appSettings)
    {
        // ==== DB CONTEXT =================================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.SqlConnectionString, "SqlConnectionString");
        services.AddDbContext<SimplyContext>(
            options => options.UseSqlServer(appSettings.SqlConnectionString)
                .EnableDetailedErrors(true)
                .EnableSensitiveDataLogging(true)
                .LogTo(s => System.Diagnostics.Debug.WriteLine(s)));


        // ==== REPOSITORIES ===============================================
        services.AddScoped<ISimplyCustomerRepository, SimplyCustomerRepository>();
        services.AddScoped<ISimplyPaymentRepository, SimplyPaymentRepository>();
        services.AddScoped<ISimplyCashReceiptsRepository, SimplyCashReceiptsRepository>();
        services.AddScoped<ISimplyPOHeaderRepository, SimplyPOHeaderRepository>();
        services.AddScoped<ISimplyPurchaseReceiptRepository, SimplyPurchaseReceiptRepository>();
        services.AddScoped<ISimplySalesHeaderRepository, SimplySalesHeaderRepository>();

        // ==== PROCESSORS =================================================
        services.AddScoped<ISimplyPatientProcessor, SimplyPatientProcessor>();
        services.AddScoped<ISimplyPaymentProcessor, SimplyPaymentProcessor>();
        services.AddScoped<ISimplyCashReceiptsProcessor, SimplyCashReceiptsProcessor>();
        services.AddScoped<ISimplyPurchaseOrderProcessor, SimplyPurchaseOrderProcessor>();
        services.AddScoped<ISimplyPurchaseReceiptProcessor, SimplyPurchaseReceiptProcessor>();
        services.AddScoped<ISimplySalesProcessor, SimplySalesProcessor>();

        services.AddLogging();

        return services;
    }
}

