﻿
Create PROCEDURE [dbo].[GetModifiedProducts]
AS
BEGIN
    SET NOCOUNT ON;

    -- Declare a variable to store the LastModifiedOn value from the Watermark table
    DECLARE @LastModifiedOn DATETIME2(7);

    -- Retrieve the LastModifiedOn value from the Watermark table for the given Code
    SELECT @LastModifiedOn = LastModifiedOn
    FROM [$(integration_database)].[dbo].[Watermark]
    WHERE [Code] = 'dbo.Product';

    -- Check if the value was retrieved
    IF @LastModifiedOn IS NOT NULL
    BEGIN
        -- Retrieve all products with ModifiedOn greater than the LastModifiedOn value
        SELECT 
		p.Code AS [ITEM CATEGORY CODE],
        p.Code AS [ITEM NUMBER],
        p.Name AS [ITEM DESCRIPTION],
		0 AS [WARRANTY MONTHS], -- Default value 0
        CASE 
            WHEN pc.Code IN ('6100', '1100') THEN 'HA'
            ELSE 'NHA'
        END AS [PRODUCT TYPE CODE],
        CASE 
            WHEN pc.Code IN ('1100', '6100', '1200', '6200') THEN 'Y'
            ELSE 'N'
        END AS [SERIALIZED ITEM(Y/N)],
		0 AS [UNIT COST], -- Default value 0
        0 AS [RRP], -- Default value 0
        m.Code AS [MANUFACTURER],
		'' AS [ITEM MODEL], -- Blank value
        '' AS [STYLE], -- Blank value
        CASE 
            WHEN pc.Code IN ('2000', '7000') THEN 'N'
            ELSE 'Y'
        END AS [NON-STOCK(Y/N)],
		'Y' AS [PRODUCT_INACTIVE(Y/N)],-- Always 'Y'
        m.Code AS [ITEM BRAND CODE],
        '' AS [ITEM COLOUR], -- Blank value
        '' AS [ITEM PRICE RANGE], -- Blank value
        '' AS [OHS_DEVICE_CODE], -- Blank value
        '' AS [OHS_DEVICE_CATEGORY], -- Blank value
        '' AS [OHS_CLIENT_COST], -- Blank value
        'N' AS [ALWAYS_N] -- Always 'N'
    FROM 
        [$(integration_database)].dbo.Product AS p
    LEFT JOIN 
        [$(integration_database)].dbo.ProductCategory pc ON p.ProductCategoryId = pc.Id
    LEFT JOIN 
        [$(integration_database)].dbo.Manufacturer m ON p.ManufacturerId = m.Id
        WHERE p.[CreatedOn] > @LastModifiedOn;
    END
    ELSE
    BEGIN
        -- If no record is found in the Watermark table, return an error message
        PRINT 'No record found in the Watermark table with Code = dbo.Product';
    END
END
GO