namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesHeaderEntity
{
    public Guid Id { get; set; }
    public required string InvoiceNumber { get; set; }
    public required string SalesType { get; set; }
    public int? Line { get; set; }
    public string? PatientNumber { get; set; }
    public DateTime? DocumentDate { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public int? Quantity { get; set; }
    public decimal? Amount { get; set; }
    public decimal? GST { get; set; }
    public string? Clinic { get; set; }
    public string? ReferralSource { get; set; }
    public string? HearingAidSale { get; set; }
    public string? InvoiceCreatedBy { get; set; }
    public string? InvoiceSpecialist { get; set; }
    public string? CreditReasonCode { get; set; }
    public string? OHSClaim { get; set; }
    public string? AppliesToDocument { get; set; }
    public decimal? Gross { get; set; }
    public decimal? DiscountAmount { get; set; }
    public string? DateTimeCreated { get; set; }
    public string? InventoryLocation { get; set; }
    public string? FileName { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
    public bool IntegrationRequired { get; set; }
    public DateTime? IntegrationDate { get; set; }

    // Navigation properties
    public ICollection<SimplySalesDetailEntity>? Details { get; set; }
    public ICollection<SimplySalesFunderEntity>? Funders { get; set; }
}