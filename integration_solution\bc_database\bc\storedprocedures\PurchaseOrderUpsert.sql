﻿CREATE PROCEDURE [bc].[PurchaseOrderUpsert]
(
    @loadId NVARCHAR(40)
)
WITH EXECUTE AS OWNER
AS
BEGIN  
    BEGIN TRY
    SET XACT_ABORT ON
    BEGIN TRANSACTION

    UPDATE dest
     
       SET CompanyId = IIF(dest.ExternalCompanyId IS DISTINCT FROM source.CompanyId, NULL, dest.CompanyId),
           DocumentNumber = source.DocumentNumber,
           DocumentDate = source.DocumentDate,
           PostingDate = source.PostingDate,
           VendorId = IIF(dest.ExternalVendorId IS DISTINCT FROM source.VendorId, NULL, dest.VendorId),
           ExternalVendorId = source.VendorId,
           BuyFromVendorId = IIF(dest.ExternalBuyFromVendorId IS DISTINCT FROM source.BuyFromVendorId, NULL, dest.BuyFromVendorId),
           ExternalBuyFromVendorId = source.BuyFromVendorId,
           ResponsibilityCenterId = IIF(dest.ExternalResponsibilityCenterId IS DISTINCT FROM source.ResponsibilityCenterId, NULL, dest.ResponsibilityCenterId),
           ExternalResponsibilityCenterId = source.ResponsibilityCenterId,
           CurrencyId = IIF(dest.ExternalCurrencyId IS DISTINCT FROM source.CurrencyId, NULL, dest.CurrencyId),
           ExternalCurrencyId = source.CurrencyId,
           PaymentTermsId = IIF(dest.ExternalPaymentTermsId IS DISTINCT FROM source.PaymentTermsId, NULL, dest.PaymentTermsId),
           ExternalPaymentTermsId = source.PaymentTermsId,
           DimensionSetId = IIF(dest.ExternalDimensionSetNumber IS DISTINCT FROM source.DimensionSetId, NULL, dest.DimensionSetId),
           ExternalDimensionSetNumber = source.DimensionSetId,
           ExternalCreatedOn = source.SystemCreatedAt,
           ExternalModifiedOn = source.SystemModifiedAt,
           ModifiedOn = SYSUTCDATETIME()

      FROM bc.PurchaseOrder AS dest

     INNER JOIN bc.PurchaseOrder_Staging AS source
        ON dest.ExternalCompanyId = source.CompanyId
       AND dest.ExternalId = source.Id
       AND source.ETL_LoadId = @loadId

     WHERE dest.ExternalModifiedOn < source.SystemModifiedAt
        OR dest.ExternalModifiedOn IS NULL;

    INSERT INTO bc.PurchaseOrder (
           ExternalCompanyId,
           ExternalId,
           DocumentNumber,
           DocumentDate,
           PostingDate,
           ExternalVendorId,
           ExternalBuyFromVendorId,
           ExternalResponsibilityCenterId,
           ExternalCurrencyId,
           ExternalPaymentTermsId,
           ExternalDimensionSetNumber,
           ExternalCreatedOn,
           ExternalModifiedOn)

    SELECT source.CompanyId AS ExternalCompanyId,
           source.Id AS ExternalId,
           source.DocumentNumber AS DocumentNumber,
           source.DocumentDate,
           source.PostingDate,
           source.VendorId AS ExternalVendorId,
           source.BuyFromVendorId AS ExternalBuyFromVendorId,
           source.ResponsibilityCenterId AS ExternalResponsibilityCenterId,
           source.CurrencyId AS ExternalCurrencyId,
           source.PaymentTermsId AS ExternalPaymentTermsId,
           source.DimensionSetId AS ExternalDimensionSetNumber,
           source.SystemCreatedAt AS ExternalCreatedOn,
           source.SystemModifiedAt AS ExternalModifiedOn

      FROM bc.PurchaseOrder_Staging AS source

      WHERE source.ETL_LoadId = @loadId
        AND NOT EXISTS (
            SELECT 1
            FROM bc.PurchaseOrder AS dest
            WHERE dest.ExternalCompanyId = source.CompanyId
              AND dest.ExternalId = source.Id
    )
           
    TRUNCATE TABLE bc.PurchaseOrder_Staging;
    COMMIT TRANSACTION;
           
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH

END