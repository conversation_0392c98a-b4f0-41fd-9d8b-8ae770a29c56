﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Models.Patients;

namespace WSA.Retail.Integration.Simply.Models.Patients;

public class SimplyPatientProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPatientProcessor> logger,
    ISimplyCustomerRepository simplyCustomerRepository,
    IPatientService patientService)
    : ISimplyPatientProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPatientProcessor> _logger = logger;
    private readonly ISimplyCustomerRepository _simplyCustomerRepository = simplyCustomerRepository;
    readonly IPatientService _patientService = patientService ?? throw new ArgumentNullException(nameof(patientService));

    public async Task ProcessSimplyPatientsAsyc()
    {
        _logger.LogMethodStart(_appSettings.AppName);

        var patients = await _simplyCustomerRepository.GetIntegrationRecordsAsync();
        _logger.LogCustomInformation(_appSettings.AppName, $"Found {patients.Count} patients to process");

        await Parallel.ForEachAsync(
            patients,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (patient, cancellationToken) =>
            {
                await ProcessSimplyPatientAsync(patient);
            });
    }

    public async Task ProcessSimplyPatientAsync(SimplyCustomerEntity simplyPatient)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        try
        {
            var patient = simplyPatient.ToPatient();
            patient.ExternalSystemCode = _appSettings.ExternalSystemCode;

            if (patient.AlternateCode != null && patient.AlternateCode.Trim().StartsWith('9') && patient.AlternateCode.Trim().Length > 5)
            {
                _logger.LogCustomInformation(_appSettings.AppName,
                    $"Skipping funder {patient.Code} - {patient.Name}.");

                await _simplyCustomerRepository.UpdateIntegrationStatusAsync(simplyPatient.Id);
            }
            else
            {
                _logger.LogCustomInformation(_appSettings.AppName,
                    $"Sending patient {patient.Code} to middle layer.");
                var result = await _patientService.UpsertAsync(patient);

                if (result != null)
                {
                    _logger.LogCustomInformation(_appSettings.AppName,
                        $"Successfully processed patient {patient.Code} - {patient.Name}.");

                    await _simplyCustomerRepository.UpdateIntegrationStatusAsync(simplyPatient.Id);
                }
                else
                {
                    _logger.LogCustomWarning(_appSettings.AppName,
                        $"Failed to process patient {simplyPatient.Id} - {simplyPatient.Name}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex, _appSettings.AppName,
                $"Error processing patient {simplyPatient.Id} - {simplyPatient.Name}");
        }
    }
}