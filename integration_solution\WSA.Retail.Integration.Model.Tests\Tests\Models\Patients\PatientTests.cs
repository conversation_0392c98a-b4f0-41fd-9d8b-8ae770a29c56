﻿using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Patients;

public class PatientTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly PatientService _patientService;

    public PatientTests()
    {
        _serviceFactory = new ServiceFactory();
        _patientService = _serviceFactory.CreatePatientService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnPatient()
    {
        // Act
        var result = await _patientService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "PAT1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("7C17FD50-1F26-4371-A8D3-161A6D053C74"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnPatient()
    {
        // Act
        var result = await _patientService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-PAT1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("7C17FD50-1F26-4371-A8D3-161A6D053C74"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _patientService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "PAT1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("7C17FD50-1F26-4371-A8D3-161A6D053C74"), result.Id);
    }
}

