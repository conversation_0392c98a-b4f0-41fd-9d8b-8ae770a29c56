﻿using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class InvoiceEventEntity : IInvoiceEventEntity
{
    public Guid Id { get; set; }
    public required string EventType { get; set; }
    public Guid MessageId { get; set; }
    public Guid SaleId { get; set; }
    public string? ExternalNumber { get; set; }
    public Guid? LocationId { get; set; }
    public Guid? PatientId { get; set; }
    public Guid? FunderId { get; set; }
    public DateTime Timestamp { get; set; }
    public DateTime LocalTimestamp { get; set; }
    public required string BlobPath { get; set; }
    public EventProcessingStatus Status { get; set; }
    public DateTime? ProcessedOn { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
}
