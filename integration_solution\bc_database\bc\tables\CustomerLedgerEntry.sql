﻿CREATE TABLE [bc].[CustomerLedgerEntry](
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[CompanyId] UNIQUEIDENTIFIER,
	[ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
	[ExternalId] UNIQUEIDENTIFIER NOT NULL,
	[EntryNumber] INT,
	[CustomerId] UNIQUEIDENTIFIER,
	[ExternalCustomerId] UNIQUEIDENTIFIER,
	[CustomerPostingGroupId] UNIQUEIDENTIFIER,
	[ExternalCustomerPostingGroupId] UNIQUEIDENTIFIER,
	[PostingDate] DATETIME2(7),
	[DocumentDate] DATETIME2(7),
	[DueDate] DATETIME2(7),
	[DocumentType] NVARCHAR(20),
	[DocumentNumber] NVARCHAR(20),
	[OrderReferenceNumber] NVARCHAR(50),
	[Description] NVARCHAR(100),
	[Open] BIT,
	[Amount] DECIMAL(18,2),
	[DimensionSetId] UNIQUEIDENTIFIER,
	[ExternalDimensionSetNumber] INT,
	[ExternalCreatedOn] DATETIME2(7),
	[ExternalModifiedOn] DATETIME2(7),
	[CreatedOn] [datetime2](7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ModifiedOn] [datetime2](7) NOT NULL DEFAULT SYSUTCDATETIME(),
       CONSTRAINT [PK_CustomerLedgerEntry_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
	)
GO

CREATE UNIQUE INDEX [IX_CustomerLedgerEntry_CompanyId_ExternalId]
ON [bc].[CustomerLedgerEntry] ([CompanyId], [ExternalId])
GO

CREATE INDEX [IX_CustomerLedgerEntry_DocumentType_PostingDate_DocumentNumber]
ON [bc].[CustomerLedgerEntry] ([DocumentType], [PostingDate], [DocumentNumber])
GO