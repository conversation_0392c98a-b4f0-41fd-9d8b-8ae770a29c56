﻿CREATE TABLE [cosium].[appareillage](
	[id] [nvarchar](50) NOT NULL,
	[refappareil] [varchar](max) NULL,
	[hstatut] [varchar](max) NULL,
	[datedebut] [varchar](max) NULL,
	[datefin] [varchar](max) NULL,
	[numpiece] [varchar](max) NULL,
	[hcommentaire] [varchar](max) NULL,
	[hrefclient] [varchar](max) NULL,
	[hcote] [varchar](max) NULL,
	[fingarantie] [varchar](max) NULL,
	[finextgarantie] [varchar](max) NULL,
	[prescripteur] [varchar](max) NULL,
	[refuser] [varchar](max) NULL,
	[dateprescription] [varchar](max) NULL,
	[dateappareillage] [varchar](max) NULL,
	[prixvendu] [varchar](max) NULL,
	[origine] [varchar](max) NULL,
	[datemodif] [varchar](max) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_appareillage] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [cosium].[appareillage] ADD  CONSTRAINT [DF_appareillage_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[appareillage] ADD  CONSTRAINT [DF_appareillage_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO
