﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisProductResponseForAllProductsOutputType
{
    [JsonPropertyName("assets")] public List<PisProductAssetOutputType> Assets { get; set; } = [];

    [JsonPropertyName("attributes")] public List<PisAttributeResponseOutputType> Attributes { get; set; } = [];

    [JsonPropertyName("brands")] public List<string> Brands { get; set; } = [];

    [JsonPropertyName("bundleSets")] public List<PisBundleSetResponseWithProductsOutputType> BundleSets { get; set; } = [];

    [JsonPropertyName("categoryIds")] public List<PisCategoryResponseOutputType> CategoryIds { get; set; } = [];

    [JsonPropertyName("channelId")] public string? ChannelId { get; set; }

    [JsonPropertyName("childProducts")] public List<PisChildProductResponseOutputType> ChildProducts { get; set; } = [];

    [JsonPropertyName("color")] public ColorOutputType? Color { get; set; }

    [JsonPropertyName("createdAt")] public DateTime CreatedAt { get; set; }

    [JsonPropertyName("currency")] public string? Currency { get; set; }

    [JsonPropertyName("customerGroups")] public List<CustomerGroupOutput> CustomerGroups { get; set; } = [];

    [JsonPropertyName("customerLabels")] public List<PisCustomerLabel> CustomerLabels { get; set; } = [];

    [JsonPropertyName("earlyReleaseDate")] public DateTime? EarlyReleaseDate { get; set; }

    [JsonPropertyName("featureMarketingNames")] public List<string> FeatureMarketingNames { get; set; } = [];

    [JsonPropertyName("featureRNDNames")] public List<string> FeatureRNDNames { get; set; } = [];

    [JsonPropertyName("featureSoftwareNames")] public List<string> FeatureSoftwareNames { get; set; } = [];

    [JsonPropertyName("isAvailable")] public bool IsAvailable { get; set; }

    [JsonPropertyName("isPhasedOut")] public bool IsPhasedOut { get; set; }

    [JsonPropertyName("listPrice")] public decimal? ListPrice { get; set; }

    [JsonPropertyName("name")] public string? Name { get; set; }

    [JsonPropertyName("parentProductSku")] public List<string> ParentProductSku { get; set; } = [];

    [JsonPropertyName("productId")] public Guid ProductId { get; set; } = Guid.Empty;

    [JsonPropertyName("productSource")] public ProductSourceOutputType? ProductSource { get; set; }

    [JsonPropertyName("productType")] public ProductTypeOutputType? ProductType { get; set; }

    [JsonPropertyName("ranking")] public int Ranking { get; set; }

    [JsonPropertyName("relatedProducts")] public List<PisRelatedProductResponseOutputType> RelatedProducts { get; set; } = [];

    [JsonPropertyName("releaseDate")] public DateTime? ReleaseDate { get; set; }

    [JsonPropertyName("sku")] public string? Sku { get; set; }

    [JsonPropertyName("state")] public StateOutputType? State { get; set; }

    [JsonPropertyName("techDocsAttributes")] public List<PisTechDocsAttributeOutputType> TechDocsAttributes { get; set; } = [];

    [JsonPropertyName("updatedAt")] public DateTime UpdatedAt { get; set; }
}
