﻿CREATE TABLE [dbo].[PurchaseReturnLine]
(
    [Id]                        UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseReturnLine_Id] DEFAULT NEWID() NOT NULL,
    [PurchaseReturnId]          UNIQUEIDENTIFIER NOT NULL,
    [Sequence]                  INT NOT NULL,
    [ProductId]                 UNIQUEIDENTIFIER NULL,
    [Description]               NVARCHAR(100) NULL,
    [Quantity]                  DECIMAL(18,4) NULL,
    [UnitPrice]                 DECIMAL(18,4) NULL,
    [GrossAmount]               DECIMAL(18,4) NULL,
    [DiscountAmount]            DECIMAL(18,4) NULL,
    [AmountExclTax]             DECIMAL(18,4) NULL,
    [TaxAmount]                 DECIMAL(18,4) NULL,
    [AmountInclTax]             DECIMAL(18,4) NULL,
    [IsActive]                  BIT CONSTRAINT [DF_PurchaseReturnLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]                 DATETIME2 CONSTRAINT [DF_PurchaseReturnLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]                DATETIME2 CONSTRAINT [DF_PurchaseReturnLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT                  [PK_PurchaseReturnLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT                  [FK_PurchaseReturnLine_PurchaseReturn] FOREIGN KEY (PurchaseReturnId) REFERENCES [dbo].[PurchaseReturn](Id),
    CONSTRAINT                  [FK_PurchaseReturnLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseReturnLine_PurchaseReceiptId_Sequence
ON [dbo].[PurchaseReturnLine] ([PurchaseReturnId], [Sequence])
GO