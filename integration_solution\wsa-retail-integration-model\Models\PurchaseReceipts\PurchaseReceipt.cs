﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json.Serialization;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Models.PurchaseReceipts;

public class PurchaseReceipt : IIdentifiable, ITelemetryEntity
{
    [JsonPropertyName("id")] public Guid Id { get; set; } = Guid.Empty;
    [JsonPropertyName("externalSystemCode")] public string? ExternalSystemCode { get; set; }
    [JsonPropertyName("documentNumber")] public string? DocumentNumber { get; set; }
    [JsonPropertyName("externalReference")] public string? ExternalReference { get; set; }
    [JsonPropertyName("purchaseOrder")] public ExternalDocumentReference? PurchaseOrder { get; set; }
    [JsonPropertyName("vendor")] public ExternalReference? Vendor { get; set; }
    [JsonPropertyName("clinic")] public ExternalReference? Clinic { get; set; }
    [JsonPropertyName("documentDate"), JsonConverter(typeof(DateOnlyJsonConverter))] public DateTime? DocumentDate { get; set; }
    [JsonPropertyName("purchaseReceiptLines")] public List<PurchaseReceiptLine> PurchaseReceiptLines { get; set; } = [];
    [JsonPropertyName("createdOn")] public DateTime? CreatedOn { get; set; }
    [JsonPropertyName("modifiedOn")] public DateTime? ModifiedOn { get; set; }

    public bool HasChanges(PurchaseReceipt? oldPurchaseReceipt)
    {
        if (oldPurchaseReceipt == null) return true;
        if (!Common.AreEqual(DocumentNumber, oldPurchaseReceipt.DocumentNumber)) return true;
        if (!Common.AreEqual(ExternalReference, oldPurchaseReceipt.ExternalReference)) return true;
        if (!Common.AreEqual(PurchaseOrder, oldPurchaseReceipt.PurchaseOrder)) return true;
        if (!Common.AreEqual(Vendor, oldPurchaseReceipt.Vendor)) return true;
        if (!Common.AreEqual(Clinic, oldPurchaseReceipt.Clinic)) return true;
        if (!Common.AreEqual(DocumentDate, oldPurchaseReceipt.DocumentDate)) return true;
        if (PurchaseReceiptLines.Count != oldPurchaseReceipt.PurchaseReceiptLines.Count)
            return true;

        foreach (var line in PurchaseReceiptLines)
        {
            var matchingOldLine = oldPurchaseReceipt.PurchaseReceiptLines.FirstOrDefault(l => l.Sequence == line.Sequence);
            if (matchingOldLine == null || line.HasChanges(matchingOldLine))
                return true;
        }

        foreach (var oldLine in oldPurchaseReceipt.PurchaseReceiptLines)
        {
            if (!PurchaseReceiptLines.Any(l => l.Sequence == oldLine.Sequence))
                return true;
        }
        return false;
    }

    public string GetEntityTypeName()
    {
        return this.GetType().Name;
    }

    public void AddTelemetryProperties(MetricTelemetry metricTelemetry)
    {
        metricTelemetry.Properties.Add("PurchaseReceiptId", Id.ToString());
        metricTelemetry.Properties.Add("PurchaseReceiptNumber", DocumentNumber);
        if (DocumentDate != null)
            metricTelemetry.Properties.Add("PurchaseReceiptDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (PurchaseOrder?.Id != null)
            metricTelemetry.Properties.Add("PurchaseOrderId", PurchaseOrder.Id.ToString());
        if (PurchaseOrder?.DocumentNumber != null)
            metricTelemetry.Properties.Add("PurchaseOrderNumber", PurchaseOrder.DocumentNumber);
        if (Clinic?.Id != null)
            metricTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            metricTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Vendor?.Id != null)
            metricTelemetry.Properties.Add("VendorId", Vendor.Id.ToString());
        if (Vendor?.Code != null)
            metricTelemetry.Properties.Add("VendorCode", Vendor.Code);
    }

    public void AddEventProperties(EventTelemetry eventTelemetry)
    {
        eventTelemetry.Properties.Add("PurchaseReceiptId", Id.ToString());
        eventTelemetry.Properties.Add("PurchaseReceiptNumber", DocumentNumber);
        if (DocumentDate != null)
            eventTelemetry.Properties.Add("PurchaseReceiptDate", DocumentDate.Value.ToString("yyyy-MM-dd"));
        if (PurchaseOrder?.Id != null)
            eventTelemetry.Properties.Add("PurchaseOrderId", PurchaseOrder.Id.ToString());
        if (PurchaseOrder?.DocumentNumber != null)
            eventTelemetry.Properties.Add("PurchaseOrderNumber", PurchaseOrder.DocumentNumber);
        if (Clinic?.Id != null)
            eventTelemetry.Properties.Add("ClinicId", Clinic.Id.ToString());
        if (Clinic?.Code != null)
            eventTelemetry.Properties.Add("ClinicCode", Clinic.Code);
        if (Vendor?.Id != null)
            eventTelemetry.Properties.Add("VendorId", Vendor.Id.ToString());
        if (Vendor?.Code != null)
            eventTelemetry.Properties.Add("VendorCode", Vendor.Code);
    }
}