﻿using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Core;

public interface IDocument<TLineModel> :
    IIdentifiable,
    IExternallyReferenceable,
    IDocumentNumbered,
    IDocumentDated,
    IClinicAssociated,
    ITelemetryEntity
    where TLineModel : IDocumentLine
{
    public List<TLineModel> Lines { get; }

    public bool HasChanges(IDocument<TLineModel>? oldEntity)
    {
        if (((IDocumentNumbered)this).HasChanges(oldEntity)) return true;
        if (((IDocumentDated)this).HasChanges(oldEntity)) return true;
        if (((IClinicAssociated)this).HasChanges(oldEntity)) return true;
        return false;
    }

    public IEnumerable<string> ValidateRequiredProperties()
    {
        var errors = new List<string>();
        if (string.IsNullOrWhiteSpace(ExternalSystemCode))
            errors.Add("ExternalSystemCode is required.");
        if (string.IsNullOrWhiteSpace(DocumentNumber))
            errors.Add("DocumentNumber is required.");
        if (DocumentDate == null)
            errors.Add("DocumentDate is required.");
        if (Clinic?.Code == null)
            errors.Add("Clinic code is required.");

        if (Lines == null || Lines.Count == 0)
            errors.Add("At least one line is required.");

        return errors;
    }
}
