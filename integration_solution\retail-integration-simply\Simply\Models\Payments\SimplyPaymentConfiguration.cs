using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.Payments
{
    public class SimplyPaymentConfiguration : IEntityTypeConfiguration<SimplyPaymentEntity>
    {
        public void Configure(EntityTypeBuilder<SimplyPaymentEntity> builder)
        {
            builder.ToTable("Payment");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.Property(x => x.Id)
                .HasColumnName("Id")
                .HasColumnType("uniqueidentifier")
                .IsRequired(true);

            builder.Property(x => x.DocumentNumber)
                .HasColumnName("DocumentNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(true);

            builder.Property(x => x.DocumentDate)
                .HasColumnName("DocumentDate")
                .HasColumnType("date")
                .IsRequired(false);

            builder.Property(x => x.PaymentMethod)
                .HasColumnName("PaymentMethod")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.Amount)
                .HasColumnName("Amount")
                .HasColumnType("decimal(18,4)")
                .IsRequired(false);

            builder.Property(x => x.ClinicCode)
                .HasColumnName("ClinicCode")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.PatientNumber)
                .HasColumnName("PatientNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.PayerNumber)
                .HasColumnName("PayerNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.AppliesToDocNumber)
                .HasColumnName("AppliesToDocNumber")
                .HasColumnType("nvarchar(20)")
                .IsRequired(false);

            builder.Property(x => x.CreatedOn)
                .HasColumnName("CreatedOn")
                .HasColumnType("datetime2(7)")
                .IsRequired(true)
                .ValueGeneratedOnAdd();

            builder.Property(x => x.ModifiedOn)
                .HasColumnName("ModifiedOn")
                .HasColumnType("datetime2(7)")
                .IsRequired(true)
                .ValueGeneratedOnAdd();

            builder.Property(x => x.IntegrationRequired)
                .HasColumnName("IntegrationRequired")
                .HasColumnType("bit")
                .IsRequired(true);

            builder.Property(x => x.IntegrationDate)
                .HasColumnName("IntegrationDate")
                .HasColumnType("datetime2(7)")
                .IsRequired(false);
        }
    }
}