﻿using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Couplings;

public class CouplingTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly CouplingService _couplingService;
    private readonly IEnumerable<EntityEntity> _entities;
    private readonly IEnumerable<ExternalSystemEntity> _externalSystems;

    public CouplingTests()
    {
        _serviceFactory = new ServiceFactory();
        _couplingService = _serviceFactory.CreateCouplingService();
        _entities = TestDataLoader.Entities();
        _externalSystems = TestDataLoader.ExternalSystems();

        SeedDatabase();
    }

    private void SeedDatabase()
    {
        using var context = TestDbContextHelper.GetInMemoryDbContext(_serviceFactory.DbName);

        var patientEntityId = _entities.Where(x => x.Code == "PATIENT").First().Id;
        var externalSystemId = _externalSystems.Where(x => x.Code == "TEST1").First().Id;

        // Add couplings with various patterns
        List<CouplingEntity> couplings = [];

        // Basic coupling for simple testsva
        couplings.Add(new()
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = externalSystemId,
            EntityId = patientEntityId,
            RecordId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
            ExternalRecordId = "EXT1-001",
            CreatedOn = DateTime.UtcNow.AddDays(-10),
            ModifiedOn = DateTime.UtcNow.AddDays(-5)
        });


        // Same entity with different external system
        couplings.Add(new()
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = externalSystemId,
            EntityId = patientEntityId,
            RecordId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
            ExternalRecordId = "EXT2-001",
            CreatedOn = DateTime.UtcNow.AddDays(-8),
            ModifiedOn = DateTime.UtcNow.AddDays(-4)
        });

        context.CouplingEntity.AddRange(couplings);
        context.SaveChanges();
    }

    [Fact]
    public async Task GetAsync_GetByIdShouldReturnCoupling()
    {
        // Act
        var result = await _couplingService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            entityCode: EntityType.Patient.GetEntityCode(),
            recordId: Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal("EXT1-001", result.ExternalCode);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnCoupling()
    {
        // Act
        var result = await _couplingService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            entityCode: EntityType.Patient.GetEntityCode(),
            externalCode: "EXT1-001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"), result.RecordId);
    }

    [Fact]
    public async Task UpsertAsync_InsertShouldReturnNewCoupling()
    {
        // Create new coupling
        var coupling = new Coupling
        {
            Entity = new() { Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new() { Code = _serviceFactory.AppSettings.ExternalSystemCode },
            RecordId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
            ExternalCode = "EXT1-003"
        };
        var success = await _couplingService.UpsertAsync(coupling);

        // Assert
        Assert.NotNull(success);
        Assert.NotEqual(Guid.Empty, success.Id);

        // Get coupling from database
        var result = await _couplingService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            entityCode: EntityType.Patient.GetEntityCode(),
            externalCode: "EXT1-003");
        // Assert
        Assert.NotNull(result);
        Assert.Equal(success.Id, result.Id);
    }

    [Fact]
    public async Task UpsertAsync_UpdateShouldReturnNewCoupling()
    {
        // Create new coupling
        var coupling = new Coupling
        {
            Id = Guid.NewGuid(),
            Entity = new() { Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new() { Code = _serviceFactory.AppSettings.ExternalSystemCode },
            RecordId = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc"),
            ExternalCode = "EXT1-004"
        };
        await _couplingService.UpsertAsync(coupling);

        // Modify coupling
        coupling.ExternalCode = "NEWEXTERNALCODE";
        var success = await _couplingService.UpsertAsync(coupling);

        // Assert
        Assert.NotNull(success);

        // Get coupling from database
        var result = await _couplingService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            entityCode: EntityType.Patient.GetEntityCode(),
            recordId: Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc"));
        // Assert
        Assert.NotNull(result);
        Assert.Equal("NEWEXTERNALCODE", result.ExternalCode);
    }

}
