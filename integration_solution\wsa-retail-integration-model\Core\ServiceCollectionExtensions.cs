﻿using Azure.Storage.Queues;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Data.Repositories;
using WSA.Retail.Integration.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Companies;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Payments;
using WSA.Retail.Integration.Models.Payors;
using WSA.Retail.Integration.Models.ProductModels;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.PurchaseReturns;
using WSA.Retail.Integration.Models.PurchaseShipments;
using WSA.Retail.Integration.Models.SalesCredits;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Models.TaxGroups;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Models.Claims;
using WSA.Retail.Integration.Models.Adjustments;
namespace WSA.Retail.Integration.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCoreServices(this IServiceCollection services, IConfiguration configuration)
    {
        // ==== SETTINGS ====================================================
        var appSettings = Activator.CreateInstance<AppSettings>()!;
        configuration.Bind(appSettings);

        services.Configure<AppSettings>(configuration);
        services.AddSingleton(appSettings);
        LoggingSettings.Initialize(appSettings.AppName);

        // ==== DB CONTEXT ==================================================
        bool enableDetailedDbLogging = Convert.ToBoolean(Environment.GetEnvironmentVariable("EnableDetailedDbLogging") ?? "false");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.SqlConnectionString, "SqlConnectionString");
        services.AddDbContextFactory<IntegrationContext>(options =>
        {
            var dbOptionsBuilder = options.UseSqlServer(
                appSettings.SqlConnectionString,
                sqlServicerOptions => sqlServicerOptions.EnableRetryOnFailure(
                    maxRetryCount: 5,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: new[] { 40613, 40501, 49918, 4060, 10060 }
                    ));
            if (enableDetailedDbLogging)
            {
                dbOptionsBuilder
                    .EnableDetailedErrors(true)
                    .EnableSensitiveDataLogging(true)
                    .LogTo(s => System.Diagnostics.Debug.WriteLine(s));
            }
        });

        // ==== EVENT GRID CONTEXT ==========================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.EventGridEndpoint, "EventGridEndpoint");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.EventGridAccessKey, "EventGridAccessKey");
        services.AddHttpClient<IEventGridPublisher, EventGridPublisher>(client =>
        {
            client.BaseAddress = new Uri(appSettings.EventGridEndpoint);
            client.DefaultRequestHeaders.Add("aeg-sas-key", appSettings.EventGridAccessKey);
        });

        // ==== STORAGE ACCOUNT =============================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.StorageQueueConnectionString, "StorageQueueConnectionString");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.FromStorageQueueName, "FromStorageQueueName");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.ToStorageQueueName, "ToStorageQueueName");
        services.AddSingleton(provider =>
        {
            return new Dictionary<string, QueueClient>
            {
                { appSettings.FromStorageQueueName, new QueueClient(appSettings.StorageQueueConnectionString, appSettings.FromStorageQueueName) },
                { appSettings.ToStorageQueueName, new QueueClient(appSettings.StorageQueueConnectionString, appSettings.ToStorageQueueName) }
            };
        });

        // ==== REPOSITORY SERVICES =========================================
        services.AddScoped<IClaimRepository, ClaimRepository>();
        services.AddScoped<ICouplingRepository, CouplingRepository>();
        services.AddScoped<IEntityRepository, EntityRepository>();
        services.AddScoped<IEntitySubscriberRepository, EntitySubscriberRepository>();
        services.AddScoped<IExternalSystemRepository, ExternalSystemRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();
        services.AddScoped<IPurchaseOrderRepository, PurchaseOrderRepository>();
        services.AddScoped<IPurchaseReceiptRepository, PurchaseReceiptRepository>();
        services.AddScoped<IPurchaseReturnRepository, PurchaseReturnRepository>();
        services.AddScoped<IPurchaseShipmentRepository, PurchaseShipmentRepository>();
        services.AddScoped<ISalesCreditRepository, SalesCreditRepository>();
        services.AddScoped<ISalesInvoiceRepository, SalesInvoiceRepository>();

        // ==== ADJUSTMENT SERVICES =========================================
        services.AddScoped<IAdjustmentRepositoryService, AdjustmentRepositoryService>();
        services.AddScoped<ITransactionRepositoryServiceBase<Adjustment, AdjustmentEntity>, AdjustmentRepositoryService>();
        services.AddScoped<ITransactionQueryBuilder<Adjustment, AdjustmentEntity>, AdjustmentQueryBuilder>();
        services.AddScoped<IAdjustmentService, AdjustmentService>();
        services.AddScoped<ITransactionServiceBase<Adjustment>, AdjustmentService>();

        // ==== BATTERY SERVICES ============================================
        services.AddScoped<IBatteryRepositoryService, BatteryRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Battery, BatteryEntity>, BatteryRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Battery, BatteryEntity>, BatteryQueryBuilder>();
        services.AddScoped<IBatteryService, BatteryService>();
        services.AddScoped<IMasterDataServiceBase<Battery>, BatteryService>();

        // ==== CATEGORY SERVICES ===========================================
        services.AddScoped<ICategoryRepositoryService, CategoryRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Category, CategoryEntity>, CategoryRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Category, CategoryEntity>, CategoryQueryBuilder>();
        services.AddScoped<ICategoryService, CategoryService>();
        services.AddScoped<IMasterDataServiceBase<Category>, CategoryService>();

        // ==== CLINIC SERVICES =============================================
        services.AddScoped<IClinicRepositoryService, ClinicRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Clinic, ClinicEntity>, ClinicRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Clinic, ClinicEntity>, ClinicQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Clinic, ClinicEntity>, ClinicAdapter>();
        services.AddScoped<IClinicService, ClinicService>();
        services.AddScoped<IMasterDataServiceBase<Clinic>, ClinicService>();

        // ==== COLOR SERVICES ==============================================
        services.AddScoped<IColorRepositoryService, ColorRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Color, ColorEntity>, ColorRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Color, ColorEntity>, ColorQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Color, ColorEntity>, ColorAdapter>();
        services.AddScoped<IColorService, ColorService>();
        services.AddScoped<IMasterDataServiceBase<Color>, ColorService>();

        // ==== COMPANY SERVICES ============================================
        services.AddScoped<ICompanyRepositoryService, CompanyRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Company, CompanyEntity>, CompanyRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Company, CompanyEntity>, CompanyQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Company, CompanyEntity>, CompanyEntityAdapter>();
        services.AddScoped<ICompanyService, CompanyService>();
        services.AddScoped<IMasterDataServiceBase<Company>, CompanyService>();

        // ==== MANUFACTURER SERVICES =======================================
        services.AddScoped<IManufacturerRepositoryService, ManufacturerRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Manufacturer, ManufacturerEntity>, ManufacturerRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Manufacturer, ManufacturerEntity>, ManufacturerQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Manufacturer, ManufacturerEntity>, ManufacturerAdapter>();
        services.AddScoped<IManufacturerService, ManufacturerService>();
        services.AddScoped<IMasterDataServiceBase<Manufacturer>, ManufacturerService>();

        // ==== PATIENT SERVICES ============================================
        services.AddScoped<IPatientRepositoryService, PatientRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Patient, PatientEntity>, PatientRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Patient, PatientEntity>, PatientQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Patient, PatientEntity>, PatientAdapter>();
        services.AddScoped<IPatientService, PatientService>();
        services.AddScoped<IMasterDataServiceBase<Patient>, PatientService>();

        // ==== PAYOR SERVICES ==============================================
        services.AddScoped<IPayorRepositoryService, PayorRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Payor, PayorEntity>, PayorRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Payor, PayorEntity>, PayorQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Payor, PayorEntity>, PayorAdapter>();
        services.AddScoped<IPayorService, PayorService>();
        services.AddScoped<IMasterDataServiceBase<Payor>, PayorService>();

        // ==== PRODUCT SERVICES ============================================
        services.AddScoped<IProductRepositoryService, ProductRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Product, ProductEntity>, ProductRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Product, ProductEntity>, ProductQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Product, ProductEntity>, ProductAdapter>();
        services.AddScoped<IProductService, ProductService>();
        services.AddScoped<IMasterDataServiceBase<Product>, ProductService>();

        // ==== PRODUCTMODEL SERVICES ============================================
        services.AddScoped<IProductModelRepositoryService, ProductModelRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<ProductModel, ProductModelEntity>, ProductModelRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<ProductModel, ProductModelEntity>, ProductModelQueryBuilder>();
        services.AddScoped<IMasterDataMapper<ProductModel, ProductModelEntity>, ProductModelAdapter>();
        services.AddScoped<IProductModelService, ProductModelService>();
        services.AddScoped<IMasterDataServiceBase<ProductModel>, ProductModelService>();

        // ==== SALES INVOICE SERVICES ============================================
        services.AddScoped<IDocumentQueryBuilder<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>, SalesInvoiceQueryBuilder>();
        services.AddScoped<IDocumentMapper<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>, SalesInvoiceMapper>();

        // ==== TAXGROUP SERVICES ===========================================
        services.AddScoped<ITaxGroupRepositoryService, TaxGroupRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<TaxGroup, TaxGroupEntity>, TaxGroupRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<TaxGroup, TaxGroupEntity>, TaxGroupQueryBuilder>();
        services.AddScoped<IMasterDataMapper<TaxGroup, TaxGroupEntity>, TaxGroupAdapter>();
        services.AddScoped<ITaxGroupService, TaxGroupService>();
        services.AddScoped<IMasterDataServiceBase<TaxGroup>, TaxGroupService>();

        // ==== VENDOR SERVICES =============================================
        services.AddScoped<IVendorRepositoryService, VendorRepositoryService>();
        services.AddScoped<IMasterDataRepositoryServiceBase<Vendor, VendorEntity>, VendorRepositoryService>();
        services.AddScoped<IMasterDataQueryBuilder<Vendor, VendorEntity>, VendorQueryBuilder>();
        services.AddScoped<IMasterDataMapper<Vendor, VendorEntity>, VendorAdapter>();
        services.AddScoped<IVendorService, VendorService>();
        services.AddScoped<IMasterDataServiceBase<Vendor>, VendorService>();

        // ==== ENTITY SERVICES =============================================
        services.AddScoped<IClaimService, ClaimService>();
        services.AddScoped<ICouplingService, CouplingService>();
        services.AddScoped<IEntityService, EntityService>();
        services.AddScoped<IEntitySubscriberService, EntitySubscriberService>();
        services.AddScoped<IExternalSystemService, ExternalSystemService>();
        services.AddScoped<IPaymentService, PaymentService>();
        services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
        services.AddScoped<IPurchaseReceiptService, PurchaseReceiptService>();
        services.AddScoped<IPurchaseReturnService, PurchaseReturnService>();
        services.AddScoped<IPurchaseShipmentService, PurchaseShipmentService>();
        services.AddScoped<ISalesCreditService, SalesCreditService>();
        services.AddScoped<ISalesInvoiceService, SalesInvoiceService>();

        services.AddLogging();
        
        services.AddDbContextFactory<IntegrationContext>();

        return services;
    }
}