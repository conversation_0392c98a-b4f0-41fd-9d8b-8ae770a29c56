{"profiles": {"us_dev": {"commandName": "Project", "commandLineArgs": "--port 7261", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Sycle", "EventGridEndpoint": "https://wsa-retail-integration-usdev.eastus-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "CRAiKVe0duOjr8nB2OIWo4fHzvBFaF2jBFuIbTLqjHS1GozNLX8JJQQJ99BCACYeBjFXJ3w3AAABAZEGbuXB", "ExternalSystemCode": "SYCLE", "FromStorageQueueName": "from-sycle", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-us.database.windows.net,1433;Initial Catalog=us_dev;Persist Security Info=False;User ID=sycle;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationusdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-sycle", "Schedule1": "0 0 0 1 1 *"}}, "us_uat": {"commandName": "Project", "commandLineArgs": "--port 7361", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Sycle", "EventGridEndpoint": "https://wsa-retail-integration-usuat.eastus-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "CHUhl22WG5edeYEGPVaTH4nxxf95FN1wnGiEhleMeUBoXPtQSeaUJQQJ99BCACYeBjFXJ3w3AAABAZEGIXBm", "ExternalSystemCode": "SYCLE", "FromStorageQueueName": "from-sycle", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-us.database.windows.net,1433;Initial Catalog=us_uat;Persist Security Info=False;User ID=sycle;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationusuat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-sycle", "Schedule1": "0 0 0 1 1 *", "ReplacementProduct": "REPLACEMENT"}}, "us_prod": {"commandName": "Project", "commandLineArgs": "--port 7361", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Sycle", "EventGridEndpoint": "https://wsa-retail-integration-usprod.eastus-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "62IJABzaCXs1qmF1v75X10W1JTtz76kDnTQNcsObjCRXh9Kn0CGGJQQJ99BDACYeBjFXJ3w3AAABAZEGH2zu", "ExternalSystemCode": "SYCLE", "FromStorageQueueName": "from-sycle", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-us.database.windows.net,1433;Initial Catalog=us_prod;Persist Security Info=False;User ID=sycle;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationusprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-sycle", "Schedule1": "0 0 0 1 1 *", "ReplacementProduct": "REPLACEMENT"}}}}