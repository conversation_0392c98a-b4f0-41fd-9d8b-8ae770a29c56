namespace WSA.Integration;
using Microsoft.Finance.GeneralLedger.Journal;
using Microsoft.Inventory.Journal;

table 50100 "Retail Integration Setup"
{
    Caption = 'Retail Integration Setup';
    DataClassification = SystemMetadata;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
        }

        field(2; "Enabled"; Boolean)
        {
            Caption = 'Enabled';
        }

        field(3; "API Base URL"; Text[250])
        {
            Caption = 'API Base URL';
            ExtendedDatatype = URL;
        }

        field(4; "API Key"; Text[250])
        {
            Caption = 'API Key';
            ExtendedDatatype = Masked;
        }

        field(5; "External System Code"; Text[255])
        {
            Caption = 'External System Code';
        }

        field(6; "Handle Events Asyncronously"; Boolean)
        {
            Caption = 'Handle Events Asyncronously';
        }

        field(20; "Payment Journal Template"; Code[10])
        {
            Caption = 'Payment Journal Template';
            DataClassification = SystemMetadata;
            TableRelation = "Gen. Journal Template";
        }

        field(21; "Payment Journal Batch"; Code[10])
        {
            Caption = 'Payment Journal Batch';
            DataClassification = SystemMetadata;
            TableRelation = "Gen. Journal Batch".Name where("Journal Template Name" = field("Payment Journal Template"));
        }

        field(22; "Claim Journal Template"; Code[10])
        {
            Caption = 'Claim Journal Template';
            DataClassification = SystemMetadata;
            TableRelation = "Gen. Journal Template";
        }

        field(23; "Claim Journal Batch"; Code[10])
        {
            Caption = 'Claim Journal Batch';
            DataClassification = SystemMetadata;
            TableRelation = "Gen. Journal Batch".Name where("Journal Template Name" = field("Claim Journal Template"));
        }


        field(25; "Item Adjustment Template"; Code[10])
        {
            Caption = 'Item Adjustment Template';
            DataClassification = SystemMetadata;
            TableRelation = "Item Journal Template";
        }

        field(26; "Item Adjustment Batch"; Code[10])
        {
            Caption = 'Item Adjustment Batch';
            DataClassification = SystemMetadata;
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Item Adjustment Template"));
        }

        field(31; "Event Grid Topic Endpoint"; Text[250])
        {
            Caption = 'Event Grid Topic Endpoint';
            ExtendedDatatype = URL;
        }

        field(32; "Event Grid Access Key"; Text[250])
        {
            Caption = 'Event Grid Access Key';
            ExtendedDatatype = Masked;
        }
    }


    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }


    procedure SafeGet()
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert();
        end;
    end;
}
