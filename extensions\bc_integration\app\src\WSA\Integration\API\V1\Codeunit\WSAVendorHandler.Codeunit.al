namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Sales.Customer;
using Microsoft.Purchases.Vendor;
using WSA.Integration;


codeunit 50141 "WSA Vendor Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Vendor Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        vendor: Record Vendor;

    begin
        if TryHandlePost(Request, vendor) then
            Common.SetCreatedResponse(Request, Common.VendorToJson(vendor))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        vendor: Record Vendor;

    begin
        if not TryHandleGet(Request, vendor) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Vendor: Record Vendor)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Request."Vendor No." := Common.GetJsonValue(json, '$.code').AsCode();
            Commit();
        end;

        if not Vendor.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Vendor.Init();
            Vendor."No." := Common.GetJsonValue(json, '$.code').AsCode();
            Vendor.Insert(false);
        end;

        recRef.GetTable(Vendor);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Vendor.FieldNo(Name), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address', recRef, Vendor.FieldNo(Address), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address2', recRef, Vendor.FieldNo("Address 2"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.country', recRef, Vendor.FieldNo("Country/Region Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.region', recRef, Vendor.FieldNo(County), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.postalCode', recRef, Vendor.FieldNo("Post Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.city', recRef, Vendor.FieldNo(City), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.phone', recRef, Vendor.FieldNo("Phone No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.email', recRef, Vendor.FieldNo("E-Mail"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.accountNo', recRef, Vendor.FieldNo("Our Account No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.integrateWithPos', recRef, Vendor.FieldNo("Integrate with POS"), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Vendor);
        Vendor.Modify(true);

        Request."Vendor No." := Vendor."No.";
        Request.Modify(true);
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Vendor: Record Vendor)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Vendor.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.VendorToJson(Vendor));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Vendor.SetRange("No.", Common.GetJsonValue(json, '$.code').AsCode());
            if Vendor.FindFirst() then begin
                Common.SetOkResponse(Request, Common.VendorToJson(Vendor));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Vendor.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if Vendor.FindFirst() then begin
                Common.SetOkResponse(Request, Common.VendorToJson(Vendor));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
