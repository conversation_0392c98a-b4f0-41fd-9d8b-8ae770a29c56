# Transaction Entities

## 📋 Overview

Transaction entities represent business documents and processes that flow between Manage and Business Central systems. These entities capture the operational activities such as purchasing, sales, and inventory movements.

## 🛒 Purchase Transaction Entities

### Purchase Order
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrder.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderEntityConfiguration.cs`

Purchase order documents for procurement processes.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique purchase order identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Purchase order number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Purchase order date |
| `VendorId` | Guid? | ❌ | - | FK to Vendor | Supplier reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Ordering clinic reference |
| `Status` | string? | ❌ | 20 | Enum values | Order status (Draft, Submitted, Approved, etc.) |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total order amount |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code (USD, EUR, etc.) |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system order ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Purchase orders must have either `DocumentNumber` or `ExternalCode`
- `TotalAmount` must be non-negative when specified
- Status transitions follow defined workflow
- Synchronized bidirectionally between Manage and BC

### Purchase Order Line
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderLine.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseOrders/PurchaseOrderLineEntity.cs`

Line items for purchase orders.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique line identifier |
| `PurchaseOrderId` | Guid | ✅ | - | FK to PurchaseOrder | Parent order reference |
| `ProductId` | Guid? | ❌ | - | FK to Product | Product reference |
| `LineNumber` | int? | ❌ | - | > 0 | Line sequence number |
| `Quantity` | decimal? | ❌ | - | > 0 | Ordered quantity |
| `UnitPrice` | decimal? | ❌ | - | >= 0 | Price per unit |
| `LineAmount` | decimal? | ❌ | - | >= 0 | Total line amount |
| `Description` | string? | ❌ | 255 | - | Line item description |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Each line must belong to a purchase order
- `Quantity` must be positive when specified
- `LineAmount` typically equals `Quantity * UnitPrice`
- Line numbers should be unique within an order

### Purchase Receipt
**Source**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceipt.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceiptEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/PurchaseReceipts/PurchaseReceiptEntityConfiguration.cs`

Goods receipt documents for received inventory.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique receipt identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Receipt document number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Receipt date |
| `PurchaseOrderId` | Guid? | ❌ | - | FK to PurchaseOrder | Related purchase order |
| `VendorId` | Guid? | ❌ | - | FK to Vendor | Supplier reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Receiving clinic reference |
| `Status` | string? | ❌ | 20 | Enum values | Receipt status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total receipt amount |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system receipt ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Receipts can be linked to purchase orders
- Receipt quantities cannot exceed ordered quantities
- Synchronized bidirectionally between Manage and BC

## 💰 Sales Transaction Entities

### Sales Invoice
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoice.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceEntityConfiguration.cs`

Customer billing documents.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique invoice identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Invoice number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Invoice date |
| `PatientId` | Guid? | ❌ | - | FK to Patient | Customer reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Billing clinic reference |
| `PayorId` | Guid? | ❌ | - | FK to Payor | Insurance/payor reference |
| `Status` | string? | ❌ | 20 | Enum values | Invoice status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total invoice amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Total tax amount |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code |
| `DueDate` | DateOnly? | ❌ | - | >= DocumentDate | Payment due date |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system invoice ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Invoices must have either `DocumentNumber` or `ExternalCode`
- `DueDate` must be on or after `DocumentDate`
- `TotalAmount` includes `TaxAmount`
- Synchronized bidirectionally between Manage and BC

### Sales Invoice Line
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceLine.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesInvoices/SalesInvoiceLineEntity.cs`

Line items for sales invoices.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique line identifier |
| `SalesInvoiceId` | Guid | ✅ | - | FK to SalesInvoice | Parent invoice reference |
| `ProductId` | Guid? | ❌ | - | FK to Product | Product reference |
| `LineNumber` | int? | ❌ | - | > 0 | Line sequence number |
| `Quantity` | decimal? | ❌ | - | > 0 | Invoiced quantity |
| `UnitPrice` | decimal? | ❌ | - | >= 0 | Price per unit |
| `LineAmount` | decimal? | ❌ | - | >= 0 | Total line amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Line tax amount |
| `TaxGroupId` | Guid? | ❌ | - | FK to TaxGroup | Tax classification |
| `Description` | string? | ❌ | 255 | - | Line item description |
| `SerialNumber` | string? | ❌ | 50 | - | Product serial number |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Each line must belong to a sales invoice
- `Quantity` must be positive when specified
- Serial numbers required for serialized products
- Tax calculations based on `TaxGroupId`

### Sales Credit
**Source**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCredit.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCreditEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/SalesCredits/SalesCreditEntityConfiguration.cs`

Credit note documents for returns and adjustments.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique credit identifier |
| `DocumentNumber` | string? | ❌ | 50 | Unique | Credit note number |
| `DocumentDate` | DateOnly? | ❌ | - | - | Credit date |
| `PatientId` | Guid? | ❌ | - | FK to Patient | Customer reference |
| `ClinicId` | Guid? | ❌ | - | FK to Clinic | Issuing clinic reference |
| `OriginalInvoiceId` | Guid? | ❌ | - | FK to SalesInvoice | Original invoice reference |
| `Status` | string? | ❌ | 20 | Enum values | Credit status |
| `TotalAmount` | decimal? | ❌ | - | >= 0 | Total credit amount |
| `TaxAmount` | decimal? | ❌ | - | >= 0 | Total tax credit |
| `Currency` | string? | ❌ | 3 | ISO currency | Currency code |
| `Reason` | string? | ❌ | 255 | - | Credit reason/description |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system credit ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Credits can reference original invoices
- Credit amounts are typically positive (representing credit value)
- Synchronized bidirectionally between Manage and BC

---

*This document covers the main transaction entities. Additional specialized entities and their relationships are documented in the entity configurations section.*
