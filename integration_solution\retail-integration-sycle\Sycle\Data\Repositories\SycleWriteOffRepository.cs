using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Sycle.Models.Payments;

namespace WSA.Retail.Integration.Sycle.Data.Repositories;

public class SycleWriteOffRepository(IDbContextFactory<SycleContext> dbContextFactory, 
                             ILogger<SycleWriteOffRepository> logger,
                             IOptions<AppSettings> appSettings) : ISycleWriteOffRepository
{
    private readonly IDbContextFactory<SycleContext> _dbContextFactory = dbContextFactory;
    private readonly ILogger<SycleWriteOffRepository> _logger = logger;
    private readonly AppSettings _appSettings = appSettings.Value;

    public async Task<List<SycleWriteOffEntity>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart(_appSettings.AppName);

        using var context = await _dbContextFactory.CreateDbContextAsync();

        var transactionList = await context.SycleWriteOffEntity
            .Where(p => p.IntegrationRequired == true)
            .Where(p => p.TransactionNo != null)
            .ToListAsync();

        return transactionList;
    }

    public async Task<SycleWriteOffEntity?> GetAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleWriteOffEntity
            .FirstOrDefaultAsync(p => p.WriteOffId == id);

        if (entity == null)
        {
            return null;
        }

        return entity;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(int id, bool isIntegrated = true)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleWriteOffEntity
            .FirstOrDefaultAsync(p => p.WriteOffId == id);

        if (entity == null)
        {
            return false;
        }

        entity.IntegrationRequired = false;
        if (isIntegrated) entity.IntegratedOn = DateTime.UtcNow;
        entity.ModifiedOn = DateTime.UtcNow;

        await context.SaveChangesAsync();

        return true;
    }
}