enum 50103 "WSA Request Status"
{
    Extensible = true;

    value(0; new)
    {
        Caption = 'new', Locked = true;
    }

    value(1; ready)
    {
        Caption = 'ready', Locked = true;
    }

    value(5; hold)
    {
        Caption = 'hold', Locked = true;
    }

    value(6; skipped)
    {
        Caption = 'skipped', Locked = true;
    }

    value(200; ok)
    {
        Caption = 'ok', Locked = true;
    }

    value(201; created)
    {
        Caption = 'created', Locked = true;
    }

    value(202; accepted)
    {
        Caption = 'accepted', Locked = true;
    }

    value(204; noContent)
    {
        Caption = 'no content', Locked = true;
    }

    value(400; badRequest)
    {
        Caption = 'bad request', Locked = true;
    }

    value(500; serverError)
    {
        Caption = 'server error', Locked = true;
    }
}
