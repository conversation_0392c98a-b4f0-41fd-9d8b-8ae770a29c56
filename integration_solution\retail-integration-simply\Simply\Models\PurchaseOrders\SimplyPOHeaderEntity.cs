﻿namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPOHeaderEntity
{
    public Guid Id { get; set; }
    public int PONumber { get; set; }
    public required string POType { get; set; }
    public DateTime PODate { get; set; }
    public int? TotalItems { get; set; }
    public decimal? POAmount { get; set; }
    public decimal? DiscountAmount { get; set; }
    public string? ClinicCode { get; set; }
    public string? CreatedBy {  get; set; }
    public string? ApproverFirstName { get; set; }
    public string? ApproverName { get; set; }
    public string? Area { get; set; }
    public string? Region { get; set; }
    public string? POSCode { get; set; }
    public string? FileName { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
    public bool IntegrationRequired { get; set; }
    public DateTime? IntegrationDate { get; set; }

    // Navigation property for PO Details
    public ICollection<SimplyPODetailsEntity>? Details { get; set; }
}
