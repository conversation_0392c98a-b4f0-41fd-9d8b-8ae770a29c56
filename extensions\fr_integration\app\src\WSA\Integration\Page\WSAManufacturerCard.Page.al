namespace WSA.Integration;

using Microsoft.Inventory.Item.Catalog;

page 50178 "WSA Manufacturer Card"
{
    ApplicationArea = All;
    Caption = 'Manufacturer Card';
    PageType = Card;
    SourceTable = Manufacturer;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Code"; Rec.Code)
                {
                    Importance = Promoted;
                    ToolTip = 'Specifies the code of the manufacturer.';
                }

                field(Name; Rec.Name)
                {
                    Importance = Promoted;
                    ToolTip = 'Specifies the name of the manufacturer.';
                }
            }

            group("Address & Contact")
            {
                Caption = 'Address & Contact';
                group(AddressDetails)
                {
                    Caption = 'Address';
                    field(Address; Rec.Address)
                    {
                        ToolTip = 'Specifies the address of the manufacturer.';
                    }

                    field("Address 2"; Rec."Address 2")
                    {
                        ToolTip = 'Specifies additional address information.';
                    }

                    field("Post Code"; Rec."Post Code")
                    {
                        ToolTip = 'Specifies the postal code.';
                    }

                    field(City; Rec.City)
                    {
                        ToolTip = 'Specifies the city of the address.';
                    }

                    field("Country/Region Code"; Rec."Country/Region Code")
                    {
                        ToolTip = 'Specifies the country/region of the address.';
                    }
                }

                group(ContactDetails)
                {
                    Caption = 'Contact';

                    field("Phone No."; Rec."Phone No.")
                    {
                        Importance = Promoted;
                        ToolTip = 'Specifies the phone number of the manufacturer.';
                    }

                    field("E-Mail"; Rec."E-Mail")
                    {
                        ExtendedDatatype = EMail;
                        ToolTip = 'Specifies the email address of the manufacturer.';
                    }
                }

                group(IntegrationDetails)
                {
                    Caption = 'Integration';

                    field("External Code"; Rec."External Code")
                    {
                        Editable = false;
                        ToolTip = 'Specifies the external code of the manufacturer.';
                    }
                }
            }
        }
    }
}
