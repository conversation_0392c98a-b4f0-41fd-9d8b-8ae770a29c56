using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesHeaderConfiguration : IEntityTypeConfiguration<SimplySalesHeaderEntity>
{
    public void Configure(EntityTypeBuilder<SimplySalesHeaderEntity> builder)
    {
        builder.ToTable("SalesHeader");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.InvoiceNumber)
            .HasColumnName("InvoiceNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.SalesType)
            .HasColumnName("SalesType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(true);

        builder.Property(x => x.Line)
            .HasColumnName("Line")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.PatientNumber)
            .HasColumnName("PatientNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.DocumentDate)
            .HasColumnName("DocumentDate")
            .HasColumnType("date")
            .IsRequired(false);

        builder.Property(x => x.DeliveryDate)
            .HasColumnName("DeliveryDate")
            .HasColumnType("date")
            .IsRequired(false);

        builder.Property(x => x.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.Amount)
            .HasColumnName("Amount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.GST)
            .HasColumnName("GST")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.Clinic)
            .HasColumnName("Clinic")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.ReferralSource)
            .HasColumnName("ReferralSource")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.HearingAidSale)
            .HasColumnName("HearingAidSale")
            .HasColumnType("char(1)")
            .IsRequired(false);

        builder.Property(x => x.InvoiceCreatedBy)
            .HasColumnName("InvoiceCreatedBy")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.InvoiceSpecialist)
            .HasColumnName("InvoiceSpecialist")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.CreditReasonCode)
            .HasColumnName("CreditReasonCode")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.OHSClaim)
            .HasColumnName("OHSClaim")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.AppliesToDocument)
            .HasColumnName("AppliesToDocument")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.Gross)
            .HasColumnName("Gross")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.DiscountAmount)
            .HasColumnName("DiscountAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.DateTimeCreated)
            .HasColumnName("DateTimeCreated")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.InventoryLocation)
            .HasColumnName("InventoryLocation")
            .HasColumnType("nvarchar(100)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true);

        builder.Property(x => x.IntegrationDate)
            .HasColumnName("IntegrationDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);
    }
}