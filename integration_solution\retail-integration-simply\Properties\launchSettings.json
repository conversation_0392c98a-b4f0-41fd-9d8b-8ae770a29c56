{"profiles": {"au_uat3": {"commandName": "Project", "commandLineArgs": "--port 7351", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Simply", "EventGridEndpoint": "https://wsa-retail-integration-auuat3.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "yzP3yZvnxSJsFkBA2oUSeErxn0aXbHkrBaLyQAwsn0MhdTGnBfHBJQQJ99BCACL93NaXJ3w3AAABAZEGxNjk", "ExternalSystemCode": "SIMPLY", "FromStorageQueueName": "from-simply", "OHSPayorNo": "9000006", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SalesTaxItem": "GST", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au-uat3;Persist Security Info=False;User ID=simply;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauuat3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-simply", "Schedule1": "0 0 0 1 1 *"}}, "au_prod": {"commandName": "Project", "commandLineArgs": "--port 7351", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Simply", "EventGridEndpoint": "https://wsa-retail-integration-auprod.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "pEues3BSCszYLvhOCdK4JBcxwDfTPvrYIdiIqRh18uIHbzK1YbK6JQQJ99BEACL93NaXJ3w3AAABAZEG26x4", "ExternalSystemCode": "SIMPLY", "FromStorageQueueName": "from-simply", "OHSPayorNo": "9000006", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SalesTaxItem": "GST", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au_prod;Persist Security Info=False;User ID=simply;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-simply", "Schedule1": "0 0 0 1 1 *"}}, "nz_uat3": {"commandName": "Project", "commandLineArgs": "--port 7351", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Simply", "EventGridEndpoint": "https://wsa-retail-integration-nzuat3.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "EOedOSOyPRhqLw4LQpvlQSaeWjvqmOKSS48lTWntCoLy8wjGDUdtJQQJ99BCACL93NaXJ3w3AAABAZEG5rPS", "ExternalSystemCode": "SIMPLY", "FromStorageQueueName": "from-simply", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SalesTaxItem": "GST", "SqlConnectionString": "Server=tcp:retail-integration-nz.database.windows.net,1433;Initial Catalog=nz-uat3;Persist Security Info=False;User ID=simply;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationnzuat3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-simply", "Schedule1": "0 0 0 1 1 *"}}, "nz_prod": {"commandName": "Project", "commandLineArgs": "--port 7351", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "", "AppName": "Simply", "EventGridEndpoint": "https://wsa-retail-integration-nzprod.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "F9RO3UxqCzLobmDJZHxYM6kBp2rqZgfepEytOqTVgIo2gtZMsaHXJQQJ99BEACL93NaXJ3w3AAABAZEGCFhg", "ExternalSystemCode": "SIMPLY", "FromStorageQueueName": "from-simply", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SalesTaxItem": "GST", "SqlConnectionString": "Server=tcp:retail-integration-nz.database.windows.net,1433;Initial Catalog=nz_prod;Persist Security Info=False;User ID=simply;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationnzprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-simply", "Schedule1": "0 0 0 1 1 *"}}}}