﻿namespace WSA.Retail.Integration.Simply.Models.Patients;

public class SimplyCustomerEntity
{
    public required Guid Id { get; set; }
    public required int CustomerNumber { get; set; }
    public required string Name { get; set; }
    public string? Address { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; }
    public string? StateRegion { get; set; }
    public string? Phone {  get; set; }
    public string? Email { get; set; }
    public string? ContactName { get; set; }
    public string? ClinicCode { get; set; }
    public string? FileName { get; set; }
    public bool? IntegrationRequired { get; set; }
    public DateTime? IntegrationDate { get; set; }
    public DateTime? CreatedOn { get; set; }
    public DateTime? ModifiedOn { get; set; }
}