﻿using System.Globalization;
using System.Text.Json;

namespace WSA.Retail.Integration.Manage.Tests.Utilities;

public static class <PERSON><PERSON><PERSON>elper
{
    public static string ReplacePlaceholders(string jsonTemplate, Dictionary<string, object?> values)
    {
        foreach (var (key, value) in values)
        {
            var placeholder = $"{{{{{key}}}}}";

            var replacement = value switch
            {
                null => "null",
                RawJson raw => raw.ToString(),
                string s => JsonEncodedText.Encode(s).ToString().WrapInQuotes(),
                Guid g => g.ToString().WrapInQuotes(),
                DateTime dt => dt.ToString("O").WrapInQuotes(),
                int i => i.ToString(CultureInfo.InvariantCulture),
                long l => l.ToString(CultureInfo.InvariantCulture),
                float f => f.ToString(CultureInfo.InvariantCulture),
                double d => d.ToString(CultureInfo.InvariantCulture),
                decimal m => m.ToString(CultureInfo.InvariantCulture),
                _ => JsonSerializer.Serialize(value)
            };

            jsonTemplate = jsonTemplate.Replace(placeholder, replacement);
        }

        return jsonTemplate;
    }

    private static string WrapInQuotes(this string s) => $"\"{s}\"";
}
