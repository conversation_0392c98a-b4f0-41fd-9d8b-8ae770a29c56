﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Data.Repositories;

public class PurchaseOrderRepository(
    IOptions<AppSettings> appSettings,
    ILogger<PurchaseOrderRepository> logger,
    IDbContextFactory<IntegrationContext> dbContextFactory)
    : IPurchaseOrderRepository
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly ILogger<PurchaseOrderRepository> _logger = logger;
    protected readonly IDbContextFactory<IntegrationContext> _dbContextFactory = dbContextFactory;

    public async Task<PurchaseOrder?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        using var context = _dbContextFactory.CreateDbContext();
        var item = await BuildQuery(context, externalSystemCode, id, documentNumber, externalReference)
            .FirstOrDefaultAsync();
        if (item == null) return null;
        return item;
    }

    public async Task<List<PurchaseOrder>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        using var context = _dbContextFactory.CreateDbContext();
        var list = await BuildQuery(context, externalSystemCode, id, documentNumber, externalReference, documentDate)
            .ToListAsync();
        if (list == null) return [];
        return list;
    }

    private static IQueryable<PurchaseOrder> BuildQuery(
        IntegrationContext context,
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        var latestCouplings = context.CouplingEntity
            .Where(c => context.ExternalSystemEntity
                .Any(es => es.Id == c.ExternalSystemId && es.Code == externalSystemCode));

        var query = from x in context.PurchaseOrderEntity
            .Include(x => x.VendorEntity)
            .Include(x => x.ClinicEntity)
            .Include(x => x.Lines)
                .ThenInclude(line => line == null ? null : line.ProductEntity)

            where (id == null || x.Id == id)
            where (documentNumber == null || x.DocumentNumber == documentNumber)
            where (documentDate == null || x.DocumentDate == documentDate)
            where (string.IsNullOrWhiteSpace(externalReference) ||
                    latestCouplings.Any(c => c.RecordId == x.Id && c.ExternalRecordId == externalReference))

            let latestCoup = latestCouplings
                .Where(c => c.RecordId == x.Id)
                .OrderByDescending(c => c.ModifiedOn)
                .Select(c => c.ExternalRecordId)
                .FirstOrDefault()

            let latestVdrCoup = latestCouplings
                .Where(c => c.RecordId == x.VendorId)
                .OrderByDescending(c => c.ModifiedOn)
                .Select(c => c.ExternalRecordId)
                .FirstOrDefault()

            let latestCliCoup = latestCouplings
                .Where(c => c.RecordId == x.ClinicId)
                .OrderByDescending(c => c.ModifiedOn)
                .Select(c => c.ExternalRecordId)
                .FirstOrDefault()

            orderby (x.DocumentNumber)

            select new PurchaseOrder
            {
                Id = x.Id,
                ExternalSystemCode = externalSystemCode,
                DocumentNumber = x.DocumentNumber,
                ExternalReference = latestCoup,
                AlternateNumber = x.AlternateNumber,
                Vendor = x.VendorEntity != null ? new()
                {
                    Id = x.VendorId,
                    Code = x.VendorEntity != null ? x.VendorEntity!.Code : null,
                    ExternalCode = latestVdrCoup,
                    Name = x.VendorEntity != null ? x.VendorEntity!.Name : null
                } : null,
                Clinic = x.ClinicEntity != null ? new()
                {
                    Id = x.ClinicId,
                    Code = x.ClinicEntity != null ? x.ClinicEntity!.Code : null,
                    ExternalCode = latestCliCoup,
                    Name = x.ClinicEntity != null ? x.ClinicEntity!.Name : null
                } : null,
                DocumentDate = x.DocumentDate,
                Lines = x.Lines
                    .Select(line => new PurchaseOrderLine
                    {
                        Id = line.Id,
                        ExternalReference = latestCouplings
                            .Where(c => c.RecordId == line.Id)
                            .OrderByDescending(c => c.ModifiedOn)
                            .Select(c => c.ExternalRecordId)
                            .FirstOrDefault(),
                        Sequence = line.Sequence,
                        Product = line.ProductEntity != null ? new ExternalReference()
                        {
                            Id = line.ProductId,
                            Code = line.ProductEntity != null ? line.ProductEntity!.Code : null,
                            Name = line.ProductEntity != null ? line.ProductEntity!.Name : null,
                            ExternalCode = line.ProductId.HasValue ? latestCouplings
                                .Where(c => c.RecordId == line.ProductId)
                                .OrderByDescending(c => c.ModifiedOn)
                                .Select(c => c.ExternalRecordId)
                                .FirstOrDefault() : null
                        } : null,
                        Quantity = line.Quantity,
                        UnitPrice = line.UnitPrice,
                        CreatedOn = line.CreatedOn,
                        ModifiedOn = line.ModifiedOn
                    }).ToList(),
                CreatedOn = x.CreatedOn,
                ModifiedOn = x.ModifiedOn
            };

        return query.AsNoTracking();
    }

    public async Task<bool> InsertAsync(PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        purchaseOrder.Lines.ForEach(line => line.Id = line.Id == Guid.Empty ? Guid.NewGuid() : line.Id);

        using var context = _dbContextFactory.CreateDbContext();
        try
        {
            var entity = purchaseOrder.ToEntity();
            context.PurchaseOrderEntity.Add(entity);
            await context.SaveChangesAsync();
            purchaseOrder.Id = entity.Id;
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex, $"Encountered a database error while inserting PurchaseOrder record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }

    public async Task<bool> UpdateAsync(PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (purchaseOrder.Id == Guid.Empty)
        {
            _logger.LogCustomWarning(_appSettings.AppName,
                $"PurchaseOrder {purchaseOrder.DocumentNumber} cannot be updated because it has a null or missing Id.");
            return false;
        }

        using var context = _dbContextFactory.CreateDbContext();

        // Retrieve the existing PurchaseOrder including its lines
        var existingPurchaseOrder = await context.PurchaseOrderEntity
            .Include(po => po.Lines)
            .FirstOrDefaultAsync(po => po.Id == purchaseOrder.Id);
        if (existingPurchaseOrder == null)
        {
            _logger.LogCustomWarning(_appSettings.AppName,
                $"PurchaseOrder with ID {purchaseOrder.Id} not found for update.");
            return false;
        }

        // Update PurchaseOrder fields
        context.Entry(existingPurchaseOrder).CurrentValues.SetValues(purchaseOrder.ToEntity());
        foreach (var updatedLine in purchaseOrder.Lines ?? [])
        {
            if (updatedLine != null)
            {
                var updatedLineEntity = updatedLine.ToEntity(purchaseOrder.Id);
                var existingLine = existingPurchaseOrder.Lines
                    .FirstOrDefault(l => l?.Id == updatedLine.Id);

                if (existingLine == null)
                {
                    // New line - add it
                    existingPurchaseOrder.Lines.Add(updatedLineEntity);
                }
                else
                {
                    // Update existing line
                    context.Entry(existingLine).CurrentValues.SetValues(updatedLine);
                }
            }
        }
        try
        {
            await context.SaveChangesAsync();

            _logger.LogCustomInformation(_appSettings.AppName,
                $"PurchaseOrder {purchaseOrder.DocumentNumber} was successfully updated in the database.");
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex, $"A database error while updating PurchaseOrder record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }
}