﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.Core;

public static class ManageEntityConfigurationExtensions
{
    public static EntityTypeBuilder<T> ConfigureManageIdField<T>(this EntityTypeBuilder<T> builder)
        where T : class, IIdentifiable
    {
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageEventMetadataFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IEventMetadata
    {
        builder.Property(x => x.EventType)
            .HasColumnName("EventType")
            .HasColumnType("nvarchar(50)")
            .IsRequired(true);

        builder.Property(x => x.MessageId)
            .HasColumnName("MessageId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.Timestamp)
            .HasColumnName("Timestamp")
            .HasColumnType("datetime2(7)")
            .IsRequired(true);

        builder.Property(x => x.LocalTimestamp)
            .HasColumnName("LocalTimestamp")
            .HasColumnType("datetime2(7)")
            .IsRequired(true);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageOrderIdentifersFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IOrderIdentifiers
    {
        builder.Property(x => x.OrderId)
            .HasColumnName("OrderId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.ExternalNumber)
            .HasColumnName("ExternalNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageInvoiceIdentifersFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IInvoiceIdentifiers
    {
        builder.Property(x => x.SaleId)
            .HasColumnName("SaleId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.ExternalNumber)
            .HasColumnName("ExternalNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageLocationScopedField<T>(this EntityTypeBuilder<T> builder)
        where T : class, ILocationScoped
    {
        builder.Property(x => x.LocationId)
            .HasColumnName("LocationId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageSupplierScopedField<T>(this EntityTypeBuilder<T> builder)
        where T : class, ISupplierScoped
    {
        builder.Property(x => x.SupplierId)
            .HasColumnName("SupplierId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManagePatientScopedField<T>(this EntityTypeBuilder<T> builder)
        where T : class, IPatientScoped
    {
        builder.Property(x => x.PatientId)
            .HasColumnName("PatientId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageFunderScopedField<T>(this EntityTypeBuilder<T> builder)
        where T : class, IFunderScoped
    {
        builder.Property(x => x.FunderId)
            .HasColumnName("FunderId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(false);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureManageEventProcessingInfoFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IEventProcessingInfo
    {
        builder.Property(x => x.BlobPath)
            .HasColumnName("BlobPath")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.Status)
            .HasColumnName("Status")
            .HasConversion<int>()
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.ProcessedOn)
            .HasColumnName("ProcessedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        return builder;
    }
}
