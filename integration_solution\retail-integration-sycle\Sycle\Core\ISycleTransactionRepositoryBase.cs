﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Sycle.Core;

public interface ISycleTransactionRepositoryBase<TEntity>
    where TEntity : class, ISycleTransaction, IIntegrationEntity, IAuditInfoEntity
{
    public Task<List<TEntity>> GetIntegrationRecordsAsync();

    public Task<TEntity?> GetAsync(int id);

    public Task<bool> UpdateIntegrationStatusAsync(int id, bool isIntegrated = true);
}
