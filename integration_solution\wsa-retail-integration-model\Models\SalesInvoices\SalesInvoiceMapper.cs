﻿using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoiceMapper : IDocumentMapper<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>
{
    public SalesInvoiceEntity ToEntity(SalesInvoice domainModel)
    {
        var entity = new SalesInvoiceEntity()
        {
            Id = domainModel.Id,
            DocumentNumber = domainModel.DocumentNumber,
            AlternateNumber = domainModel.AlternateNumber,
            PatientId = domainModel.Patient?.Id,
            ClinicId = domainModel.Clinic?.Id,
            DocumentDate = domainModel.DocumentDate
        };
        entity.Lines = domainModel.Lines.Select(line => line.ToEntity(entity.Id)).ToList() ?? [];
        return entity;
    }

    public ExternalDocumentReference ToExternalDocumentReference(SalesInvoice domainModel)
    {
        return new ExternalDocumentReference()
        {
            Id = domainModel.Id,
            ExternalReference = domainModel.ExternalReference,
            DocumentNumber = domainModel.DocumentNumber
        };
    }
}