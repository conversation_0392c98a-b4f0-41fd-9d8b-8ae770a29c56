﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Manage.Data;

namespace WSA.Retail.Integration.Manage.Tests.Data;

public static class TestDbContextHelper
{
    public static TestIntegrationContext GetInMemoryDbContext(string dbName = "TestDb")
    {
        var options = new DbContextOptionsBuilder<ManageDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;

        var context = new TestIntegrationContext(options);
        context.Database.EnsureCreated();
        return context;
    }
}