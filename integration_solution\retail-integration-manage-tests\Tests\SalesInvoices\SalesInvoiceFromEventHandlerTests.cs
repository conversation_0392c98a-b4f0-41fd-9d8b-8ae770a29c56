using Moq;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Tests.Configuration;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseOrders;
using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Tests.Utilities;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Manage.Models.SalesInvoices;
using WSA.Retail.Integration.Models.SalesInvoices;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Manage.Tests.PurchaseOrders;
using WSA.Retail.Integration.Manage.Tests.SalesInvoices;

namespace WSA.Retail.Integration.Tests.Tests.SalesInvoices;

public class SalesInvoiceFromEventHandlerTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<IManageAPI> _manageAPIMock;
    private readonly SalesInvoiceEventHandler _handler;
    private readonly Mock<ISalesInvoiceService> _salesInvoiceServiceMock;
    private readonly Mock<IClinicService> _clinicServiceMock;
    private readonly Mock<IPatientService> _patientServiceMock;
    private readonly Mock<IProductService> _productServiceMock;

    public SalesInvoiceFromEventHandlerTests()
    {
        _serviceFactory = new ServiceFactory();
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());

        _manageAPIMock = new Mock<IManageAPI>();
        _salesInvoiceServiceMock = new Mock<ISalesInvoiceService>();
        _clinicServiceMock = new Mock<IClinicService>();
        _patientServiceMock = new Mock<IPatientService>();
        _productServiceMock = new Mock<IProductService>();

        _handler = new SalesInvoiceEventHandler(
            appSettings: _optionsMock.Object,
            logger: new Mock<ILogger<SalesInvoiceEventHandler>>().Object,
            blobServiceClient: new Mock<BlobServiceClient>().Object,
            queueClients: new Mock<Dictionary<string, QueueClient>>().Object,
            manageApi: _manageAPIMock.Object,
            contextFactory: _serviceFactory.ManageDbContextFactory,
            salesInvoiceService: _salesInvoiceServiceMock.Object,
            clinicService: _clinicServiceMock.Object,
            patientService: _patientServiceMock.Object,
            productService: _productServiceMock.Object);
    }


    [Fact]
    public async Task HandleFromQueueAsync_Test()
    {
        // GIVEN a delivery note created event
        Guid saleId = Guid.NewGuid();
        Guid deliveryNoteId = Guid.NewGuid();
        var json = SalesInvoiceHelper.DeliveryNoteCreatedEvent(
            deliveryNoteId: deliveryNoteId,
            saleId: saleId);

        var eventMessage = JsonSerializer.Deserialize<EventHubMessage>(json);
        ArgumentNullException.ThrowIfNull(eventMessage);

        // GIVEN the Manage API contains this purchase order
        var apiResponse = PurchaseOrderHelper.CreateOrder2GetResponse(
            eventMessage: eventMessage,
            locationId: Guid.NewGuid(),
            supplierId: Guid.NewGuid());

        _manageAPIMock
            .Setup(x => x.OrdersGET2Async(It.IsAny<Guid>()))
            .ReturnsAsync(apiResponse);

        // GIVEN clinic exists in the middle layer
        _clinicServiceMock
            .Setup(x => x.ValidateExternalReferenceAsync(It.IsAny<string>(), 
                It.Is<ExternalReference>(er => er.ExternalCode == apiResponse.Location.Id.ToString())))
            .ReturnsAsync(new ExternalReference()
            {
                Id = Guid.NewGuid(),
                Code = "TESTCLI",
                Name = "Test Clinic",
                ExternalCode = apiResponse.Location.Id.ToString()
            });



        // GIVEN product exists in the middle layer
        _productServiceMock
            .Setup(x => x.ValidateExternalReferenceAsync(It.IsAny<string>(),
                It.Is<ExternalReference>(er => er.ExternalCode == apiResponse.LineItems.First().Product.Id.ToString())))
            .ReturnsAsync(new ExternalReference()
            {
                Id = Guid.NewGuid(),
                Code = "TESTPROD",
                Name = "Test Product",
                ExternalCode = apiResponse.LineItems.First().Product.Id.ToString()
            });



        // WHEN the event is handled
        var result = await _handler.HandleFromQueueAsync(eventMessage!);

        // THEN the event is handled successfully
        Assert.True(result);
    }
}