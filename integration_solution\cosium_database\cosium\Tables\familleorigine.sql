﻿CREATE TABLE [cosium].[familleorigine] (
    [id]               NVARCHAR (50)  NOT NULL,
    [creationdate]     NVARCHAR (50)  NULL,
    [nomfamille]       NVARCHAR (100) NULL,
    [modificationdate] NVARCHAR (100) NULL,
    [CreatedOn]        DATETIME2 CONSTRAINT [DF_familleorigin_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]       DATETIME2 CONSTRAINT [DF_familleorigin_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_familleorigin] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].[familleorigin_UpdateModified]
ON [cosium].[familleorigine]
AFTER UPDATE 
AS
   UPDATE [cosium].[familleorigine]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[familleorigine].[id] = i.[id]
GO