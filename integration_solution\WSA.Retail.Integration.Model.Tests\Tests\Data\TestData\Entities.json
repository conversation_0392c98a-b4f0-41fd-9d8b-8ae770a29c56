{"entities": [{"id": "0FC07073-9D4A-48F0-8048-B2BFAD331846", "code": "BATTERY", "name": "Battery", "prefix": "BAT", "lastNumber": 0}, {"id": "F7041F64-C773-4053-BEB0-F8DF98FA9FB6", "code": "CATEGORY", "name": "Category", "prefix": "CAT", "lastNumber": 0}, {"id": "2E7FE253-E676-4147-9D75-523DE6D7D732", "code": "CLAIM", "name": "<PERSON><PERSON><PERSON>", "prefix": "CLM", "lastNumber": 0}, {"id": "F818F05E-2EB9-4AE6-A3E4-450276D12A98", "code": "CLINIC", "name": "Clinic", "prefix": "CLI", "lastNumber": 0}, {"id": "3D222E4E-8D93-48D2-A91C-547B9659BA6F", "code": "COLOR", "name": "Color", "prefix": "COL", "lastNumber": 0}, {"id": "39C2169E-AC81-4BC1-A253-142D478A0D64", "code": "COUNTRY", "name": "Country", "lastNumber": 0}, {"id": "8C420FA9-A12C-4D03-B9AE-4B2F7FCD7269", "code": "MANUFACTURER", "name": "Manufacturer", "prefix": "MFR", "lastNumber": 0}, {"id": "9BF4D132-E9EF-46E8-BB9E-8719B4DF4FCD", "code": "PATIENT", "name": "Patient", "prefix": "PAT", "lastNumber": 0}, {"id": "8DB2E06D-8380-4BE0-9493-BE1C2139A846", "code": "PAYMENT", "name": "Payment", "prefix": "PMT", "lastNumber": 0}, {"id": "73DF42FC-1475-406E-8476-15C784335D6A", "code": "PAYMENTMETHOD", "name": "Payment Methods", "prefix": "PMETH", "lastNumber": 0}, {"id": "27BE7428-72A1-4435-BE8F-AC9D52F83BC0", "code": "PAYOR", "name": "Payor", "prefix": "PAY", "lastNumber": 0}, {"id": "6462129A-CF1D-4782-84CD-2FBCE607F5B5", "code": "PRODUCT", "name": "Product", "prefix": "ITEM", "lastNumber": 0}, {"id": "2AE2976B-B923-40FB-9D2F-865ECCAC03D8", "code": "PRODUCTMODEL", "name": "Product Model", "prefix": "PM", "lastNumber": 0}, {"id": "7C26DB95-047F-464C-B163-32A17ABD3CC4", "code": "PURCHASEORDER", "name": "Purchase Order", "prefix": "PORD", "lastNumber": 0}, {"id": "4B951254-D1B2-413A-A2A5-DE6174374B0C", "code": "PURCHASERECEIPT", "name": "Purchase Receipt", "prefix": "PREC", "lastNumber": 0}, {"id": "FD57EDF6-99A4-4461-ABB2-F70FDCA600F9", "code": "PURCHASERETURN", "name": "Purchase Return Order", "prefix": "PRO", "lastNumber": 0}, {"id": "CB7C2490-599A-49E6-9000-811A78D78A3E", "code": "PURCHASESHIP", "name": "Purchase Shipment", "prefix": "PSHIP", "lastNumber": 0}, {"id": "5DD33915-C182-4CD3-A686-2DACC200D5CC", "code": "SALESCREDIT", "name": "Sales Credit", "prefix": "SCM", "lastNumber": 0}, {"id": "4A782D34-7F54-4CDA-80C7-44A580599396", "code": "SALESINVOICE", "name": "Sales Invoice", "prefix": "SINV", "lastNumber": 0}, {"id": "7EA45FAF-EC25-40C5-8EDB-35CBB079F149", "code": "VENDOR", "name": "<PERSON><PERSON><PERSON>", "prefix": "VDR", "lastNumber": 0}]}