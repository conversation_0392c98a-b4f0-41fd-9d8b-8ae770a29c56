# Entity Class Diagram

This diagram illustrates the entity classes and their relationships in the WSA Retail Integration Model project.

```mermaid
classDiagram
    class BaseEntity {
        +Guid Id
        +bool IsActive
        +DateTime CreatedOn
        +DateTime ModifiedOn
    }

    class ClinicEntity {
        +string Code
        +string Name
        +string Address
        +string Address2
        +string City
        +string Region
        +string Country
        +string PostalCode
        +string Phone
        +Guid? CompanyId
        +CompanyEntity Company
    }

    class PatientEntity {
        +string Code
        +string AlternateCode
        +string Name
        +string Address
        +string Address2
        +string City
        +string Region
        +string Country
        +string PostalCode
        +string Phone
        +string Email
        +string IdentificationNumber
    }

    class ProductEntity {
        +string Code
        +string Name
        +string PimProductId
        +Guid? ProductCategoryId
        +Guid? ProductSubcategoryId
        +Guid? VendorId
        +Guid? ManufacturerId
        +Guid? ColorId
        +Guid? TaxGroupId
        +string VendorItemNo
        +string GTIN
        +string ImageUrl
        +bool? IsSerialized
        +bool? IsInventory
        +bool? IsTaxable
        +decimal? UnitCost
        +Guid? ProductModelId
        +Guid? BatteryId
    }

    class SalesOrderEntity {
        +string DocumentNumber
        +string AlternateNumber
        +Guid PatientId
        +Guid ClinicId
        +DateTime? DocumentDate
        +List~SalesOrderLineEntity~ SalesOrderLines
        +PatientEntity Patient
        +ClinicEntity Clinic
    }

    class SalesOrderLineEntity {
        +Guid SalesOrderId
        +int Sequence
        +Guid ProductId
        +string Description
        +decimal? Quantity
        +string SerialNumber
        +decimal? UnitPrice
        +decimal? GrossAmount
        +decimal? DiscountAmount
        +decimal? AmountExclTax
        +decimal? TaxAmount
        +decimal? AmountInclTax
        +SalesOrderEntity SalesOrder
        +ProductEntity Product
    }
    
    class SalesInvoiceEntity {
        +string DocumentNumber
        +string AlternateNumber
        +Guid? SalesOrderId
        +Guid PatientId
        +Guid ClinicId
        +DateTime? DocumentDate
        +List~SalesInvoiceLineEntity~ SalesInvoiceLines
        +SalesOrderEntity SalesOrder
        +PatientEntity Patient
        +ClinicEntity Clinic
    }

    class SalesInvoiceLineEntity {
        +Guid SalesInvoiceId
        +int Sequence
        +Guid? SalesOrderLineId
        +Guid ProductId
        +string Description
        +decimal? Quantity
        +string SerialNumber
        +decimal? UnitPrice
        +decimal? GrossAmount
        +decimal? DiscountAmount
        +decimal? AmountExclTax
        +decimal? TaxAmount
        +decimal? AmountInclTax
        +SalesInvoiceEntity SalesInvoice
        +SalesOrderLineEntity SalesOrderLine
        +ProductEntity Product
    }

    BaseEntity <|-- ClinicEntity
    BaseEntity <|-- PatientEntity
    BaseEntity <|-- ProductEntity
    BaseEntity <|-- SalesOrderEntity
    BaseEntity <|-- SalesOrderLineEntity
    BaseEntity <|-- SalesInvoiceEntity
    BaseEntity <|-- SalesInvoiceLineEntity
    
    SalesOrderEntity "1" --> "0..*" SalesOrderLineEntity
    SalesOrderEntity "1" --> "0..1" PatientEntity
    SalesOrderEntity "1" --> "0..1" ClinicEntity
    
    SalesOrderLineEntity "1" --> "0..1" ProductEntity
    
    SalesInvoiceEntity "1" --> "0..*" SalesInvoiceLineEntity
    SalesInvoiceEntity "0..1" --> "0..1" SalesOrderEntity
    SalesInvoiceEntity "1" --> "0..1" PatientEntity
    SalesInvoiceEntity "1" --> "0..1" ClinicEntity
    
    SalesInvoiceLineEntity "1" --> "0..1" SalesOrderLineEntity
    SalesInvoiceLineEntity "1" --> "0..1" ProductEntity
```

This diagram shows the key entity classes and their relationships. All entities inherit from a common `BaseEntity` class which provides core fields like `Id`, `IsActive`, `CreatedOn`, and `ModifiedOn`. The diagram illustrates the relationships between major entities like Clinics, Patients, Products, Sales Orders, and Sales Invoices.