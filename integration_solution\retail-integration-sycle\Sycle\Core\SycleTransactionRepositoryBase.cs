using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Sycle.Data;

namespace WSA.Retail.Integration.Sycle.Core;

public abstract class SycleTransactionRepositoryBase<TEntity>(
    IDbContextFactory<SycleContext> _dbContextFactory) : ISycleTransactionRepositoryBase<TEntity>
    where TEntity : class, ISycleTransaction, IIntegrationEntity, IAuditInfoEntity
{

    public async Task<List<TEntity>> GetIntegrationRecordsAsync()
    {
        LogMethodStart();

        using var context = await _dbContextFactory.CreateDbContextAsync();
        var dbSet = context.Set<TEntity>();

        var transactionList = await dbSet
            .Where(x => x.IntegrationRequired == true && x.TransactionNo != null)
            .ToListAsync();

        return transactionList;
    }

    public async Task<TEntity?> GetAsync(int id)
    {
        LogMethodStart();

        using var context = await _dbContextFactory.CreateDbContextAsync();
        var dbSet = context.Set<TEntity>();

        var entity = await dbSet.FirstOrDefaultAsync(x => x.Id == id);

        if (entity == null)
        {
            return null;
        }

        return entity;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(int id, bool isIntegrated = true)
    {
        LogMethodStart();

        using var context = await _dbContextFactory.CreateDbContextAsync();
        var dbSet = context.Set<TEntity>();
        
        var entity = await dbSet.FirstOrDefaultAsync(x => x.Id == id);

        if (entity == null)
        {
            return false;
        }

        entity.IntegrationRequired = false;
        if (isIntegrated) entity.IntegratedOn = DateTime.UtcNow;
        entity.ModifiedOn = DateTime.UtcNow;

        await context.SaveChangesAsync();

        return true;
    }

    protected abstract void LogMethodStart([CallerMemberName] string? methodName = null);
}