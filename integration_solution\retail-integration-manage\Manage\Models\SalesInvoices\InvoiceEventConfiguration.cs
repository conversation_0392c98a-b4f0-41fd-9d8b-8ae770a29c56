﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Manage.Core;

namespace WSA.Retail.Integration.Manage.Models.SalesInvoices;

public class InvoiceEventConfiguration : IEntityTypeConfiguration<InvoiceEventEntity>
{
    public void Configure(EntityTypeBuilder<InvoiceEventEntity> builder)
    {
        builder.ToTable("InvoiceEvent");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureManageIdField();
        builder.ConfigureManageEventMetadataFields();
        builder.ConfigureManageInvoiceIdentifersFields();
        builder.ConfigureManageLocationScopedField();
        builder.ConfigureManagePatientScopedField();
        builder.ConfigureManageFunderScopedField();
        builder.ConfigureManageEventProcessingInfoFields();
        builder.ConfigureAuditInfoFields();
    }
}
