# Integration Entities Diagram

This diagram shows the integration-specific entities and their relationships in the integration database.

```mermaid
erDiagram
    ExternalSystem ||--o{ ExternalSystemType : "has type"
    ExternalSystem ||--o{ ExternalSystemPreferences : "has preferences"
    ExternalSystem ||--o{ EntitySubscriber : "subscribes to"
    
    Entity ||--o{ EntitySubscriber : "subscribed by"
    
    Coupling ||--o{ ExternalSystem : "links"
    
    Watermark ||--o{ ExternalSystem : "tracks for"
    
    ExternalSystem {
        UUID Id PK
        string Code
        string Name
        bool IsActive
        UUID ExternalSystemTypeId FK
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    ExternalSystemType {
        UUID Id PK
        string Code
        string Name
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    ExternalSystemPreferences {
        UUID Id PK
        UUID ExternalSystemId FK
        string Name
        string Value
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Entity {
        UUID Id PK
        string Code
        string Name
        string TableName
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    EntitySubscriber {
        UUID Id PK
        UUID EntityId FK
        UUID ExternalSystemId FK
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Coupling {
        UUID Id PK
        string SourceSystemCode
        string SourceEntityCode
        string SourceCode
        string TargetSystemCode
        string TargetEntityCode
        string TargetCode
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Watermark {
        UUID Id PK
        UUID ExternalSystemId FK
        string EntityName
        datetime LastProcessedTime
        datetime LastSuccessTime
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
```