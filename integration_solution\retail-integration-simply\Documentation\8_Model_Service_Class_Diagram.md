# Service Layer Class Diagram

This diagram illustrates the service layer classes in the WSA Retail Integration Model project.

```mermaid
classDiagram
    class IClinicService {
        <<interface>>
        +GetAsync(externalSystemCode, id, code, externalCode) Task~Clinic~
        +GetListAsync(externalSystemCode, id, code, name, externalCode) Task~List~Clinic~~
        +GetExternalReferenceAsync(externalSystemCode, id, code, externalCode) Task~ExternalReference~
        +UpsertAsync(dto) Task~Clinic~
    }

    class ClinicService {
        -IClinicRepository _repository
        -AppSettings _appSettings
        -ILogger~ClinicService~ _logger
        -IEventGridPublisher _eventGridPublisher
        +GetAsync(externalSystemCode, id, code, externalCode) Task~Clinic~
        +GetListAsync(externalSystemCode, id, code, name, externalCode) Task~List~Clinic~~
        +GetExternalReferenceAsync(externalSystemCode, id, code, externalCode) Task~ExternalReference~
        +UpsertAsync(dto) Task~Clinic~
    }

    class ISalesOrderService {
        <<interface>>
        +GetAsync(externalSystemCode, id, documentNumber, externalReference) Task~SalesOrder~
        +GetListAsync(externalSystemCode, id, documentNumber, externalReference) Task~List~SalesOrder~~
        +GetExternalDocumentReferenceAsync(externalSystemCode, externalReference) Task~ExternalDocumentReference~
        +UpsertAsync(dto) Task~SalesOrder~
    }

    class SalesOrderService {
        -ISalesOrderRepository _repository
        -IClinicService _clinicService
        -IPatientService _patientService
        -IProductService _productService
        -AppSettings _appSettings
        -ILogger~SalesOrderService~ _logger
        -IEventGridPublisher _eventGridPublisher
        +GetAsync(externalSystemCode, id, documentNumber, externalReference) Task~SalesOrder~
        +GetListAsync(externalSystemCode, id, documentNumber, externalReference) Task~List~SalesOrder~~
        +GetExternalDocumentReferenceAsync(externalSystemCode, externalReference) Task~ExternalDocumentReference~
        +UpsertAsync(dto) Task~SalesOrder~
    }

    class IMasterDataEntityService~TModel,TDto~ {
        <<interface>>
        +GetAsync(externalSystemCode, id, code, externalCode) Task~TModel~
        +GetListAsync(externalSystemCode, id, code, name, externalCode) Task~List~TModel~~
        +GetExternalReferenceAsync(externalSystemCode, id, code, externalCode) Task~ExternalReference~
        +UpsertAsync(dto) Task~TModel~
    }

    class ITransactionEntityService~TModel,TDto~ {
        <<interface>>
        +GetAsync(externalSystemCode, id, documentNumber, externalReference) Task~TModel~
        +GetListAsync(externalSystemCode, id, documentNumber, externalReference) Task~List~TModel~~
        +GetExternalDocumentReferenceAsync(externalSystemCode, externalReference) Task~ExternalDocumentReference~
        +UpsertAsync(dto) Task~TModel~
    }

    class BaseEntityService~T~ {
        #AppSettings _appSettings
        #ILogger~T~ _logger
        #IDbContextFactory~IntegrationContext~ _dbContextFactory
        #IEventGridPublisher _eventGridPublisher
    }

    class IEventGridPublisher {
        <<interface>>
        +RaiseEventAsync(settings, eventType, subject, data) Task
        +RaiseMockEventAsync(eventType, subject, data) Task
    }

    class EventGridPublisher {
        -HttpClient _httpClient
        -AppSettings _appSettings
        -Dictionary~string,QueueClient~ _queueClients
        +RaiseEventAsync(settings, eventType, subject, data) Task
        +RaiseMockEventAsync(eventType, subject, data) Task
    }

    IMasterDataEntityService <|.. IClinicService
    IClinicService <|.. ClinicService
    BaseEntityService <|-- ClinicService

    ITransactionEntityService <|.. ISalesOrderService
    ISalesOrderService <|.. SalesOrderService
    BaseEntityService <|-- SalesOrderService

    IEventGridPublisher <|.. EventGridPublisher
    BaseEntityService --> IEventGridPublisher : uses
```

This diagram shows the service layer with interfaces and implementations. Master data services like `ClinicService` implement the `IMasterDataEntityService` interface, while transaction services like `SalesOrderService` implement the `ITransactionEntityService` interface. All services inherit from the `BaseEntityService` class, which provides common functionality. The `EventGridPublisher` is used for event-based communication.