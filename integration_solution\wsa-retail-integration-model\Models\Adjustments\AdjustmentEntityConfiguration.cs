using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Adjustments
{
    public class AdjustmentEntityConfiguration : IEntityTypeConfiguration<AdjustmentEntity>
    {
        public void Configure(EntityTypeBuilder<AdjustmentEntity> builder)
        {
            builder.ToTable("Adjustment", "dbo");

            builder.<PERSON><PERSON><PERSON>(b => b.Id);

            builder.ConfigureTransactionFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(b => b.ProductId)
                .HasColumnName("ProductId")
                .HasColumnType("uniqueidentifier");

            builder.Property(b => b.SerialNumber)
                .HasColumnName("SerialNumber")
                .HasColumnType("nvarchar(30)")
                .HasMaxLength(30);

            builder.Property(b => b.IntegrateWithPos)
                .HasColumnName("IntegrateWithPos")
                .HasColumnType("bit");

            builder.HasOne(x => x.ProductEntity)
                .WithMany()
                .HasForeignKey(x => x.ProductId);

            builder.HasOne(x => x.ClinicEntity)
                .WithMany()
                .HasForeignKey(x => x.ClinicId);
        }
    }
}
