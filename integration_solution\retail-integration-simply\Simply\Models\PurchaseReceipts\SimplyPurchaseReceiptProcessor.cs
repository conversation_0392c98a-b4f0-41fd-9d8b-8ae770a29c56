using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.PurchaseShipments;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReturns;

namespace WSA.Retail.Integration.Simply.Models.PurchaseReceipts;

public class SimplyPurchaseReceiptProcessor(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPurchaseReceiptProcessor> logger,
    ISimplyPurchaseReceiptRepository simplyPurchaseReceiptRepository,
    IPurchaseReceiptService purchaseReceiptService,
    IPurchaseShipmentService purchaserShipmentService,
    IPurchaseOrderService purchaseOrderService,
    IPurchaseReturnService purchaseReturnService)
    : ISimplyPurchaseReceiptProcessor
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPurchaseReceiptProcessor> _logger = logger;
    private readonly ISimplyPurchaseReceiptRepository _simplyPurchaseReceiptRepository = simplyPurchaseReceiptRepository;
    private readonly IPurchaseReceiptService _purchaseReceiptService = purchaseReceiptService;
    private readonly IPurchaseShipmentService _purchaseShipmentService = purchaserShipmentService;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IPurchaseReturnService _purchaseReturnService = purchaseReturnService;

    public enum PurchaseOrderType
    {
        PO,
        RO
    }

    public async Task ProcessSimplyPurchaseTransactionsAsync()
    {
        _logger.LogMethodStart();

        var simplyReceipts = await _simplyPurchaseReceiptRepository.GetIntegrationRecordsAsync();
        if (simplyReceipts.Count == 0)
        {
            _logger.LogCustomInformation(
                "No purchase receipts are available for processing.");
            return;
        }

        await Parallel.ForEachAsync(
            simplyReceipts,
            new ParallelOptions { MaxDegreeOfParallelism = 1 },
            async (sycleReceipt, cancellationToken) =>
            {
                await ProcessSimplyPurchaseTransactionAsync(sycleReceipt.ToList());
            });
    }

    public async Task<bool> ProcessSimplyPurchaseTransactionAsync(List<SimplyPurchaseReceiptEntity> simplyReceipt)
    {
        _logger.LogMethodStart();

        if (simplyReceipt.Count == 0)
        {
            _logger.LogCustomError("Simply receipt does not contain any lines.");
            return false;
        }

        if (simplyReceipt.First().OrderType == PurchaseOrderType.PO.ToString())
        {
            //return false;
            return await ProcessSimplyPurchaseReceiptAsync(simplyReceipt);
        }

        if (simplyReceipt.First().OrderType == PurchaseOrderType.RO.ToString())
        {
            return await ProcessSimplyPurchaseShipmentAsync(simplyReceipt);
        }

        return false;
    }


    public async Task<bool> ProcessSimplyPurchaseReceiptAsync(List<SimplyPurchaseReceiptEntity> simplyReceipt)
    {
        _logger.LogMethodStart();

        var headerLine = simplyReceipt.First();
        ArgumentNullException.ThrowIfNull(headerLine.OrderType);
        ArgumentNullException.ThrowIfNull(headerLine.DocNumber);

        var purchaseReceipt = new PurchaseReceipt()
        {
            ExternalSystemCode = _appSettings.ExternalSystemCode,
            DocumentNumber = GetDocumentNumber(PurchaseOrderType.PO, (int)headerLine.DocNumber),
            ExternalReference = headerLine.DocNumber.ToString(),
            DocumentDate = headerLine.TransactionDate,
            PurchaseOrder = new() { DocumentNumber = GetOrderNumber(PurchaseOrderType.PO, (int)headerLine.DocNumber) }
        };

        int i = 0;
        foreach (var simplyReceiptLine in simplyReceipt)
        {
            i++;
            var purchaseReceiptLine = new PurchaseReceiptLine()
            {
                Sequence = i,
                Quantity = simplyReceiptLine.Quantity,
                SerialNumber = simplyReceiptLine.SerialNumber
            };
            if (purchaseReceipt.PurchaseOrder?.DocumentNumber != null)
            {
                purchaseReceiptLine.PurchaseOrderLine = new()
                {
                    Sequence = simplyReceiptLine.LineNumber
                };
            }
            if (simplyReceiptLine.ItemNumber != null)
            {
                purchaseReceiptLine.Product = new()
                {
                    Code = simplyReceiptLine.ItemNumber,
                    ExternalCode = simplyReceiptLine.ItemNumber
                };
            }

            purchaseReceipt.PurchaseReceiptLines.Add(purchaseReceiptLine);
        }

        if (purchaseReceipt.PurchaseOrder?.DocumentNumber != null)
        {
            var purchaseOrder = await _purchaseOrderService.GetAsync(
                externalSystemCode: _appSettings.ExternalSystemCode,
                documentNumber: purchaseReceipt.PurchaseOrder?.DocumentNumber);

            if (purchaseOrder != null)
            {
                purchaseReceipt.PurchaseOrder!.Id = purchaseOrder.Id;
                purchaseReceipt.PurchaseOrder.DocumentNumber = purchaseOrder.DocumentNumber;
                purchaseReceipt.PurchaseOrder.ExternalReference = purchaseOrder.ExternalReference;
                purchaseReceipt.Clinic = purchaseOrder.Clinic;
                purchaseReceipt.Vendor = purchaseOrder.Vendor;

                foreach (var purchaseReceiptLine in purchaseReceipt.PurchaseReceiptLines.Where(x => x.PurchaseOrderLine?.Sequence != null))
                {
                    var purchaseOrderLine = purchaseOrder.Lines
                        .Where(x => x.Sequence == purchaseReceiptLine.PurchaseOrderLine!.Sequence)
                        .FirstOrDefault();

                    if (purchaseOrderLine != null)
                    {
                        purchaseReceiptLine.Product = purchaseOrderLine.Product;
                        purchaseReceiptLine.PurchaseOrderLine ??= new();
                        purchaseReceiptLine.PurchaseOrderLine.Id = purchaseOrderLine.Id;
                        purchaseReceiptLine.PurchaseOrderLine.Sequence = purchaseOrderLine.Sequence;
                    }
                }
            }
        }

        // Verify required data
        bool isValid = VerifyRequiredData(purchaseReceipt);
        if (!isValid)
        {
            _logger.LogCustomError($"Purchase receipt {headerLine.DocNumber} has missing required data");
            return false;
        }

        // Upsert the purchase receipt
        var result = await _purchaseReceiptService.UpsertAsync(purchaseReceipt);
        if (result != null)
        {
            // Update integration status
            foreach (var simplyReceiptLine in simplyReceipt)
            {
                await _simplyPurchaseReceiptRepository.UpdateIntegrationStatusAsync(simplyReceiptLine.Id);
            }
            
            _logger.LogCustomInformation($"Successfully processed purchase receipt {purchaseReceipt.DocumentNumber}");
        }
        else
        {
            _logger.LogCustomError($"Failed to process purchase receipt {purchaseReceipt.DocumentNumber}");
        }

        return false;
    }

    public async Task<bool> ProcessSimplyPurchaseShipmentAsync(List<SimplyPurchaseReceiptEntity> simplyReceipt)
    {
        _logger.LogMethodStart();

        var headerLine = simplyReceipt.First();
        ArgumentNullException.ThrowIfNull(headerLine.OrderType);
        ArgumentNullException.ThrowIfNull(headerLine.DocNumber);

        var purchaseShipment = new PurchaseShipment()
        {
            ExternalSystemCode = _appSettings.ExternalSystemCode,
            DocumentNumber = GetDocumentNumber(PurchaseOrderType.RO, (int)headerLine.DocNumber),
            ExternalReference = headerLine.DocNumber.ToString(),
            DocumentDate = headerLine.TransactionDate,
            PurchaseReturn = new() { DocumentNumber = GetOrderNumber(PurchaseOrderType.RO, (int)headerLine.DocNumber) }
        };

        int i = 0;
        foreach (var simplyReceiptLine in simplyReceipt)
        {
            i++;
            var purchaseShipmentLine = new PurchaseShipmentLine()
            {
                Sequence = i,
                Quantity = -simplyReceiptLine.Quantity,
                SerialNumber = simplyReceiptLine.SerialNumber
            };
            if (purchaseShipment.PurchaseReturn?.DocumentNumber != null)
            {
                purchaseShipmentLine.PurchaseReturnLine = new()
                {
                    Sequence = simplyReceiptLine.LineNumber
                };
            }
            if (simplyReceiptLine.ItemNumber != null)
            {
                purchaseShipmentLine.Product = new()
                {
                    Code = simplyReceiptLine.ItemNumber,
                    ExternalCode = simplyReceiptLine.ItemNumber
                };
            }

            purchaseShipment.PurchaseShipmentLines.Add(purchaseShipmentLine);
        }

        if (purchaseShipment.PurchaseReturn?.DocumentNumber != null)
        {
            var purchaseReturn = await _purchaseReturnService.GetAsync(
                externalSystemCode: _appSettings.ExternalSystemCode,
                documentNumber: purchaseShipment.PurchaseReturn?.DocumentNumber);

            if (purchaseReturn != null)
            {
                purchaseShipment.PurchaseReturn!.Id = purchaseReturn.Id;
                purchaseShipment.PurchaseReturn.DocumentNumber = purchaseReturn.DocumentNumber;
                purchaseShipment.PurchaseReturn.ExternalReference = purchaseReturn.ExternalReference;
                purchaseShipment.Clinic = purchaseReturn.Clinic;
                purchaseShipment.Vendor = purchaseReturn.Vendor;

                foreach (var purchaseShipmentLine in purchaseShipment.PurchaseShipmentLines.Where(x => x.PurchaseReturnLine?.Sequence != null))
                {
                    var purchaseReturnLine = purchaseReturn.PurchaseReturnLines
                        .Where(x => x.Sequence == purchaseShipmentLine.PurchaseReturnLine!.Sequence)
                        .FirstOrDefault();

                    if (purchaseReturnLine != null)
                    {
                        purchaseShipmentLine.Product = purchaseReturnLine.Product;
                        purchaseShipmentLine.PurchaseReturnLine ??= new();
                        purchaseShipmentLine.PurchaseReturnLine.Id = purchaseReturnLine.Id;
                        purchaseShipmentLine.PurchaseReturnLine.Sequence = purchaseReturnLine.Sequence;
                    }
                }
            }
        }

        // Verify required data
        bool isValid = VerifyRequiredData(purchaseShipment);
        if (!isValid)
        {
            _logger.LogCustomError($"PurchaseShipment {headerLine.DocNumber} has missing required data");
            return false;
        }

        // Upsert the purchase receipt
        var result = await _purchaseShipmentService.UpsertAsync(purchaseShipment);
        if (result != null)
        {
            // Update integration status
            foreach (var simplyReceiptLine in simplyReceipt)
            {
                await _simplyPurchaseReceiptRepository.UpdateIntegrationStatusAsync(simplyReceiptLine.Id);
            }

            _logger.LogCustomInformation($"Successfully processed purchase shipment {purchaseShipment.DocumentNumber}");
        }
        else
        {
            _logger.LogCustomError($"Failed to process purchase receipt {purchaseShipment.DocumentNumber}");
        }

        return false;
    }

    private bool VerifyRequiredData(PurchaseReceipt purchaseReceipt)
    {
        if (string.IsNullOrEmpty(purchaseReceipt.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }

        if (purchaseReceipt.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }

        if (purchaseReceipt.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (purchaseReceipt.Vendor?.Code == null)
        {
            _logger.LogCustomError("Vendor.Code is required");
            return false;
        }

        if (purchaseReceipt.PurchaseReceiptLines == null || purchaseReceipt.PurchaseReceiptLines.Count == 0)
        {
            _logger.LogCustomError("At least one purchase receipt line is required");
            return false;
        }

        foreach (var line in purchaseReceipt.PurchaseReceiptLines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }

            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }

        return true;
    }

    private bool VerifyRequiredData(PurchaseShipment purchaseShipment)
    {
        if (string.IsNullOrEmpty(purchaseShipment.DocumentNumber))
        {
            _logger.LogCustomError("DocumentNumber is required");
            return false;
        }

        if (purchaseShipment.DocumentDate == null)
        {
            _logger.LogCustomError("DocumentDate is required");
            return false;
        }

        if (purchaseShipment.Clinic?.Code == null)
        {
            _logger.LogCustomError("Clinic.Code is required");
            return false;
        }

        if (purchaseShipment.Vendor?.Code == null)
        {
            _logger.LogCustomError("Vendor.Code is required");
            return false;
        }

        if (purchaseShipment.PurchaseShipmentLines == null || purchaseShipment.PurchaseShipmentLines.Count == 0)
        {
            _logger.LogCustomError("At least one purchase receipt line is required");
            return false;
        }

        foreach (var line in purchaseShipment.PurchaseShipmentLines)
        {
            if (line.Sequence == null)
            {
                _logger.LogCustomError("Line.Sequence is required");
                return false;
            }

            if (line.Product?.Code == null)
            {
                _logger.LogCustomError("Line.Product.Code is required");
                return false;
            }
        }

        return true;
    }

    private string GetDocumentNumber(PurchaseOrderType type, int poNumber)
    {
        string prefix = type == PurchaseOrderType.PO ? "PREC-" : "PSHIP-";
        return $"{prefix}{poNumber.ToString().PadLeft(10, '0')}";
    }

    private string GetOrderNumber(PurchaseOrderType type, int poNumber)
    {
        string prefix = type == PurchaseOrderType.PO ? "PORD-" : "PRO-";
        return $"{prefix}{poNumber.ToString().PadLeft(10, '0')}";
    }
}