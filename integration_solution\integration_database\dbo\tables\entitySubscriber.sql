﻿CREATE TABLE [dbo].[EntitySubscriber]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_EntitySubscriber_Id] DEFAULT NEWID() NOT NULL,
    [EntityId]              UNIQUEIDENTIFIER NOT NULL,
    [ExternalSystemId]      UNIQUEIDENTIFIER NOT NULL,
    [FromExternalSystem]    BIT CONSTRAINT [DF_EntitySubscriber_FromExternalSystem] DEFAULT((0)) NOT NULL,
    [ToExternalSystem]      BIT CONSTRAINT [DF_EntitySubscriber_ToExternalSystem] DEFAULT((0)) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_EntitySubscriber_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_EntitySubscriber_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_EntitySubscriber_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_EntitySubscriber_Entity] FOREIGN KEY (EntityId) REFERENCES [dbo].[Entity](Id),
    CONSTRAINT              [FK_EntitySubscriber_ExternalSystem] FOREIGN KEY (ExternalSystemId) REFERENCES [dbo].[ExternalSystem](Id)
)
GO

CREATE UNIQUE INDEX IX_EntityId_ExternalSystemId
ON [dbo].[EntitySubscriber] ([EntityId], [ExternalSystemId])
GO

CREATE TRIGGER EntitySubscriber_UpdateModified
ON [dbo].[EntitySubscriber]
AFTER UPDATE 
AS
   UPDATE [dbo].[EntitySubscriber]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [dbo].[EntitySubscriber].[Id] = i.[Id]
GO