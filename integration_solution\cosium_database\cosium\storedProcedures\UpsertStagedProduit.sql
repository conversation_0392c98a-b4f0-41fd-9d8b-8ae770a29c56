﻿CREATE PROCEDURE [cosium].[UpsertStagedProduit]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.produit
          SET [reffournisseur] = Source.[reffournisseur],
              [refmarque] = Source.[refmarque],
              [famille] = Source.[famille],
              [codeproduit] = Source.[codeproduit],
              [cote] = Source.[cote],
              [osseuxaerien] = Source.[osseuxaerien],
              [intracontour] = Source.[intracontour],
              [matiere] = Source.[matiere],
              [numagrement] = Source.[numagrement],
              [cache] = Source.[cache],
              [dureegarantie] = Source.[dureegarantie],
              [typedureegarantie] = Source.[typedureegarantie],
              [avecnumserie] = Source.[avecnumserie],
              [numeriqueanalogique] = Source.[numeriqueanalogique],
              [codebarrecomm] = Source.[codebarrecomm],
              [libelle] = Source.[libelle],
              [seuilproduit] = Source.[seuilproduit],
              [libref] = Source.[libref],
              [prixventeht] = Source.[prixventeht],
              [rangniveau] = Source.[rangniveau],
              [prixachatproduit] = Source.[prixachatproduit],
              [prixachatcatalogue] = Source.[prixachatcatalogue],
              [reftva] = Source.[reftva],
              [prixventettc] = Source.[prixventettc],
              [codemarketinggammefournisseur] = Source.[codemarketinggammefournisseur],
              [prixgarantie] = Source.[prixgarantie],
              [prixprestation] = Source.[prixprestation],
              [typeprestation] = Source.[typeprestation],
              [datemodif] = Source.[datemodif],
              [refproduitsuccursale] = Source.[refproduitsuccursale],
              [productsbenefitslistcodehearingclass] = Source.[productsbenefitslistcodehearingclass],
              [taille1] = Source.[taille1],
              [taille2] = Source.[taille2],
              [taille3] = Source.[taille3],
              [geometrie] = Source.[geometrie],
              [photochromic] = Source.[photochromic],
              [teinte] = Source.[teinte],
              [codeedi] = Source.[codeedi],
              [codecommandeedi] = Source.[codecommandeedi],
              [codegtin] = Source.[codegtin],
              [contactlensduration] = Source.[contactlensduration],
              [lenspackaging] = Source.[lenspackaging],
			  [sex] = Source.[sex],
			  [deleted] = Source.[deleted],
              [ModifiedOn] = SYSUTCDATETIME(),
              [IntegrationRequired] = 1

         FROM cosium.produit

              INNER JOIN cosium.staging_produit AS Source
                      ON produit.id = Source.id

        WHERE produit.datemodif <> Source.datemodif;

       INSERT INTO cosium.produit (
              [id],
              [reffournisseur],
              [refmarque],
              [famille],
              [codeproduit],
              [cote],
              [osseuxaerien],
              [intracontour],
              [matiere],
              [numagrement],
              [cache],
              [dureegarantie],
              [typedureegarantie],
              [avecnumserie],
              [numeriqueanalogique],
              [codebarrecomm],
              [libelle],
              [seuilproduit],
              [libref],
              [prixventeht],
              [rangniveau],
              [prixachatproduit],
              [prixachatcatalogue],
              [reftva],
              [prixventettc],
              [codemarketinggammefournisseur],
              [prixgarantie],
              [prixprestation],
              [typeprestation],
              [datemodif],
              [refproduitsuccursale],
              [productsbenefitslistcodehearingclass],
              [taille1],
              [taille2],
              [taille3],
              [geometrie],
              [photochromic],
              [teinte],
              [codeedi],
              [codecommandeedi],
              [codegtin],
              [contactlensduration],
              [lenspackaging],
			  [sex],
			  [deleted],
              [IntegrationRequired])

       SELECT Source.[id],
              Source.[reffournisseur],
              Source.[refmarque],
              Source.[famille],
              Source.[codeproduit],
              Source.[cote],
              Source.[osseuxaerien],
              Source.[intracontour],
              Source.[matiere],
              Source.[numagrement],
              Source.[cache],
              Source.[dureegarantie],
              Source.[typedureegarantie],
              Source.[avecnumserie],
              Source.[numeriqueanalogique],
              Source.[codebarrecomm],
              Source.[libelle],
              Source.[seuilproduit],
              Source.[libref],
              Source.[prixventeht],
              Source.[rangniveau],
              Source.[prixachatproduit],
              Source.[prixachatcatalogue],
              Source.[reftva],
              Source.[prixventettc],
              Source.[codemarketinggammefournisseur],
              Source.[prixgarantie],
              Source.[prixprestation],
              Source.[typeprestation],
              Source.[datemodif],
              Source.[refproduitsuccursale],
              Source.[productsbenefitslistcodehearingclass],
              Source.[taille1],
              Source.[taille2],
              Source.[taille3],
              Source.[geometrie],
              Source.[photochromic],
              Source.[teinte],
              Source.[codeedi],
              Source.[codecommandeedi],
              Source.[codegtin],
              Source.[contactlensduration],
               Source.[lenspackaging],
			  Source.[sex],
			  Source.[deleted],
              1

         FROM cosium.staging_produit AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.produit WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_produit
       
       COMMIT TRANSACTION;