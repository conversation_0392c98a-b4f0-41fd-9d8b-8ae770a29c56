﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.Images;

public class PimImageRepository(
    ILogger<PimImageRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
        PimProductRelatedEntityRepositoryBase<PimImageUpsertTableType>(
            logger,
            dbContextFactory),
        IPimImageRepository
{
    protected override string StoredProcedureName => "pim.ImageUpsert";
    protected override string TableTypeName => "pim.ImageUpsertTableType";

    protected override List<PimImageUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .Where(p => p.Assets != null)
            .SelectMany(p => p.Assets
            .Where(p => p.Images != null)
            .SelectMany(a => a.Images
            .Select(a => new PimImageUpsertTableType
            {
                ProductId = p.ProductId,
                BrandCode = a.BrandCode,
                CDNUrl = a.CdnUrl,
                IsDefault = a.IsDefault
            })))
            .ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("ProductId", typeof(Guid));
        dataTable.Columns.Add("BrandCode", typeof(string));
        dataTable.Columns.Add("CDNUrl", typeof(string));
        dataTable.Columns.Add("IsDefault", typeof(bool));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimImageUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["ProductId"] = entity.ProductId;
            dataRow["BrandCode"] = entity.BrandCode;
            dataRow["CDNUrl"] = entity.CDNUrl;
            dataRow["IsDefault"] = entity.IsDefault;
        }
        return dataRow;
    }
}