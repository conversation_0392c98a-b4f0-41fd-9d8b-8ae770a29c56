﻿CREATE PROCEDURE [cosium].[UpsertStagedLienReglementFacture]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.lienreglementfacture
          SET [reffacture] = Source.[reffacture],
              [refreglement] = Source.[refreglement],
              [montantpourfacture] = Source.[montantpourfacture],
              [datecreation] = Source.[datecreation],
              [acompte] = Source.[acompte],
              [datemodif] = Source.[datemodif]

         FROM cosium.lienreglementfacture

              INNER JOIN cosium.staging_lienreglementfacture AS Source
                      ON lienreglementfacture.id = Source.id

        WHERE lienreglementfacture.datemodif <> Source.datemodif;

       INSERT INTO cosium.lienreglementfacture (
              [id],
              [reffacture],
              [refreglement],
              [montantpourfacture],
              [datecreation],
              [acompte],
              [datemodif]
              )

       SELECT Source.[id],
              Source.[reffacture],
              Source.[refreglement],
              Source.[montantpourfacture],
              Source.[datecreation],
              Source.[acompte],
              Source.[datemodif]

         FROM cosium.staging_lienreglementfacture AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.lienreglementfacture WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_lienreglementfacture
       
       COMMIT TRANSACTION;