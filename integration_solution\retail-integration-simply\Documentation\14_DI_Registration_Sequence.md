# Dependency Injection Registration Sequence Diagram

This diagram illustrates the sequence of operations for registering dependencies in the WSA Retail Integration Model project.

```mermaid
sequenceDiagram
    participant Host as Host Builder
    participant ServiceExt as Service Collection Extensions
    participant Services as Service Collection
    participant Config as Configuration
    
    Host->>ServiceExt: AddCoreServices(services, configuration)
    ServiceExt->>Config: Bind(appSettings)
    Config-->>ServiceExt: Return AppSettings
    
    ServiceExt->>Services: Configure<AppSettings>(configuration)
    ServiceExt->>Services: AddSingleton(appSettings)
    
    ServiceExt->>Services: AddDbContextFactory<IntegrationContext>
    Note over ServiceExt,Services: Configure with SQL connection string
    
    ServiceExt->>Services: AddHttpClient<IEventGridPublisher, EventGridPublisher>
    Note over ServiceExt,Services: Configure with EventGrid endpoint and access key
    
    ServiceExt->>Services: AddSingleton(Dictionary<string, QueueClient>)
    Note over ServiceExt,Services: Configure queue clients for storage queues
    
    %% Register repositories
    ServiceExt->>Services: AddScoped<IClinicRepository, ClinicRepository>
    ServiceExt->>Services: AddScoped<IPatientRepository, PatientRepository>
    ServiceExt->>Services: AddScoped<IProductRepository, ProductRepository>
    ServiceExt->>Services: AddScoped<ISalesOrderRepository, SalesOrderRepository>
    Note over ServiceExt,Services: Register all other repositories...
    
    %% Register services
    ServiceExt->>Services: AddScoped<IClinicService, ClinicService>
    ServiceExt->>Services: AddScoped<IPatientService, PatientService>
    ServiceExt->>Services: AddScoped<IProductService, ProductService>
    ServiceExt->>Services: AddScoped<ISalesOrderService, SalesOrderService>
    Note over ServiceExt,Services: Register all other services...
    
    ServiceExt->>Services: AddLogging()
    ServiceExt-->>Host: Return Services
```

This sequence diagram shows the flow of operations for registering dependencies in the project:

1. The Host Builder calls AddCoreServices on the Service Collection Extensions
2. AppSettings are bound from the configuration and registered as a singleton
3. The DbContextFactory for IntegrationContext is registered with appropriate configuration
4. The HttpClient for the EventGridPublisher is registered with endpoint and access key
5. Queue clients for storage queues are registered as a singleton dictionary
6. All repositories are registered as scoped services
7. All services are registered as scoped services
8. Logging is configured

This dependency injection setup ensures that all components are properly wired together and can be resolved when needed. The use of interfaces and DI makes the system more modular and testable.