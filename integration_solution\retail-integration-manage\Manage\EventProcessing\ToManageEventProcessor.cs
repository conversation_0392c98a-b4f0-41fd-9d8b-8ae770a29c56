using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class ToManageEventProcessor
{
    private readonly AppSettings _appSettings;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<ToManageEventProcessor> _logger;
    private readonly IDbContextFactory<IntegrationContext> _dbContextFactory;
    private readonly QueueClient _queueClient;
    private readonly EventProcessor _eventProcessor;
    
    public ToManageEventProcessor(
        IOptions<AppSettings> appSettings,
        ILoggerFactory loggerFactory,
        IDbContextFactory<IntegrationContext> dbContextFactory,
        Dictionary<string, QueueClient> queueClients, 
        EventProcessor eventProcessor)
    {
        _appSettings = appSettings.Value;
        _loggerFactory = loggerFactory;
        _logger = loggerFactory.CreateLogger<ToManageEventProcessor>();
        _dbContextFactory = dbContextFactory;

        if (!queueClients.TryGetValue("to-manage", out var queueClient) || queueClient == null)
        {
            throw new InvalidOperationException("QueueClient for 'to-manage' not found.");
        }
        _queueClient = queueClient;
        _eventProcessor = eventProcessor;
    }


    [Function(nameof(HandleToQueueMessageAsync))]
    public async Task HandleToQueueMessageAsync([QueueTrigger("%ToStorageQueueName%", Connection = "StorageQueueConnectionString")] QueueMessage queueMessage)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        string messageId = queueMessage.MessageId;
        string popReceipt = queueMessage.PopReceipt;
        string message = queueMessage.Body.ToString();
        var dequeueCount = queueMessage.DequeueCount;

        try
        {
            var eventMessage = JsonSerializer.Deserialize<Event>(message);
            if (eventMessage?.EventType != null)
            {
               if (await _eventProcessor.ProcessToQueueEventAsync(eventMessage.EventType, eventMessage))
                {
                    _queueClient.DeleteMessage(messageId, popReceipt);
                    return;
                }
                else
                {
                    SetMessageToRetry(messageId, popReceipt, message, dequeueCount);
                    return;
                };
            }
            else
            {
                SetMessageToRetry(messageId, popReceipt, message, dequeueCount);
                return;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            SetMessageToRetry(messageId, popReceipt, message, dequeueCount);
            return;
        }
    }

    private void SetMessageToRetry(string messageId, string popReceipt, string message, long dequeueCount)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        byte[] utf8Bytes = Encoding.UTF8.GetBytes(message);
        string base64String = Convert.ToBase64String(utf8Bytes);

        var queueRetryInterval = _appSettings.QueueRetryInterval;
        var queueMaxRetryInterval = _appSettings.QueueMaxRetryInterval;

        int baseDelay = int.Parse(queueRetryInterval ?? "0");
        int retryDelay = baseDelay * (int)Math.Pow(2, dequeueCount - 1);
        int maxDelayMinutes = int.Parse(queueMaxRetryInterval ?? "0");
        retryDelay = Math.Min(retryDelay, maxDelayMinutes);

        var updateResponse = _queueClient.UpdateMessage(messageId, popReceipt, base64String, TimeSpan.FromMinutes(retryDelay));

        _logger.LogCustomInformation(_appSettings.AppName,
            $"Retrying message {messageId} after {retryDelay} minutes. Dequeue Count: {dequeueCount}");
    }
}
