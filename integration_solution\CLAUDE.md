# Claude Instructions

## Overview
Create code for various transactional events
"C:\Repos\ERP-Integration\integration_solution\retail-integration-manage" for the project code.

## Relevant files
Please take a look at the files below.  They will be relevant.  Include any other files you think would be relevant.
- C:\Repos\ERP-Integration\integration_solution\retail-integration-manage\Manage\Models\PurchaseReceipts\OrderLineItemAcceptedEventEntity.cs
- C:\Repos\ERP-Integration\integration_solution\retail-integration-manage\Manage\Models\PurchaseOrders\PurchaseOrderEventHandler.cs
- C:\Repos\ERP-Integration\integration_solution\retail-integration-manage\Manage\Models\PurchaseOrders\IPurchaseOrderEventHandler.cs
- C:\Repos\ERP-Integration\integration_solution\retail-integration-manage\Manage\Models\PurchaseOrders\PurchaseOrderMapping.cs

## Tasks for Claude
The files above are reference files.  What we'll be working on will be purchase receipts, so file in the correct subfolder.

We need to create PurchaseReceiveEventHandler.cs, similar to PurchaseOrderEventHandler.  You'll also need to include an interface and a mapping class.  It's a bit complicated, so think about it, then give it good shot and I'll review.