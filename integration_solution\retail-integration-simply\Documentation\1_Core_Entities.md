# Core Entities Diagram

This diagram shows the core entities and their relationships in the integration database.

```mermaid
erDiagram
    ExternalSystem ||--o{ ExternalSystemType : "has type"
    ExternalSystem ||--o{ ExternalSystemPreferences : "has preferences"
    ExternalSystem ||--o{ Company : "manages"
    
    Company ||--o{ Clinic : "owns"
    Clinic ||--o{ SalesOrder : "processes"
    Clinic ||--o{ SalesInvoice : "processes"
    Clinic ||--o{ PurchaseOrder : "places"
    Clinic ||--o{ Patient : "serves"
    
    Patient ||--o{ SalesOrder : "places"
    Patient ||--o{ SalesInvoice : "billed on"
    Patient ||--o{ Payment : "makes payment"
    
    Payor ||--o{ Payment : "manages payment"
    
    ExternalSystem {
        UUID Id PK
        string Code
        string Name
        bool IsActive
        UUID ExternalSystemTypeId FK
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Company {
        UUID Id PK
        string Code
        string Name
        string Address
        string Address2
        string City
        string Region
        string Country
        string PostalCode
        bool IsActive
        UUID ExternalSystemId FK
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Clinic {
        UUID Id PK
        string Code
        string Name
        string Address
        string Address2
        string City
        string Region
        string Country
        string PostalCode
        string Phone
        bool IsActive
        UUID CompanyId FK
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Patient {
        UUID Id PK
        string Code
        string AlternateCode
        string Name
        string Address
        string Address2
        string City
        string Region
        string Country
        string PostalCode
        string Phone
        string Email
        string IdentificationNumber
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
    
    Payor {
        UUID Id PK
        string Code
        string Name
        string Phone
        bool IsActive
        datetime CreatedOn
        datetime ModifiedOn
    }
```