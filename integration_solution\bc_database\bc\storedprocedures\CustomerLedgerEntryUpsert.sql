﻿CREATE PROCEDURE [bc].[CustomerLedgerEntryUpsert]
(
    @loadId NVARCHAR(40)
)
WITH EXECUTE AS OWNER
AS
BEGIN  
    BEGIN TRY
    SET XACT_ABORT ON
    BEGIN TRANSACTION

    UPDATE dest
     
       SET CompanyId = IIF(dest.ExternalCompanyId IS DISTINCT FROM source.CompanyId, NULL, dest.CompanyId),
           EntryNumber = source.EntryNo,
           CustomerId = IIF(dest.ExternalCustomerId IS DISTINCT FROM source.CustomerId, NULL, dest.CustomerId),
           ExternalCustomerId = source.CustomerId,
           CustomerPostingGroupId = IIF(dest.CustomerPostingGroupId IS DISTINCT FROM source.CustomerPostingGroupId, NULL, dest.CustomerPostingGroupId),
           ExternalCustomerPostingGroupId = source.CustomerPostingGroupId,
           PostingDate = source.PostingDate,
           DocumentDate = source.DocumentDate,
           DueDate = source.DueDate,
           DocumentType = source.DocumentType,
           DocumentNumber = source.DocumentNo,
           OrderReferenceNumber = source.ExternalDocumentNo,
           Description = source.Description,
           [Open] = source.[Open],
           Amount = source.Amount,
           DimensionSetId = IIF(dest.ExternalDimensionSetNumber IS DISTINCT FROM source.DimensionSetId, NULL, dest.DimensionSetId),
           ExternalDimensionSetNumber = source.DimensionSetId,
           ExternalCreatedOn = source.SystemCreatedAt,
           ExternalModifiedOn = source.SystemModifiedAt,
           ModifiedOn = SYSUTCDATETIME()

      FROM bc.CustomerLedgerEntry AS dest

     INNER JOIN bc.CustomerLedgerEntry_Staging AS source
        ON dest.ExternalCompanyId = source.CompanyId
       AND dest.ExternalId = source.Id
       AND source.ETL_LoadId = @loadId

     WHERE dest.ExternalModifiedOn < source.SystemModifiedAt
        OR dest.ExternalModifiedOn IS NULL;

    INSERT INTO bc.CustomerLedgerEntry (
           ExternalCompanyId,
           ExternalId,
           EntryNumber,
           ExternalCustomerId,
           ExternalCustomerPostingGroupId,
           PostingDate,
           DocumentDate,
           DueDate,
           DocumentType,
           DocumentNumber,
           OrderReferenceNumber,
           Description,
           [Open],
           Amount,
           ExternalDimensionSetNumber,
           ExternalCreatedOn,
           ExternalModifiedOn)

    SELECT source.CompanyId AS ExternalCompanyId,
           source.Id AS ExternalId,
           source.EntryNo AS EntryNumber,
           source.CustomerId AS ExternalCustomerId,
           source.CustomerPostingGroupId AS ExternalCustomerPostingGroupId,
           source.PostingDate,
           source.DocumentDate,
           source.DueDate,
           source.DocumentType,
           source.DocumentNo AS DocumentNumber,
           source.ExternalDocumentNo AS OrderReferenceNumber,
           source.Description,
           source.[Open],
           source.Amount,
           source.DimensionSetId AS ExternalDimensionSetNumber,
           source.SystemCreatedAt AS ExternalCreatedOn,
           source.SystemModifiedAt AS ExternalModifiedOn

      FROM bc.CustomerLedgerEntry_Staging AS source

      WHERE source.ETL_LoadId = @loadId
        AND NOT EXISTS (
            SELECT 1
            FROM bc.CustomerLedgerEntry AS dest
            WHERE dest.ExternalCompanyId = source.CompanyId
              AND dest.ExternalId = source.Id
    )
           
    TRUNCATE TABLE bc.CustomerLedgerEntry_Staging;
    COMMIT TRANSACTION;
                
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END