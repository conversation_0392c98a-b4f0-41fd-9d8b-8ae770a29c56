﻿using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Core;

public interface IDocumentServiceBase<TModel, TLineModel>
    where TModel : class, IDocument<TLineModel>
    where TLineModel : class, IDocumentLine
{
    public Task<TModel?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null);

    public Task<List<TModel>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null);

    public Task<ExternalDocumentReference?> GetExternalDocumentReferenceAsync(
        string externalSystemCode,
        string externalReference);

    public Task<TModel?> UpsertAsync(TModel domainModel);

    public bool ValidateRequiredProperties(TModel domainModel)
    {
        if (string.IsNullOrWhiteSpace(domainModel.ExternalSystemCode)) return false;
        if (string.IsNullOrWhiteSpace(domainModel.DocumentNumber)) return false;
        if (domainModel.DocumentDate == null) return false;
        if (domainModel.Clinic?.Code == null) return false;
        return true;
    }
}
