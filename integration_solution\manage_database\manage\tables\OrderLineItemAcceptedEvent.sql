﻿CREATE TABLE [manage].[OrderLineItemAcceptedEvent]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_OrderLineItemAcceptedEvent_Id] DEFAULT NEWID() NOT NULL,
    [EventType]         NVARCHAR(100),
    [MessageId]         UNIQUEIDENTIFIER,
    [OrderId]           UNIQUEIDENTIFIER,
    [LineItemId]        UNIQUEIDENTIFIER,
    [ExternalNumber]    NVARCHAR(50),
    [Timestamp]         DATETIME2,
    [LocalTimestamp]    DATETIME2,
    [BlobPath]          NVARCHAR(255),
    [Status]            INT CONSTRAINT [DF_OrderLineItemAcceptedEvent_Status] DEFAULT 0 NOT NULL,
    [ProcessedOn]       DATETIME2,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_OrderLineItemAcceptedEvent_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_OrderLineItemAcceptedEvent_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_OrderLineItemAcceptedEvent_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_OrderLineItemAcceptedEvent_MessageId
ON [manage].[OrderLineItemAcceptedEvent] ([MessageId])
GO

CREATE INDEX IX_OrderLineItemAcceptedEvent_Status
ON [manage].[OrderLineItemAcceptedEvent] ([Status], [MessageId])
GO

CREATE INDEX IX_OrderLineItemAcceptedEvent_OrderId
ON [manage].[OrderLineItemAcceptedEvent] ([OrderId])
GO

CREATE INDEX IX_OrderLineItemAcceptedEvent_LineItemId
ON [manage].[OrderLineItemAcceptedEvent] ([LineItemId])
GO