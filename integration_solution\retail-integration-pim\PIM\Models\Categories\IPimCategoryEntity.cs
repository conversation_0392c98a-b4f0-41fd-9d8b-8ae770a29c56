﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Categories;

public interface IPimCategoryEntity : IIdentifiable, INameable, IAuditInfo
{
    public Guid? ParentCategoryId { get; set; }

    public abstract PimCategoryEntity? ParentCategory { get; set; }
    public abstract ICollection<PimProductEntity> Products { get; set; }
}
