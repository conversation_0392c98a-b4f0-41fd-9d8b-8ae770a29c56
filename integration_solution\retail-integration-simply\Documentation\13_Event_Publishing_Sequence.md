# Event Publishing Sequence Diagram

This diagram illustrates the sequence of operations for publishing events in the WSA Retail Integration Model project.

```mermaid
sequenceDiagram
    participant Enti<PERSON><PERSON>ervice as Entity Service
    participant EventGridPublisher as Event Grid Publisher
    participant EventGridEvent as Event Grid Event Factory
    participant HttpClient as HTTP Client
    participant QueueClient as Queue Client
    participant ExternalSystem as External Event Grid / Queue
    
    %% Normal Event Publishing
    EntityService->>EventGridPublisher: RaiseEventAsync(settings, eventType, subject, data)
    EventGridPublisher->>EventGridEvent: Create(settings, eventType, subject, data)
    EventGridEvent-->>EventGridPublisher: Return Event
    
    EventGridPublisher->>EventGridPublisher: Serialize Event to JSON
    EventGridPublisher->>HttpClient: PostAsync(endpoint, serializedEvent)
    HttpClient->>ExternalSystem: HTTP POST Request
    ExternalSystem-->>HttpClient: HTTP Response
    HttpClient-->>EventGridPublisher: Return HTTP Response
    EventGridPublisher->>EventGridPublisher: EnsureSuccessStatusCode()
    EventGridPublisher-->>EntityService: Return Task
    
    %% Mock Event Publishing (for testing/development)
    EntityService->>EventGridPublisher: RaiseMockEventAsync(eventType, subject, data)
    EventGridPublisher->>EventGridEvent: Create(settings, eventType, subject, data)
    EventGridEvent-->>EventGridPublisher: Return Event
    EventGridPublisher->>EventGridPublisher: Set Source to "N/A"
    
    EventGridPublisher->>EventGridPublisher: Serialize Event to JSON
    EventGridPublisher->>QueueClient: CreateIfNotExists()
    QueueClient-->>EventGridPublisher: Return Result
    
    EventGridPublisher->>EventGridPublisher: Encode to Base64
    EventGridPublisher->>QueueClient: SendMessageAsync(base64String)
    QueueClient->>ExternalSystem: Queue Message
    ExternalSystem-->>QueueClient: Return Result
    QueueClient-->>EventGridPublisher: Return Task
    EventGridPublisher-->>EntityService: Return Task
```

This sequence diagram shows the flow of operations for publishing events:

1. **Normal Event Publishing (RaiseEventAsync)**:
   - The Entity Service calls RaiseEventAsync on the Event Grid Publisher
   - An Event object is created with the provided details
   - The Event is serialized to JSON
   - The serialized Event is sent to the configured Event Grid endpoint via HTTP POST
   - The HTTP response is validated to ensure success

2. **Mock Event Publishing (RaiseMockEventAsync)**:
   - Used for testing or development environments
   - Similar to normal publishing, but the event is sent to a Storage Queue instead of Event Grid
   - The Event is serialized, encoded to Base64, and sent as a queue message

Both methods allow the system to communicate changes to external systems or other parts of the application in an event-driven architecture.