﻿CREATE TABLE [dbo].[PurchaseOrder]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseOrder_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NULL,
    [AlternateNumber]   NVARCHAR(50) NULL,
    [VendorId]          UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NULL,
    [DocumentDate]      DATE NULL,
    [IsActive]          BIT CONSTRAINT [DF_PurchaseOrder_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_PurchaseOrder_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_PurchaseOrder_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT [PK_PurchaseOrder_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_PurchaseOrder_Vendor] FOREIGN KEY (VendorId) REFERENCES [dbo].[Vendor](Id),
    CONSTRAINT [FK_PurchaseOrder_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseOrder_DocumentNumber
ON [dbo].[PurchaseOrder] ([DocumentNumber])
GO