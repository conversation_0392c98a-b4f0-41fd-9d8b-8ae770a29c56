﻿using WSA.Retail.Integration.Models.Claims;

namespace WSA.Retail.Integration.Models.SalesInvoices
{
    public class SalesInvoiceEntity : ISalesInvoiceEntity
    {
        public Guid Id { get; set; }
        public string? DocumentNumber { get; set; }
        public string? AlternateNumber { get; set; }
        public Guid? SalesOrderId { get; set; }
        public Guid? PatientId { get; set; }
        public Guid? ClinicId { get; set; }
        public DateTime? DocumentDate { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }

        public Patients.PatientEntity? PatientEntity { get; set; }
        public Clinics.ClinicEntity? ClinicEntity { get; set; }
        public ICollection<SalesInvoiceLineEntity> Lines { get; set; } = [];
        public ICollection<ClaimEntity> Claims { get; set; } = [];
    }
}