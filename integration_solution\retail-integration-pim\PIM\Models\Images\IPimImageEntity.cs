﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Images;

public interface IPimImageEntity : IIdentifiable, IAuditInfo
{
    public Guid ProductId { get; set; }
    public Guid? BrandId { get; set; }
    public string? CDNUrl { get; set; }
    public bool IsDefault { get; set; }

    public PimProductEntity? ProductEntity { get; set; }
}