{"version": "0.2.0", "configurations": [{"name": "DK DEV", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "dk_dev", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "W1 Sandbox", "request": "launch", "type": "al", "environmentType": "OnPrem", "server": "http://w1sandbox", "serverInstance": "BC", "authentication": "UserPassword", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "tenant": "default", "usePublicURLFromServer": true}]}