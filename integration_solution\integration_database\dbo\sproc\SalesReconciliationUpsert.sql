﻿CREATE PROCEDURE [dbo].[SalesReconciliationUpsert]
       @data [staging].[SalesReconciliation] READONLY,
       @watermarkCode NVARCHAR(20) = NULL

AS
BEGIN

SET NOCOUNT ON;
SET XACT_ABORT ON;
BEGIN TRANSACTION;

 WITH Source AS (
      SELECT t3.Id AS ExternalSystemId,
             t2.DocumentNumber,
             t2.AlternateNumber,
             t2.DocumentDate,
             t2.ClinicCode,
             t2.PatientCode,
             t2.AmountExclTax,
             t2.TaxAmount,
             t2.AmountInclTax
             
        FROM (
             SELECT t1.ExternalSystemCode,
                    t1.DocumentNumber,
                    t1.AlternateNumber,
                    t1.DocumentDate,
                    t1.ClinicCode,
                    t1.PatientCode,
                    SUM(t1.AmountExclTax) OVER(PARTITION BY t1.ExternalSystemCode, t1.DocumentNumber, t1.DocumentDate ORDER BY t1.ModifiedOn DESC) AS AmountExclTax,
                    SUM(t1.TaxAmount) OVER(PARTITION BY t1.ExternalSystemCode, t1.DocumentNumber, t1.DocumentDate ORDER BY t1.ModifiedOn DESC) AS TaxAmount,
                    SUM(t1.AmountInclTax) OVER(PARTITION BY t1.ExternalSystemCode, t1.DocumentNumber, t1.DocumentDate ORDER BY t1.ModifiedOn DESC) AS AmountInclTax,
                    ROW_NUMBER() OVER(PARTITION BY t1.ExternalSystemCode, t1.DocumentNumber, t1.DocumentDate ORDER BY t1.ModifiedOn DESC) AS RowNum

              FROM @data AS t1) AS t2

       OUTER APPLY (SELECT TOP 1 Id FROM dbo.ExternalSystem WHERE ExternalSystem.Code = t2.ExternalSystemCode) AS t3

       WHERE t2.RowNum = 1)

 MERGE dbo.SalesReconciliation AS Target
 USING Source
    ON Source.ExternalSystemId = Target.ExternalSystemId
   AND Source.DocumentNumber = Target.DocumentNumber

  WHEN NOT MATCHED BY Target THEN
INSERT (
       ExternalSystemId,
       DocumentNumber,
       AlternateNumber,
       DocumentDate,
       ClinicCode,
       PatientCode,
       AmountExclTax,
       TaxAmount,
       AmountInclTax)

VALUES (
       Source.ExternalSystemId,
       Source.DocumentNumber,
       Source.AlternateNumber,
       Source.DocumentDate,
       Source.ClinicCode,
       Source.PatientCode,
       Source.AmountExclTax,
       Source.TaxAmount,
       Source.AmountInclTax)

  WHEN MATCHED AND (
               (Target.AlternateNumber <> Source.AlternateNumber) OR (Target.AlternateNumber IS NULL AND Source.AlternateNumber IS NOT NULL)
            OR (Target.ClinicCode <> Source.ClinicCode) OR (Target.ClinicCode IS NULL AND Source.ClinicCode IS NOT NULL)
            OR (Target.PatientCode <> Source.PatientCode) OR (Target.PatientCode IS NULL AND Source.PatientCode IS NOT NULL)
            OR (Target.AmountExclTax <> Source.AmountExclTax) OR (Target.AmountExclTax IS NULL AND Source.AmountExclTax IS NOT NULL)
            OR (Target.TaxAmount <> Source.TaxAmount) OR (Target.TaxAmount IS NULL AND Source.TaxAmount IS NOT NULL)
            OR (Target.AmountInclTax <> Source.AmountInclTax) OR (Target.AmountInclTax IS NULL AND Source.AmountInclTax IS NOT NULL) )

  THEN UPDATE
   SET Target.AlternateNumber = Source.AlternateNumber,
       Target.ClinicCode = Source.ClinicCode,
       Target.PatientCode = Source.PatientCode,
       Target.AmountExclTax = Source.AmountExclTax,
       Target.TaxAmount = Source.TaxAmount,
       Target.AmountInclTax = Source.AmountInclTax
;

IF (@watermarkCode IS NOT NULL)
BEGIN

 MERGE dbo.Watermark AS Target
 USING (SELECT @watermarkCode AS Code) AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target THEN
INSERT (Code, LastModifiedOn) VALUES (Source.Code, (SELECT MAX(ModifiedOn) FROM @data d1) )

  WHEN MATCHED THEN
UPDATE
   SET Target.LastModifiedOn = (SELECT MAX(ModifiedOn) FROM @data d1)
;

END

COMMIT TRANSACTION;
END