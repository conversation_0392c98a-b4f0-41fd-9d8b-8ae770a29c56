using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Models.Products;
using WSA.Retail.Integration.PIM.Queries;
using WSA.Retail.Integration.PIM.Models.Countries;

namespace WSA.Retail.Integration.PIM.EventProcessing
{
    public class TimerEventProcessor(
        ILogger<TimerEventProcessor> logger, 
        IPimCountryRepository pimCountryRepository,
        ICategoryQueryService categoryQueryService,
        IProductQueryService productQueryService,
        IBaseProductService baseProductService)
    {
        private readonly ILogger<TimerEventProcessor> _logger = logger;
        private readonly IPimCountryRepository _pimCountryRepository = pimCountryRepository;
        private readonly ICategoryQueryService _categoryQueryService = categoryQueryService;
        private readonly IProductQueryService _productQueryService = productQueryService;
        private readonly IBaseProductService _baseProductService = baseProductService;


        [Function("GetAllCategoriesFromPIM")]
        public async Task GetCategories([TimerTrigger("%Schedule1%", RunOnStartup = false)] TimerInfo myTimer)
        {
            _logger.LogMethodStart();
            var countryList = await _pimCountryRepository.GetListAsync();
            if (countryList != null)
            {
                foreach (var country in countryList)
                {
                    if (country.Brands != null)
                    {
                        foreach (var brand in country.Brands)
                        {
                            await _categoryQueryService.UpdateCategoriesFromPIMAsync(country.Code, brand.Code);
                            _logger.LogCustomInformation($"Processed categores for Brand: {brand.Name}");
                        }
                    }
                }
            }

            if (myTimer.ScheduleStatus is not null)
            {
                _logger.LogCustomInformation($"Next timer schedule at: {myTimer.ScheduleStatus.Next}");
            }
        }


        [Function("GetAllProductsFromPIM")]
        public async Task GetProducts(
            [TimerTrigger("%Schedule2%", RunOnStartup = false)] TimerInfo myTimer)

        {
            _logger.LogMethodStart();
            var countryList = await _pimCountryRepository.GetListAsync();
            if (countryList != null)
            {
                foreach (var country in countryList)
                {
                    if (country.Brands != null)
                    {
                        foreach (var brand in country.Brands)
                        {
                            await _productQueryService.UpdateProductsFromPIMAsync(country.Code, brand.Code);
                        }
                    }
                }
            }

            if (myTimer.ScheduleStatus is not null)
            {
                _logger.LogCustomInformation($"Next timer schedule at: {myTimer.ScheduleStatus.Next}");
            }
        }


        [Function("UpdateBaseProducts")]
        public async Task UpdateProducts(
            [TimerTrigger("%Schedule3%", RunOnStartup = false)] TimerInfo myTimer)

        {
            _logger.LogMethodStart();
            await _baseProductService.ProcessNewRecords();

            if (myTimer.ScheduleStatus is not null)
            {
                _logger.LogCustomInformation($"Next timer schedule at: {myTimer.ScheduleStatus.Next}");
            }
        }
    }
}
