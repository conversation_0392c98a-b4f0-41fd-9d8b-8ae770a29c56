﻿CREATE TABLE [dbo].[SalesInvoice]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_SalesInvoice_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NOT NULL,
    [AlternateNumber]   NVARCHAR(50) NULL,
    [SalesOrderId]      UNIQUEIDENTIFIER NULL,
    [PatientId]         UNIQUEIDENTIFIER NOT NULL,
    [ClinicId]          UNIQUEIDENTIFIER NOT NULL,
    [DocumentDate]      DATE NULL,
    [IsActive]          BIT CONSTRAINT [DF_SalesInvoice_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_SalesInvoice_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_SalesInvoice_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT          [PK_SalesInvoice_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_SalesInvoice_SalesOrder] FOREIGN KEY (SalesOrderId) REFERENCES [dbo].[SalesOrder](Id),
    CONSTRAINT          [FK_SalesInvoice_Patient] FOREIGN KEY (PatientId) REFERENCES [dbo].[Patient](Id),
    CONSTRAINT          [FK_SalesInvoice_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesInvoice_Code
ON [dbo].[SalesInvoice] ([DocumentNumber])
GO

CREATE INDEX IX_SalesInvoice_SalesOrder
ON [dbo].[SalesInvoice] ([SalesOrderId])
GO