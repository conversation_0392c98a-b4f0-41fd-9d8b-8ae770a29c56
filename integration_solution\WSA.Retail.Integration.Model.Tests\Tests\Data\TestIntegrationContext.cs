﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Models.Batteries;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Tests.Data;

public class TestIntegrationContext(DbContextOptions<IntegrationContext> options) : IntegrationContext(options)
{
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<EntityEntity>().HasData(
            [.. TestDataLoader.Entities()
                .Select(e => new EntityEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<ExternalSystemEntity>().HasData(
            [.. TestDataLoader.ExternalSystems()
                .Select(e => new ExternalSystemEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<EntitySubscriberEntity>().HasData(
            [.. TestDataLoader.EntitySubscribers()
                .Select(e => new EntitySubscriberEntity
                {
                    Id = e.Id,
                    EntityId = e.EntityId,
                    ExternalSystemId = e.ExternalSystemId,
                    FromExternalSystem = e.FromExternalSystem,
                    ToExternalSystem = e.ToExternalSystem,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<ClinicEntity>().HasData(
            [.. TestDataLoader.Clinics()
                .Select(e => new ClinicEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    Address = e.Address,
                    Address2 = e.Address2,
                    City = e.City,
                    Region = e.Region,
                    PostalCode = e.PostalCode,
                    Country = e.Country,
                    Phone = e.Phone,
                    Email = e.Email,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.ClinicCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<PatientEntity>().HasData(
            [.. TestDataLoader.Patients()
                .Select(e => new PatientEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    Address = e.Address,
                    Address2 = e.Address2,
                    City = e.City,
                    Region = e.Region,
                    PostalCode = e.PostalCode,
                    Country = e.Country,
                    Phone = e.Phone,
                    Email = e.Email,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.PatientCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<VendorEntity>().HasData(
            [.. TestDataLoader.Vendors()
                .Select(e => new VendorEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    Address = e.Address,
                    Address2 = e.Address2,
                    City = e.City,
                    Region = e.Region,
                    PostalCode = e.PostalCode,
                    Country = e.Country,
                    Phone = e.Phone,
                    Email = e.Email,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.VendorCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<ManufacturerEntity>().HasData(
            [.. TestDataLoader.Manufacturers()
                .Select(e => new ManufacturerEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.ManufacturerCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CategoryEntity>().HasData(
            [.. TestDataLoader.Categories()
                .Select(e => new CategoryEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    IsHearingAid = e.IsHearingAid,
                    IsInventory = e.IsInventory,
                    IsSerialized = e.IsSerialized,
                    ParentId = e.ParentId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.CategoryCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<BatteryEntity>().HasData(
            [.. TestDataLoader.Batteries()
                .Select(e => new BatteryEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.BatteryCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<ColorEntity>().HasData(
            [.. TestDataLoader.Colors()
                .Select(e => new ColorEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.ColorCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<ProductEntity>().HasData(
            [.. TestDataLoader.Products()
                .Select(e => new ProductEntity
                {
                    Id = e.Id,
                    Code = e.Code,
                    Name = e.Name,
                    AlternateCode = e.AlternateCode,
                    PimProductId = e.PimProductId,
                    CategoryId = e.CategoryId,
                    VendorId = e.VendorId,
                    ManufacturerId = e.ManufacturerId,
                    ColorId = e.ColorId,
                    VendorItemNo = e.VendorItemNo,
                    GTIN = e.GTIN,
                    ImageUrl = e.ImageUrl,
                    IsSerialized = e.IsSerialized,
                    IsInventory = e.IsInventory,
                    IsTaxable = e.IsTaxable,
                    UnitCost = e.UnitCost,
                    ProductModelId = e.ProductModelId,
                    BatteryId = e.BatteryId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

        modelBuilder.Entity<CouplingEntity>().HasData(
            [.. TestDataLoader.ProductCouplings()
                .Select(e => new CouplingEntity
                {
                    Id = e.Id,
                    ExternalSystemId = e.ExternalSystemId,
                    EntityId = e.EntityId,
                    RecordId = e.RecordId,
                    ExternalRecordId = e.ExternalRecordId,
                    CreatedOn = DateTime.UtcNow,
                    ModifiedOn = DateTime.UtcNow
                })]
        );

    }
}
