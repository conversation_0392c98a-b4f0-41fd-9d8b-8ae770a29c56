﻿using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Core;

public abstract class DocumentServiceBase<TModel, TLineModel, TEntity, TLineEntity>(
    AppSettings appSettings,
    ILogger logger,
    IDocumentRepositoryBase<TModel, TLineModel, TEntity, TLineEntity> repository,
    IDocumentMapper<TModel, TLineModel, TEntity, TLineEntity> mapper,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventGridPublisher eventGridPublisher,
    EntityType entityType)
    : LoggingBase<TEntity>(logger),
      IDocumentServiceBase<TModel, TLineModel>
    where TModel : class, IDocument<TLineModel>
    where TLineModel : class, IDocumentLine
    where TEntity : class, IDocumentEntity<TLineEntity>
    where TLineEntity : class, IDocumentLineEntity
{
    protected readonly AppSettings _appSettings = appSettings;
    protected readonly IDocumentRepositoryBase<TModel, TLineModel, TEntity, TLineEntity> _repository = repository;
    protected readonly IDocumentMapper<TModel, TLineModel, TEntity, TLineEntity> _mapper = mapper;
    protected readonly IEntitySubscriberService _entitySubscriberService = entitySubscriberService;
    protected readonly ICouplingService _couplingService = couplingService;
    protected readonly IEventGridPublisher _eventGridPublisher = eventGridPublisher;
    protected readonly EntityType _entityType = entityType;

    public async Task<TModel?> GetAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null)
    {
        LogMethodStart();
        var domainModel = await _repository.GetAsync(
            externalSystemCode: externalSystemCode,
            id: id,
            documentNumber: documentNumber,
            externalReference: externalReference);
        return domainModel;
    }

    public async Task<List<TModel>> GetListAsync(
        string externalSystemCode,
        Guid? id = null,
        string? documentNumber = null,
        string? externalReference = null,
        DateTime? documentDate = null)
    {
        LogMethodStart();
        var domainModel = await _repository.GetListAsync(
            externalSystemCode: externalSystemCode,
            id: id,
            documentNumber: documentNumber,
            externalReference: externalReference,
            documentDate: documentDate);
        return domainModel;
    }

    public async Task<ExternalDocumentReference?> GetExternalDocumentReferenceAsync(
        string externalSystemCode,
        string externalReference)
    {
        LogMethodStart();

        var domainModel = await _repository.GetAsync(
            externalSystemCode: externalSystemCode,
            externalReference: externalReference);

        if (domainModel == null)
            return null;

        var externalDocumentReference = _mapper.ToExternalDocumentReference(domainModel);
        return externalDocumentReference;
    }

    public async Task<TModel?> UpsertAsync(TModel domainModel)
    {
        LogMethodStart();

        var errors = domainModel.ValidateRequiredProperties();
        if (errors.Any())
        {
            foreach (var error in errors)
            {
                LogCustomError(error);
            }
            LogCustomError($"Required properties are missing for {typeof(TModel).Name}.");
            return null;
        }

        // Fetch Entity Subscriber
        var subscriber = await _entitySubscriberService.GetAsync(
            domainModel.ExternalSystemCode!,
            _entityType.GetEntityCode());

        ArgumentNullException.ThrowIfNull(subscriber);
        if (subscriber.FromExternalSystem ?? false)
        {
            // Retrieve existing SalesInvoice from repository
            TModel? oldDomainEntity = null;
            if (!string.IsNullOrWhiteSpace(domainModel.DocumentNumber))
            {
                oldDomainEntity = await _repository.GetAsync(
                    _appSettings.ExternalSystemCode,
                    documentNumber: domainModel.DocumentNumber);
            }
            else if (!string.IsNullOrWhiteSpace(domainModel.ExternalReference))
            {
                oldDomainEntity = await _repository.GetAsync(
                    _appSettings.ExternalSystemCode,
                    externalReference: domainModel.ExternalReference);
            }

            if (oldDomainEntity == null)
            {
                LogCustomInformation(
                    $"{typeof(TEntity).Name} {domainModel.DocumentNumber} does not exist in the database and must be inserted.");
                var isSuccess = await _repository.InsertAsync(domainModel);
                if (isSuccess)
                {
                    var hookErrors = await OnAfterInsertDomainModel(domainModel);
                    if (hookErrors != null && hookErrors.Any())
                    {
                        foreach (var error in hookErrors)
                        {
                            LogCustomError(error);
                        }
                        LogCustomError($"OnAfterInsertDomainModel hook failed for {typeof(TEntity).Name}.");
                        return null;
                    }

                    LogCustomInformation(
                        $"{typeof(TEntity).Name} {domainModel.DocumentNumber} was successfully inserted in the database.");
                    await RaiseEventAsync(domainModel);
                }
                else
                {
                    LogCustomWarning(
                        $"{typeof(TEntity).Name} {domainModel.DocumentNumber} failed to insert.");
                }
            }
            else
            {
                domainModel.Id = oldDomainEntity.Id;
                if (domainModel.HasChanges(oldDomainEntity))
                {
                    // Update existing SalesInvoice
                    LogCustomInformation(
                        $"{typeof(TEntity).Name} {domainModel.DocumentNumber} has changed from database and must be updated.");
                    var isSuccess = await _repository.UpdateAsync(domainModel);
                    if (isSuccess)
                    {
                        var hookErrors = await OnAfterUpdateDomainModel(domainModel);
                        if (hookErrors != null && hookErrors.Any())
                        {
                            foreach (var error in hookErrors)
                            {
                                LogCustomError(error);
                            }
                            LogCustomError($"OnAfterUpdateDomainModel hook failed for {typeof(TEntity).Name}.");
                            return null;
                        }

                        LogCustomInformation(
                            $"{typeof(TEntity).Name} {domainModel.DocumentNumber} was successfully updated in the database.");
                        await RaiseEventAsync(domainModel);
                    }
                    else
                    {
                        LogCustomWarning(
                            $"{typeof(TEntity).Name} {domainModel.DocumentNumber} failed to update.");
                    }
                }
                else
                {
                    LogCustomInformation(
                        $"{typeof(TEntity).Name} {domainModel.DocumentNumber} has not changed.  Do nothing.");
                }
            }
        }

        // Upsert coupling record
        if (domainModel.Id != Guid.Empty)
        {
            var coupling = new Coupling
            {
                ExternalSystem = new() { Code = _appSettings.ExternalSystemCode },
                Entity = new() { Code = _entityType.GetEntityCode() },
                RecordId = domainModel.Id,
                ExternalCode = domainModel.ExternalReference
            };
            await _couplingService.UpsertAsync(coupling);
        }
        return domainModel;
    }

    public async Task RaiseEventAsync(TModel domainModel)
    {
        ArgumentNullException.ThrowIfNull(domainModel.ExternalSystemCode);
        ArgumentNullException.ThrowIfNull(domainModel.Id);
        await _eventGridPublisher.RaiseEventAsync(
            _appSettings,
            _entityType.GetEventName(),
            domainModel.Id.ToString() ?? "",
            domainModel);
    }

    public async Task RaiseMockEventAsync(TModel domainModel)
    {
        ArgumentNullException.ThrowIfNull(domainModel.ExternalSystemCode);
        ArgumentNullException.ThrowIfNull(domainModel.Id);
        await _eventGridPublisher.RaiseMockEventAsync(
            _entityType.GetEventName(),
            domainModel.Id.ToString() ?? "",
            domainModel);
    }

    public virtual async Task<IEnumerable<string>?> OnAfterInsertDomainModel(TModel domainModel)
    {
        await Task.Yield();
        return default;
    }

    public virtual async Task<IEnumerable<string>?> OnAfterUpdateDomainModel(TModel domainModel)
    {
        await Task.Yield();
        return default;
    }
}