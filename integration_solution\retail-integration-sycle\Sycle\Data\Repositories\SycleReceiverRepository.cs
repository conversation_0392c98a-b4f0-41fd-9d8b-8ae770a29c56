using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Sycle.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Sycle.Models.Receivers;

namespace WSA.Retail.Integration.Sycle.Data.Repositories;

public class SycleReceiverRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SycleReceiverRepository> logger,
    IDbContextFactory<SycleContext> dbContextFactory) : ISycleReceiverRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SycleReceiverRepository> _logger = logger;
    private readonly IDbContextFactory<SycleContext> _dbContextFactory = dbContextFactory;
       

    public async Task<List<SycleReceiver>> GetIntegrationRecordsAsync()
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetIntegrationRecordsAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entities = await context.SycleReceiverEntity
            .Where(r => r.IntegrationRequired)
            .ToListAsync();

        var receivers = entities.Select(e => new SycleReceiver
        {
            ReceiverId = e.ReceiverId,
            ReceiverUuid = e.ReceiverUuid,
            Manufacturer = e.Manufacturer,
            BasePrice = e.BasePrice,
            ActualCost = e.ActualCost,
            ClinicId = e.ClinicId,
            WarrantyLength = e.WarrantyLength,
            Type = e.Type,
            Description = e.Description,
            LastUpdate = e.LastUpdate
        }).ToList();

        return receivers ?? [];
    }

    public async Task<SycleReceiver?> GetAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(GetAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleReceiverEntity
            .FirstOrDefaultAsync(r => r.ReceiverId == id);

        if (entity == null)
        {
            return null;
        }

        var receiver = new SycleReceiver
        {
            ReceiverId = entity.ReceiverId,
            ReceiverUuid = entity.ReceiverUuid,
            Manufacturer = entity.Manufacturer,
            BasePrice = entity.BasePrice,
            ActualCost = entity.ActualCost,
            ClinicId = entity.ClinicId,
            WarrantyLength = entity.WarrantyLength,
            Type = entity.Type,
            Description = entity.Description,
            LastUpdate = entity.LastUpdate
        };

        return receiver;
    }

    public async Task<bool> UpdateIntegrationStatusAsync(int id)
    {
        _logger.LogMethodStart(_appSettings.AppName, nameof(UpdateIntegrationStatusAsync));

        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var entity = await context.SycleReceiverEntity
            .FirstOrDefaultAsync(r => r.ReceiverId == id);

        if (entity == null)
        {
            return false;
        }

        entity.IntegrationRequired = false;
        entity.IntegratedOn = DateTime.UtcNow;
        entity.ModifiedOn = DateTime.UtcNow;

        await context.SaveChangesAsync();

        return true;
    }
}