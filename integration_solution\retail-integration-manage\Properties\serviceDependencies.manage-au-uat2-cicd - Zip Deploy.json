{"dependencies": {"storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/retailintegrationauuat2", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}, "appInsights1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/retail-integration-au-uat2", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}}}