using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Simply.Configuration;
using WSA.Retail.Integration.Simply.Data;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Simply.Models.Payments;

public class SimplyPaymentRepository(
    IOptions<AppSettings> appSettings,
    ILogger<SimplyPaymentRepository> logger,
    SimplyContext simplyContext)
    : ISimplyPaymentRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<SimplyPaymentRepository> _logger = logger;
    private readonly SimplyContext _simplyContext = simplyContext;

    public async Task<List<SimplyPaymentEntity>> GetIntegrationRecordsAsync()

    {
        _logger.LogMethodStart();

        try
        {
            var list = await _simplyContext.SimplyPaymentEntity
                .Where(x => x.IntegrationRequired == true)
                .ToListAsync();

            return list ?? [];
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return [];
    }

    public async Task<bool> UpdateIntegrationStatusAsync(Guid Id)
    {
        _logger.LogMethodStart();

        try
        {
            var entity = await _simplyContext.SimplyPaymentEntity.FindAsync(Id);
            if (entity != null)
            {
                entity.IntegrationRequired = false;
                entity.IntegrationDate = DateTime.UtcNow;
                entity.ModifiedOn = DateTime.UtcNow;
                await _simplyContext.SaveChangesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return false;
    }
}