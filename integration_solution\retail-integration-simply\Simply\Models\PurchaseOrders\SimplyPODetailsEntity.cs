namespace WSA.Retail.Integration.Simply.Models.PurchaseOrders;

public class SimplyPODetailsEntity
{
    public Guid Id { get; set; }
    public Guid? PurchaseOrderId { get; set; }
    public int PONumber { get; set; }
    public required string POType { get; set; }
    public int LineNumber { get; set; }
    public string? ProductCode { get; set; }
    public decimal? Quantity { get; set; }
    public string? UnitOfMeasure { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? LineAmount { get; set; }
    public string? FileName { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }

    // Navigation property for the related header
    public SimplyPOHeaderEntity? Header { get; set; }
}