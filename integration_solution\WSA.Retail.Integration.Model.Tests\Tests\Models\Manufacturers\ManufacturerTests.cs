﻿using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Tests.Utilities;

namespace WSA.Retail.Integration.Tests.Models.Manufacturers;

public class ManufacturerTests
{
    private readonly ServiceFactory _serviceFactory;
    private readonly ManufacturerService _manufacturerService;

    public ManufacturerTests()
    {
        _serviceFactory = new ServiceFactory();
        _manufacturerService = _serviceFactory.CreateManufacturerService();
    }

    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnVendor()
    {
        // Act
        var result = await _manufacturerService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "MFR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8C3F2BA7-305B-4701-A13B-6E4ECB766344"), result.Id);
    }

    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnVendor()
    {
        // Act
        var result = await _manufacturerService.GetAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            externalCode: "EXT-MFR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8C3F2BA7-305B-4701-A13B-6E4ECB766344"), result.Id);
    }

    [Fact]
    public async Task GetExternalReferenceAsync_GetByCodeShouldReturnReference()
    {
        // Act
        var result = await _manufacturerService.GetExternalReferenceAsync(
            externalSystemCode: _serviceFactory.AppSettings.ExternalSystemCode,
            code: "MFR1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Guid.Parse("8C3F2BA7-305B-4701-A13B-6E4ECB766344"), result.Id);
    }
}

