﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Patients;

namespace WSA.Retail.Integration.Manage.Models.Patients;

public class PatientGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IPatientService entityService) 
    : BaseApiGetByQuery<
        Patient,
        IPatientService>(appSettings, entityService)
{
}