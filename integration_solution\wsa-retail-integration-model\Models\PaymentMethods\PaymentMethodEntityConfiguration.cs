using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.PaymentMethods
{
    public class PaymentMethodEntityConfiguration : IEntityTypeConfiguration<PaymentMethodEntity>
    {
        public void Configure(EntityTypeBuilder<PaymentMethodEntity> builder)
        {
            builder.ToTable("PaymentMethod", "dbo");

            builder.<PERSON><PERSON><PERSON>(v => v.Id);

            builder.ConfigureMasterDataFields();
            builder.ConfigureAuditInfoFields();
        }
    }
}