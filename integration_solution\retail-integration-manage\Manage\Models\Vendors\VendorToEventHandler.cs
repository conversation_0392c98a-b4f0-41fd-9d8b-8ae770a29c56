﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Runtime.CompilerServices;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorToEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<VendorToEventHandler> logger,
    VendorManageIntegrator manageIntegrator,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService)
    : GenericToEventHandler<
        Vendor,
        IEntitySubscriberService,
        VendorManageIntegrator,
        AuSupplierResponse>(
            appSettings,
            entitySubscriberService,
            couplingService,
            manageIntegrator,
            EntityType.Vendor)
{
    private readonly ILogger<VendorToEventHandler> _logger = logger;


    protected override void AfterValidateEventEntityProperties(
        Vendor entity,
        out bool ok,
        out bool isHandled)
    {
        ok = true;
        isHandled = false;

        if ((entity.IntegrateWithPOS ?? false) == false)
        {
            ok = true;
            isHandled = true;
            return;
        }
    }

    protected override void LogMethodStart([CallerMemberName] string? callingMethod = null)
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(ex);
    }
}