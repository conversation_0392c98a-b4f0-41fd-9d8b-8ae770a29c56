﻿CREATE   PROCEDURE [cosium].[sp_upsert_agenda]
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Step 1: Create temp table to handle duplicates in staging data
        SELECT 
            id,
            object,
            journeeentiere,
            remarque,
            utilisateur,
            refclient,
            categorie,
            daterdv,
            datefinrdv,
            supprime,
            rdvmanque,
            rdvprive,
            rdvarrive,
            compterendu,
            datemodif,
            creationdate,
            rdvannule,
            creator_id,
            site_id,
            ROW_NUMBER() OVER (PARTITION BY id ORDER BY 
                CASE WHEN datemodif IS NULL THEN '1900-01-01' ELSE datemodif END DESC) AS rn
        INTO #deduped_staging
        FROM [cosium].[stg_agenda];
        
        -- Step 2: MERGE operation (UPSERT)
        MERGE [cosium].[agenda] AS target
        USING (
            SELECT * FROM #deduped_staging WHERE rn = 1
        ) AS source
        ON target.id = source.id
        WHEN MATCHED THEN
            UPDATE SET
                target.object = source.object,
                target.journeeentiere = source.journeeentiere,
                target.remarque = source.remarque,
                target.utilisateur = source.utilisateur,
                target.refclient = source.refclient,
                target.categorie = source.categorie,
                target.daterdv = source.daterdv,
                target.datefinrdv = source.datefinrdv,
                target.supprime = source.supprime,
                target.rdvmanque = source.rdvmanque,
                target.rdvprive = source.rdvprive,
                target.rdvarrive = source.rdvarrive,
                target.compterendu = source.compterendu,
                target.datemodif = source.datemodif,
                target.creationdate = source.creationdate,
                target.rdvannule = source.rdvannule,
                target.creator_id = source.creator_id,
                target.site_id = source.site_id,
                target.ModifiedOn = SYSUTCDATETIME()
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (
                id,
                object,
                journeeentiere,
                remarque,
                utilisateur,
                refclient,
                categorie,
                daterdv,
                datefinrdv,
                supprime,
                rdvmanque,
                rdvprive,
                rdvarrive,
                compterendu,
                datemodif,
                creationdate,
                rdvannule,
                creator_id,
                site_id,
                CreatedOn,
                ModifiedOn
            )
            VALUES (
                source.id,
                source.object,
                source.journeeentiere,
                source.remarque,
                source.utilisateur,
                source.refclient,
                source.categorie,
                source.daterdv,
                source.datefinrdv,
                source.supprime,
                source.rdvmanque,
                source.rdvprive,
                source.rdvarrive,
                source.compterendu,
                source.datemodif,
                source.creationdate,
                source.rdvannule,
                source.creator_id,
                source.site_id,
                SYSUTCDATETIME(), -- CreatedOn
                SYSUTCDATETIME()  -- ModifiedOn
            );
        
        -- Step 3: Log results
        DECLARE @InsertCount INT = @@ROWCOUNT;
        
        -- Optional: Clear staging table after successful upsert
        TRUNCATE TABLE [cosium].[stg_agenda];
        
        COMMIT TRANSACTION;
        
        -- Return success message
        SELECT 
            'Success' AS Status,
            @InsertCount AS RowsAffected,
            GETDATE() AS ProcessedTime;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Return error details
        SELECT 
            'Error' AS Status,
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            ERROR_LINE() AS ErrorLine,
            GETDATE() AS ErrorTime;
    END CATCH
    
    -- Cleanup
    IF OBJECT_ID('tempdb..#deduped_staging') IS NOT NULL
        DROP TABLE #deduped_staging;
END
GO