﻿using WSA.Retail.Integration.PIM.Models.Brands;
using WSA.Retail.Integration.PIM.Models.BundleSets;
using WSA.Retail.Integration.PIM.Models.Categories;
using WSA.Retail.Integration.PIM.Models.Colors;
using WSA.Retail.Integration.PIM.Models.Images;

using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.PIM.Models.Products;

public interface IPimProductEntity : Integration.Core.IIdentifiable, INameable, PIM.Core.IAuditInfo, IIntegrationEntity
{
    public Guid? ColorId { get; set; }
    public bool? IsAvailable { get; set; }
    public bool? IsPhasedOut { get; set; }
    public decimal? ListPrice { get; set; }
    public string? ProductSource { get; set; }
    public string? ProductType { get; set; }
    public int? Ranking { get; set; }
    public DateTime? ReleaseDate { get; set; }
    public string? Sku { get; set; }
    public string? State { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public abstract PimColorEntity? PimColorEntity { get; set; }
    
    public abstract ICollection<PimProductAttributeEntity> Attributes { get; set; }
    public abstract ICollection<PimImageEntity> Images { get; set; }
    public abstract ICollection<PimProductEntity> ChildProducts { get; set; }
    public abstract ICollection<PimProductEntity> ParentProducts { get; set; }
    public abstract ICollection<PimBrandEntity> Brands { get; set; }
    public abstract ICollection<PimCategoryEntity> Categories { get; set; }
    public abstract ICollection<PimBundleSetEntity> BundleSets { get; set; }
}
