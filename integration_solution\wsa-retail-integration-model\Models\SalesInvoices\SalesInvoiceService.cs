﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Models.Claims;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.References;

namespace WSA.Retail.Integration.Models.SalesInvoices;

public class SalesInvoiceService(
    IOptions<AppSettings> options,
    ILogger<SalesInvoiceService> logger,
    IEventGridPublisher eventGridPublisher,
    ISalesInvoiceRepository repository,
    ICouplingService couplingService,
    IEntitySubscriberService entitySubscriberService,
    IClinicService clinicService,
    IPatientService patientService,
    IProductService productService,
    IClaimService claimService) : 
        DocumentServiceBase<SalesInvoice, SalesInvoiceLine, SalesInvoiceEntity, SalesInvoiceLineEntity>(
            appSettings: options.Value,
            logger: logger,
            repository: repository,
            mapper: null,
            entitySubscriberService: entitySubscriberService,
            couplingService: couplingService,
            eventGridPublisher: eventGridPublisher,
            entityType: EntityType.SalesInvoice),
        ISalesInvoiceService
{
    private readonly ILogger<SalesInvoiceService> _logger = logger;

    private readonly IClinicService _clinicService = clinicService;
    private readonly IPatientService _patientService = patientService;
    private readonly IProductService _productService = productService;
    private readonly IClaimService _claimService = claimService;

    public override async Task<IEnumerable<string>?> OnAfterInsertDomainModel(SalesInvoice domainModel)
    {
        var errors = new List<string>();
        if (domainModel.Claims.Count > 0)
        {
            foreach (var claim in domainModel.Claims)
            {
                claim.SalesInvoice ??= new();
                claim.SalesInvoice.Id = domainModel.Id;
                claim.SalesInvoice.DocumentNumber = domainModel.DocumentNumber;
                claim.SalesInvoice.ExternalReference = domainModel.ExternalReference;
                var response = await _claimService.UpsertAsync(claim);
                if (response == null)
                {
                    errors.Add($"Failed to upsert claim for SalesInvoice {domainModel.DocumentNumber}.");
                }
            }
        }
        if (errors.Count == 0) return errors;
        return default;
    }

    public override async Task<IEnumerable<string>?> OnAfterUpdateDomainModel(SalesInvoice domainModel)
    {
        var errors = new List<string>();
        if (domainModel.Claims.Count > 0)
        {
            foreach (var claim in domainModel.Claims)
            {
                claim.SalesInvoice ??= new();
                claim.SalesInvoice.Id = domainModel.Id;
                claim.SalesInvoice.DocumentNumber = domainModel.DocumentNumber;
                claim.SalesInvoice.ExternalReference = domainModel.ExternalReference;
                var response = await _claimService.UpsertAsync(claim);
                if (response == null)
                {
                    errors.Add($"Failed to upsert claim for SalesInvoice {domainModel.DocumentNumber}.");
                }
            }
        }
        if (errors.Count == 0) return errors;
        return default;
    }

    public async Task ValidateExternalReferencesAsync(string externalSystemCode, SalesInvoice salesInvoice)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        salesInvoice.Clinic = await _clinicService.ValidateExternalReferenceAsync(externalSystemCode, salesInvoice.Clinic);
        salesInvoice.Patient = await _patientService.ValidateExternalReferenceAsync(externalSystemCode, salesInvoice.Patient);
        foreach (var line in salesInvoice.Lines)
        {
            line.Product = await _productService.ValidateExternalReferenceAsync(externalSystemCode, line.Product);
        }
    }
}