﻿openapi: 3.0.1

info:
  title: v1.0
  description: API's to interact with PIM data elements
  version: '1.0'

servers:
  - url: 'https://retail-integration-we-dev.azure-api.net/pim/v1.0'
  
paths:
  /categories:
    post:
      tags:
        - categories
      summary: CategoryPost
      description: Post Category
      operationId: post-category
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/post-category-schema'
            example:
              id: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
              name: Signia
              parentCategoryId: null

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-category-schema'
              example:
                  id: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
                  name: Signia
                  parentCategoryId: null

components:
  schemas:
    post-category-schema:
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          maxLength: 100
          minLength: 1
          type: string
        parentCategoryId:
          type: string
          format: uuid