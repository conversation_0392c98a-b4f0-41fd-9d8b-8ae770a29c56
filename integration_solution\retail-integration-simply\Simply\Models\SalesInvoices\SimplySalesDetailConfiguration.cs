using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WSA.Retail.Integration.Simply.Models.SalesInvoices;

public class SimplySalesDetailConfiguration : IEntityTypeConfiguration<SimplySalesDetailEntity>
{
    public void Configure(EntityTypeBuilder<SimplySalesDetailEntity> builder)
    {
        builder.ToTable("SalesDetail");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.SalesInvoiceId)
            .HasColumnName("SalesInvoiceId")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        builder.Property(x => x.SalesType)
            .HasColumnName("SalesType")
            .HasColumnType("nvarchar(10)")
            .IsRequired(true);

        builder.Property(x => x.InvoiceNumber)
            .HasColumnName("InvoiceNumber")
            .HasColumnType("nvarchar(20)")
            .IsRequired(true);

        builder.Property(x => x.Line)
            .HasColumnName("Line")
            .HasColumnType("int")
            .IsRequired(true);

        builder.Property(x => x.ProductNumber)
            .HasColumnName("ProductNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.Quantity)
            .HasColumnName("Quantity")
            .HasColumnType("int")
            .IsRequired(false);

        builder.Property(x => x.UnitPrice)
            .HasColumnName("UnitPrice")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.Amount)
            .HasColumnName("Amount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.GST)
            .HasColumnName("GST")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.OHSBenefitAmount)
            .HasColumnName("OHSBenefitAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.OHSTopUpAmount)
            .HasColumnName("OHSTopUpAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.OHSBenefitAmountInclGST)
            .HasColumnName("OHSBenefitAmountInclGST")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.OHSTopUpAmountInclGST)
            .HasColumnName("OHSTopUpAmountInclGST")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.NonStock)
            .HasColumnName("NonStock")
            .HasColumnType("bit")
            .IsRequired(false);

        builder.Property(x => x.Gross)
            .HasColumnName("Gross")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.DiscountAmount)
            .HasColumnName("DiscountAmount")
            .HasColumnType("decimal(18,2)")
            .IsRequired(false);

        builder.Property(x => x.SerialNumber)
            .HasColumnName("SerialNumber")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        builder.Property(x => x.WarrantyExpireDate)
            .HasColumnName("WarrantyExpireDate")
            .HasColumnType("datetime2(7)")
            .IsRequired(false);

        builder.Property(x => x.FileName)
            .HasColumnName("FileName")
            .HasColumnType("nvarchar(255)")
            .IsRequired(false);

        builder.Property(x => x.CreatedOn)
            .HasColumnName("CreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ModifiedOn)
            .HasColumnName("ModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        // Set up foreign key relationship
        builder.HasOne(x => x.Header)
            .WithMany(h => h.Details)
            .HasForeignKey(x => x.SalesInvoiceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}