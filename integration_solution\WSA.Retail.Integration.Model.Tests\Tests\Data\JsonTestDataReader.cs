﻿using System.Text.Json;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Tests.Data;

public static class JsonTestDataReader
{
    public static IEnumerable<T> ReadArray<T>(string fileName, string propertyName)
    {
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "Tests", "Data", "TestData", fileName);
        var json = File.ReadAllText(filePath);

        using var document = JsonDocument.Parse(json);
        if (!document.RootElement.TryGetProperty(propertyName, out var arrayElement))
        {
            return [];
        }

        var items = JsonSerializer.Deserialize<List<T>>(arrayElement.GetRawText(), Common.GetJsonOptions());
        return items ?? [];
    }
}