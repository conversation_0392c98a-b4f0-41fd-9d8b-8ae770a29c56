﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Data;

namespace WSA.Retail.Integration.Models.Manufacturers;

public class ManufacturerEntityConfiguration : IEntityTypeConfiguration<ManufacturerEntity>
{
    public void Configure(EntityTypeBuilder<ManufacturerEntity> builder)
    {
        builder.ToTable("Manufacturer");
        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.ConfigureMasterDataFields();
        builder.ConfigureAuditInfoFields();
    }
}