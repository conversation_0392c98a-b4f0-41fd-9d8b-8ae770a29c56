﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Brands;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public interface IPimProductBrandEntity : IIdentifiable, IAuditInfo
    {
        public Guid ProductId { get; set; }
        public Guid BrandId { get; set; }

        public abstract PimProductEntity ProductEntity { get; set; }
        public abstract PimBrandEntity BrandEntity { get; set; }
    }
}
