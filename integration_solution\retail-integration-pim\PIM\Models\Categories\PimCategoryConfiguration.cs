using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;


namespace WSA.Retail.Integration.PIM.Models.Categories
{
    public class PimCategoryConfiguration : IEntityTypeConfiguration<PimCategoryEntity>
    {
        public void Configure(EntityTypeBuilder<PimCategoryEntity> builder)
        {
            builder.ToTable("Category", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.ParentCategoryId)
                .HasColumnName("ParentCategoryId")
                .HasColumnType("uniqueidentifier");

            builder.HasOne(x => x.ParentCategory)
                .WithMany()
                .HasForeignKey(x => x.ParentCategoryId);
        }
    }
}